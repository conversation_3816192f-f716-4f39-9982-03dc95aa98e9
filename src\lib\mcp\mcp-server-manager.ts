/**
 * MCP Server Manager
 * 
 * This module provides functionality to start and stop MCP servers.
 */

import { mcpServers, mcpServerPorts, getMCPServerUrl } from './mcp-config';

interface MCPServerProcess {
  id: string;
  process: any; // Child process
  url: string;
  running: boolean;
}

/**
 * MCP Server Manager class
 */
export class MCPServerManager {
  private static instance: MCPServerManager;
  private servers: Map<string, MCPServerProcess> = new Map();

  /**
   * Get the singleton instance of the MCP Server Manager
   */
  public static getInstance(): MCPServerManager {
    if (!MCPServerManager.instance) {
      MCPServerManager.instance = new MCPServerManager();
    }
    return MCPServerManager.instance;
  }

  /**
   * Start an MCP server
   */
  public async startServer(serverId: string): Promise<string> {
    // Check if server is already running
    if (this.servers.has(serverId) && this.servers.get(serverId)!.running) {
      return this.servers.get(serverId)!.url;
    }

    // Check if server config exists
    if (!mcpServers[serverId]) {
      throw new Error(`MCP server configuration not found for: ${serverId}`);
    }

    const serverConfig = mcpServers[serverId];
    const port = mcpServerPorts[serverId] || 3100;
    const url = getMCPServerUrl(serverId);

    try {
      console.log(`Starting MCP server: ${serverId} on port ${port}`);

      // In a browser environment, we can't directly spawn processes
      // We'll use a different approach to start the server
      // This could be via a backend API call or using a service worker
      
      // For now, we'll just simulate the server being started
      // In a real implementation, you would need to start the server via a backend API
      
      // Create a simulated server process
      const serverProcess: MCPServerProcess = {
        id: serverId,
        process: null, // In a real implementation, this would be the child process
        url,
        running: true
      };

      // Store the server process
      this.servers.set(serverId, serverProcess);

      // In a real implementation, you would wait for the server to start
      // For now, we'll just return the URL
      console.log(`MCP server started: ${serverId} at ${url}`);
      return url;
    } catch (error) {
      console.error(`Error starting MCP server ${serverId}:`, error);
      throw error;
    }
  }

  /**
   * Stop an MCP server
   */
  public async stopServer(serverId: string): Promise<void> {
    // Check if server is running
    if (!this.servers.has(serverId) || !this.servers.get(serverId)!.running) {
      console.log(`MCP server not running: ${serverId}`);
      return;
    }

    try {
      const serverProcess = this.servers.get(serverId)!;
      
      // In a real implementation, you would stop the server process
      // For now, we'll just simulate the server being stopped
      
      // Mark the server as not running
      serverProcess.running = false;
      
      console.log(`MCP server stopped: ${serverId}`);
    } catch (error) {
      console.error(`Error stopping MCP server ${serverId}:`, error);
      throw error;
    }
  }

  /**
   * Stop all running MCP servers
   */
  public async stopAllServers(): Promise<void> {
    for (const serverId of this.servers.keys()) {
      if (this.servers.get(serverId)!.running) {
        await this.stopServer(serverId);
      }
    }
  }

  /**
   * Check if an MCP server is running
   */
  public isServerRunning(serverId: string): boolean {
    return this.servers.has(serverId) && this.servers.get(serverId)!.running;
  }

  /**
   * Get the URL for a running MCP server
   */
  public getServerUrl(serverId: string): string | null {
    if (this.isServerRunning(serverId)) {
      return this.servers.get(serverId)!.url;
    }
    return null;
  }

  /**
   * Get all running MCP servers
   */
  public getRunningServers(): string[] {
    return Array.from(this.servers.entries())
      .filter(([_, server]) => server.running)
      .map(([id, _]) => id);
  }
}
