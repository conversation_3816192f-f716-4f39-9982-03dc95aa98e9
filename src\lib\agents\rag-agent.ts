/**
 * RAG Agent
 *
 * This module provides a Retrieval-Augmented Generation agent that can assist other agents
 * by providing company-specific information from the knowledge base.
 */

import { KnowledgeBaseService } from '../knowledge/knowledge-base';
import { PineconeService } from '../knowledge/pinecone-service';
import { AgentSettings } from '../../types';

/**
 * RAG Agent class
 */
export class RAGAgent {
  private knowledgeBase: KnowledgeBaseService;
  private config: AgentSettings & { apiKey?: string };

  /**
   * Create a new RAG Agent
   */
  constructor(config: AgentSettings, pineconeService: PineconeService) {
    this.config = {
      ...config,
      apiKey: import.meta.env.VITE_OPENROUTER_API_KEY
    };
    this.knowledgeBase = new KnowledgeBaseService(pineconeService);
  }

  /**
   * Initialize the RAG Agent
   */
  async initialize(): Promise<void> {
    try {
      await this.knowledgeBase.initialize();
    } catch (error) {
      console.error('Error initializing RAG Agent:', error);
    }
  }

  /**
   * Process a message with the RAG Agent
   */
  async processMessage(message: string): Promise<string> {
    try {
      console.log(`RAG Agent processing message: "${message}"`);

      // For debugging, log the message type
      const isQuery = this.isDirectQuery(message);
      console.log(`Message identified as: ${isQuery ? 'direct query' : 'assistance request'}`);

      // Always treat messages as direct queries for now to ensure we search the knowledge base
      const response = await this.handleDirectQuery(message);

      // Log the response for debugging
      console.log(`RAG Agent response length: ${response.length} characters`);

      // If we got a meaningful response, return it
      if (response.length > 100 && !response.includes("I couldn't find any relevant information")) {
        return response;
      }

      // If direct query didn't yield good results, try assistance approach
      const assistanceResponse = await this.provideAssistance(message);

      // Return the better response
      if (assistanceResponse.length > response.length) {
        return assistanceResponse;
      }

      return response;
    } catch (error) {
      console.error('Error processing message with RAG Agent:', error);
      return `I encountered an error while retrieving information: ${error instanceof Error ? error.message : String(error)}`;
    }
  }

  /**
   * Determine if a message is a direct query
   */
  private isDirectQuery(message: string): boolean {
    const directQueryIndicators = [
      'what is',
      'tell me about',
      'information on',
      'details about',
      'describe',
      'who is',
      'where is',
      'when was',
      'how does',
      'explain',
      'find',
      'search for',
      'lookup',
      '?'
    ];

    const messageLower = message.toLowerCase();

    return directQueryIndicators.some(indicator =>
      messageLower.includes(indicator.toLowerCase())
    );
  }

  /**
   * Handle a direct query to the knowledge base
   */
  private async handleDirectQuery(query: string): Promise<string> {
    console.log(`Searching knowledge base for: "${query}"`);

    try {
      // Search the knowledge base with more results
      const searchResults = await this.knowledgeBase.search(query, 5);

      console.log(`Search results: ${searchResults.length > 0 ? 'found' : 'none'}`);

      if (!searchResults.trim()) {
        // If no results, provide a helpful response
        return this.generateNoResultsResponse(query);
      }

      // First, collect the raw information
      const rawInfo = this.extractRawInformation(searchResults);

      // Then, use a general agent to format the response nicely
      const formattedResponse = await this.formatResponseWithGeneralAgent(query, rawInfo);

      // If we got a formatted response, return it
      if (formattedResponse && formattedResponse.trim().length > 0) {
        return formattedResponse;
      }

      // Fallback to the old formatting if the general agent fails
      let response = "Based on the information I have about your company:\n\n";

      // Extract key information from the search results
      const lines = searchResults.split('\n');
      let currentSource = '';
      let content = [];

      for (const line of lines) {
        if (line.startsWith('Source')) {
          // If we have content from a previous source, add it to the response
          if (currentSource && content.length > 0) {
            response += `${currentSource}:\n${content.join('\n')}\n\n`;
          }

          // Start a new source
          currentSource = line.replace('Source', 'From').replace(':', '');
          content = [];
        } else if (line.startsWith('Content:')) {
          // Add the content without the "Content:" prefix
          content.push(line.replace('Content:', '').trim());
        } else if (!line.startsWith('URL:') && !line.startsWith('Type:') && !line.startsWith('Relevance:') && line.trim()) {
          // Add any other non-empty lines that aren't metadata
          content.push(line.trim());
        }
      }

      // Add the last source
      if (currentSource && content.length > 0) {
        response += `${currentSource}:\n${content.join('\n')}\n\n`;
      }

      return response;
    } catch (error) {
      console.error('Error in handleDirectQuery:', error);
      return this.generateErrorResponse(query, error);
    }
  }

  /**
   * Extract raw information from search results
   */
  private extractRawInformation(searchResults: string): any {
    const sources: any[] = [];
    const lines = searchResults.split('\n');
    let currentSource: any = null;

    for (const line of lines) {
      if (line.startsWith('Source')) {
        // If we have a previous source, add it to the sources array
        if (currentSource) {
          sources.push(currentSource);
        }

        // Start a new source
        const sourceName = line.replace('Source', '').replace(':', '').trim();
        currentSource = {
          name: sourceName,
          content: '',
          url: ''
        };
      } else if (line.startsWith('URL:') && currentSource) {
        currentSource.url = line.replace('URL:', '').trim();
      } else if (line.startsWith('Content:') && currentSource) {
        currentSource.content += line.replace('Content:', '').trim() + ' ';
      } else if (!line.startsWith('Type:') && !line.startsWith('Relevance:') && line.trim() && currentSource) {
        currentSource.content += line.trim() + ' ';
      }
    }

    // Add the last source
    if (currentSource) {
      sources.push(currentSource);
    }

    return sources;
  }

  /**
   * Format the response using a general agent
   */
  private async formatResponseWithGeneralAgent(query: string, rawInfo: any[]): Promise<string> {
    try {
      // Create a prompt for the general agent
      const prompt = `
You are an AI assistant for Phoenix Roofing and Repair, a roofing company.
Based on the following information from our knowledge base, please provide a helpful,
conversational response to the user's query: "${query}"

Here is the information from our knowledge base:
${JSON.stringify(rawInfo, null, 2)}

Please format your response in a natural, conversational way. Include all relevant information
but organize it logically. Don't mention that you're using a knowledge base or reference the
sources directly in your response. Just provide the information as if you're a knowledgeable
representative of the company.
`;

      // Use the OpenAI API to generate a response
      const apiKey = this.config.apiKey || import.meta.env.VITE_OPENROUTER_API_KEY;
      if (!apiKey) {
        console.error('No API key available for general agent');
        return '';
      }

      // Make the API call
      const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${apiKey}`,
          'HTTP-Referer': window.location.href,
          'X-Title': 'Phoenix Roofing and Repair'
        },
        body: JSON.stringify({
          model: this.config.model || 'anthropic/claude-3-7-sonnet',
          messages: [
            { role: 'user', content: prompt }
          ],
          temperature: 0.7,
          max_tokens: 1000
        })
      });

      if (!response.ok) {
        console.error('Error from general agent API:', await response.text());
        return '';
      }

      const data = await response.json();
      return data.choices[0].message.content;
    } catch (error) {
      console.error('Error formatting response with general agent:', error);
      return '';
    }
  }

  /**
   * Generate a response for when no results are found
   */
  private generateNoResultsResponse(query: string): string {
    const responses = [
      "I couldn't find any specific information about that in my knowledge base. Would you like to add some information about your company?",
      "I don't have any data on that topic yet. You can add information in the Settings page under the Knowledge Base tab.",
      "I don't have enough information to answer that question. Try scraping your company website in the Settings page to add more data.",
      "I don't have any relevant information about that in my knowledge base. Would you like to know how to add more information?",
      "I don't have specific details about that yet. You can enhance my knowledge by adding company information or scraping your website."
    ];

    return responses[Math.floor(Math.random() * responses.length)];
  }

  /**
   * Generate a response for when an error occurs
   */
  private generateErrorResponse(query: string, error: any): string {
    // Check if it's a connection error
    if (error.message && error.message.includes('Not connected to Pinecone')) {
      return "I'm having trouble connecting to the knowledge base. Please check your Pinecone settings in the Settings page and make sure you've entered your API key, environment, and index name.";
    }

    // Generic error response
    return "I encountered an error while searching for information. Please check your knowledge base settings and try again.";
  }

  /**
   * Provide assistance to another agent
   */
  private async provideAssistance(request: string): Promise<string> {
    // Extract the information needs from the request
    const informationNeeds = this.extractInformationNeeds(request);

    // Search for each information need
    const results: Record<string, string> = {};

    for (const [key, query] of Object.entries(informationNeeds)) {
      const searchResults = await this.knowledgeBase.search(query, 2);
      results[key] = searchResults.trim() || 'No information found';
    }

    // Format the response
    let response = `Here's the information I found that might help:\n\n`;

    for (const [key, result] of Object.entries(results)) {
      response += `## ${key}\n${result}\n\n`;
    }

    return response;
  }

  /**
   * Extract information needs from a request
   */
  private extractInformationNeeds(request: string): Record<string, string> {
    // This is a simplified version - in a real implementation, this would use
    // an LLM to extract specific information needs from the request

    const needs: Record<string, string> = {
      'Company Overview': 'company description overview',
    };

    const requestLower = request.toLowerCase();

    // Check for specific information needs
    if (requestLower.includes('contact') || requestLower.includes('phone') || requestLower.includes('email') || requestLower.includes('address')) {
      needs['Contact Information'] = 'company contact information phone email address';
    }

    if (requestLower.includes('product') || requestLower.includes('service') || requestLower.includes('offering')) {
      needs['Products and Services'] = 'company products services offerings';
    }

    if (requestLower.includes('target') || requestLower.includes('audience') || requestLower.includes('customer') || requestLower.includes('client')) {
      needs['Target Audience'] = 'company target audience customers clients';
    }

    if (requestLower.includes('value') || requestLower.includes('mission') || requestLower.includes('vision')) {
      needs['Company Values'] = 'company values mission vision statement';
    }

    if (requestLower.includes('unique') || requestLower.includes('usp') || requestLower.includes('selling') || requestLower.includes('proposition')) {
      needs['Unique Selling Proposition'] = 'company unique selling proposition USP differentiator';
    }

    return needs;
  }

  /**
   * Add company information to the knowledge base
   */
  async addCompanyInfo(companyInfo: any): Promise<void> {
    await this.knowledgeBase.addCompanyInfo(companyInfo);
  }

  /**
   * Scrape a website and add it to the knowledge base
   */
  async scrapeWebsite(url: string, options?: any): Promise<number> {
    return await this.knowledgeBase.scrapeAndUpsert(url, options);
  }
}
