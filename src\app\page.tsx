"use client";

import React, { useState } from "react";
import { CopilotContext } from "../components/CopilotContext";
import { CopilotActions } from "../components/CopilotActions";
import { VoiceAgentCopilot } from "../components/VoiceAgentCopilot";
import { AgentChat } from "../components/AgentChat";
import Link from "next/link";

export default function HomePage() {
  const [appointments, setAppointments] = useState<any[]>([]);
  const [contactSubmissions, setContactSubmissions] = useState<any[]>([]);
  const [estimateRequests, setEstimateRequests] = useState<any[]>([]);

  // Company information
  const companyInfo = {
    name: "Phoenix Roofing and Repair",
    address: "532 E. Maryland Suite F, Phoenix, AZ 85012",
    phone: "(602) 837-ROOF (7663)",
    email: "<EMAIL>",
    hours: {
      monday: "8:00 AM - 5:00 PM",
      tuesday: "8:00 AM - 5:00 PM",
      wednesday: "8:00 AM - 5:00 PM",
      thursday: "8:00 AM - 5:00 PM",
      friday: "8:00 AM - 5:00 PM",
      saturday: "9:00 AM - 2:00 PM",
      sunday: "Closed",
    },
  };

  // Services
  const services = [
    {
      id: "roof-repair",
      name: "Roof Repair",
      description: "Professional roof repair services for all types of roofs.",
      priceRange: "$500 - $2,000",
    },
    {
      id: "roof-replacement",
      name: "Roof Replacement",
      description: "Complete roof replacement services with quality materials.",
      priceRange: "$8,000 - $20,000",
    },
    {
      id: "roof-inspection",
      name: "Roof Inspection",
      description: "Thorough roof inspection to identify issues and provide recommendations.",
      priceRange: "FREE Estimates",
    },
    {
      id: "emergency-repairs",
      name: "Emergency Repairs",
      description: "24/7 emergency roof repair services for urgent situations.",
      priceRange: "Varies",
    },
  ];

  // Handle scheduling an appointment
  const handleScheduleAppointment = async (appointmentData: any) => {
    // In a real implementation, you would call an API to schedule the appointment
    setAppointments([...appointments, { ...appointmentData, id: Date.now() }]);
  };

  // Handle submitting a contact form
  const handleSubmitContactForm = async (formData: any) => {
    // In a real implementation, you would call an API to submit the form
    setContactSubmissions([...contactSubmissions, { ...formData, id: Date.now() }]);
  };

  // Handle requesting an estimate
  const handleRequestEstimate = async (estimateData: any) => {
    // In a real implementation, you would call an API to request the estimate
    setEstimateRequests([...estimateRequests, { ...estimateData, id: Date.now() }]);
  };

  return (
    <CopilotContext
      companyInfo={companyInfo}
      services={services}
    >
      <CopilotActions
        onScheduleAppointment={handleScheduleAppointment}
        onSubmitContactForm={handleSubmitContactForm}
        onRequestEstimate={handleRequestEstimate}
      >
        <VoiceAgentCopilot />

        <div className="container mx-auto px-4 py-8">
          <header className="mb-8">
            <h1 className="text-3xl font-bold text-center mb-2">Phoenix Roofing and Repair</h1>
            <p className="text-center text-gray-600 mb-4">Quality Roofing Services in Phoenix, AZ</p>
            <div className="text-center">
              <p className="font-semibold">{companyInfo.address}</p>
              <p className="font-semibold">{companyInfo.phone}</p>
            </div>
          </header>

          <section className="mb-12">
            <h2 className="text-2xl font-bold mb-4">Our Services</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {services.map((service) => (
                <div key={service.id} className="bg-white rounded-lg shadow p-6">
                  <h3 className="text-xl font-semibold mb-2">{service.name}</h3>
                  <p className="text-gray-600 mb-4">{service.description}</p>
                  <p className="font-medium">Price Range: {service.priceRange}</p>
                </div>
              ))}
            </div>
          </section>

          <section className="mb-12">
            <h2 className="text-2xl font-bold mb-4">Recent Appointments</h2>
            {appointments.length === 0 ? (
              <p className="text-gray-500">No appointments scheduled yet.</p>
            ) : (
              <div className="bg-white rounded-lg shadow overflow-hidden">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Name
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Service
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Date
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Time
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {appointments.map((appointment) => (
                      <tr key={appointment.id}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {appointment.customerName}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {appointment.serviceType}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {appointment.preferredDate}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {appointment.preferredTime}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </section>

          <div className="text-center mb-8">
            <Link href="/settings/copilot">
              <span className="inline-block px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
                Copilot Settings
              </span>
            </Link>
          </div>

          <section className="mb-12">
            <h2 className="text-2xl font-bold mb-4">Agent Chat</h2>
            <div className="bg-white rounded-lg shadow overflow-hidden" style={{ height: "500px" }}>
              <AgentChat />
            </div>
          </section>
        </div>
      </CopilotActions>
    </CopilotContext>
  );
}
