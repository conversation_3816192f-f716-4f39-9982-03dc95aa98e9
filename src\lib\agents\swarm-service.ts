/**
 * Swarm Service
 *
 * This module provides a service for initializing and managing the LangGraph Swarm.
 */

import { SwarmOrchestrator } from './swarm-orchestrator';
import { AgentSettings } from '../../types';

// Singleton instance of the swarm orchestrator
let swarmOrchestrator: SwarmOrchestrator | null = null;

/**
 * Initialize the swarm service with the provided agent configurations
 */
export async function initializeSwarmService(
  agentConfigs: AgentSettings[],
  options?: {
    allowCollaboration?: boolean;
    showReasoning?: boolean;
    pineconeApiKey?: string;
    pineconeEnvironment?: string;
    pineconeIndex?: string;
    pineconeHost?: string;
    pineconeMetric?: string;
    pineconeDimensions?: number;
    embeddingModel?: string;
  }
): Promise<void> {
  if (!swarmOrchestrator) {
    swarmOrchestrator = new SwarmOrchestrator(agentConfigs, options);
  } else {
    // Re-create the orchestrator with new configs
    swarmOrchestrator = new SwarmOrchestrator(agentConfigs, options);
  }

  await swarmOrchestrator.initialize();
}

/**
 * Get the swarm orchestrator instance
 */
export function getSwarmOrchestrator(): SwarmOrchestrator {
  if (!swarmOrchestrator) {
    throw new Error('Swarm service not initialized');
  }

  return swarmOrchestrator;
}

/**
 * Process a message using the swarm
 */
export async function processSwarmMessage(message: string): Promise<string> {
  if (!swarmOrchestrator) {
    throw new Error('Swarm service not initialized');
  }

  // Create a new task
  const taskId = swarmOrchestrator.createTask(message);

  // Process the task
  return swarmOrchestrator.processTask(taskId);
}

/**
 * Check if the swarm service is initialized
 */
export function isSwarmInitialized(): boolean {
  return swarmOrchestrator !== null;
}
