const express = require('express');
const cors = require('cors');

const app = express();
app.use(cors());
app.use(express.json());

// API endpoints
app.get('/api/pinecone/status', (req, res) => {
  res.json({
    success: true,
    connected: true,
    indexExists: true,
    config: {
      environment: 'us-east-1',
      index: 'roofermax-ciouxjl',
      host: 'https://roofermax-ciouxjl.svc.aped-4627-b74a.pinecone.io',
      dimensions: 3072,
      metric: 'cosine',
      embeddingModel: 'text-embedding-3-large'
    }
  });
});

app.post('/api/pinecone/upsert', (req, res) => {
  res.json({ success: true, count: req.body.documents?.length || 0 });
});

app.post('/api/pinecone/search', (req, res) => {
  const { query, k = 5 } = req.body;
  
  // Generate mock results
  const results = [];
  for (let i = 0; i < k; i++) {
    results.push({
      pageContent: `Mock result for query: "${query}". This is result #${i + 1}.`,
      metadata: {
        source: 'mock_data',
        title: `Mock Result ${i + 1}`,
        url: `https://example.com/result-${i + 1}`,
        timestamp: new Date().toISOString()
      }
    });
  }
  
  res.json({ success: true, results });
});

app.delete('/api/pinecone/namespace', (req, res) => {
  res.json({ success: true });
});

app.post('/api/pinecone/create-index', (req, res) => {
  res.json({ success: true, created: true });
});

// Start the server
const PORT = process.env.PORT || 3001;
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});
