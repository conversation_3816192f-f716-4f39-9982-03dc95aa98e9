import os
import requests
from typing import Dict, List, Any, Optional

class Context7Client:
    """Client for interacting with Context7 API."""
    
    def __init__(self, api_key: Optional[str] = None):
        """Initialize the Context7 client."""
        self.api_key = api_key or os.environ.get("CONTEXT7_API_KEY")
        if not self.api_key:
            raise ValueError("Context7 API key is required")
        
        self.base_url = "https://api.context7.com/v1"
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
    
    def get_context(self, query: str, library_id: str, max_tokens: int = 5000) -> Dict[str, Any]:
        """Get context from Context7 for a given query and library."""
        url = f"{self.base_url}/context"
        
        payload = {
            "query": query,
            "library_id": library_id,
            "max_tokens": max_tokens
        }
        
        response = requests.post(url, headers=self.headers, json=payload)
        response.raise_for_status()
        
        return response.json()
    
    def list_libraries(self) -> List[Dict[str, Any]]:
        """List available libraries in Context7."""
        url = f"{self.base_url}/libraries"
        
        response = requests.get(url, headers=self.headers)
        response.raise_for_status()
        
        return response.json()["libraries"]
    
    def get_library(self, library_id: str) -> Dict[str, Any]:
        """Get details about a specific library."""
        url = f"{self.base_url}/libraries/{library_id}"
        
        response = requests.get(url, headers=self.headers)
        response.raise_for_status()
        
        return response.json()

# Function to enhance agent context with Context7
def enhance_context(query: str, library_id: str, api_key: Optional[str] = None) -> Dict[str, Any]:
    """Enhance the agent's context with information from Context7."""
    client = Context7Client(api_key)
    return client.get_context(query, library_id)

# Function to integrate Context7 with LangGraph agent
def add_context7_to_agent_state(state: Dict[str, Any], query: str, library_id: str) -> Dict[str, Any]:
    """Add Context7 information to the agent state."""
    try:
        context = enhance_context(query, library_id)
        
        # Update the state with the context
        updated_state = state.copy()
        if "context" not in updated_state:
            updated_state["context"] = {}
        
        updated_state["context"]["context7"] = context
        
        return updated_state
    except Exception as e:
        print(f"Error enhancing context with Context7: {e}")
        return state
