import OpenAI from 'openai';

// Define the available models from OpenRouter
export const OPENROUTER_MODELS = {
  'anthropic/claude-3-7-sonnet': 'anthropic/claude-3.7-sonnet',
  'anthropic/claude-3-5-sonnet': 'anthropic/claude-3.5-sonnet',
  'anthropic/claude-3-opus': 'anthropic/claude-3-opus',
  'openai/gpt-4o': 'openai/gpt-4o',
  'openai/gpt-4-turbo': 'openai/gpt-4-turbo',
  'mistral/mistral-large': 'mistralai/mistral-large',
  'mistral/mixtral-8x7b': 'mistralai/mixtral-8x7b',
  'meta/llama-3-70b': 'meta-llama/llama-3-70b-instruct',
  'meta/llama-3-8b': 'meta-llama/llama-3-8b-instruct',
  'google/gemini-pro': 'google/gemini-pro',
};

// Default model
export const DEFAULT_MODEL = 'anthropic/claude-3-7-sonnet';

let openRouterClient: OpenAI | null = null;

const getApiKey = () => {
  const key = import.meta.env.VITE_OPENROUTER_API_KEY;
  if (!key) {
    throw new Error(
      'OpenRouter API key not found. Please add your API key to .env file:\n' +
      'VITE_OPENROUTER_API_KEY=your_api_key_here'
    );
  }
  return key;
};

export const initializeOpenRouter = (model = DEFAULT_MODEL) => {
  if (!openRouterClient) {
    try {
      openRouterClient = new OpenAI({
        apiKey: getApiKey(),
        baseURL: 'https://openrouter.ai/api/v1',
        defaultHeaders: {
          'HTTP-Referer': window.location.origin,
          'X-Title': 'AG3NT',
        },
        dangerouslyAllowBrowser: true // Note: In production, API calls should be made through a backend
      });
    } catch (error) {
      console.error('Failed to initialize OpenRouter client:', error);
      throw error;
    }
  }
  return openRouterClient;
};

export interface Message {
  role: 'user' | 'assistant';
  content: string | MessageContent[];
}

interface MessageContent {
  type: 'text' | 'image_url' | 'latex' | 'file_attachment';
  text?: string;
  image_url?: { url: string };
  latex?: string;
  file_attachment?: {
    name: string;
    type: string;
    size: number;
  };
}

export async function getChatCompletion(messages: Message[], model = DEFAULT_MODEL) {
  try {
    const client = initializeOpenRouter();
    if (!client) {
      throw new Error('OpenRouter client not initialized');
    }

    const completion = await client.chat.completions.create({
      model: OPENROUTER_MODELS[model as keyof typeof OPENROUTER_MODELS] || model,
      messages: messages.map(msg => ({
        role: msg.role,
        content: Array.isArray(msg.content) ? msg.content : [{ type: 'text', text: msg.content }]
      })),
      max_tokens: 2048,
      temperature: 0.7,
      stream: true
    });

    return completion;
  } catch (error) {
    console.error('Error getting chat completion:', error);
    throw error;
  }
}

export async function analyzeImage(imageUrl: string, prompt: string, model = DEFAULT_MODEL) {
  try {
    const client = initializeOpenRouter();
    if (!client) {
      throw new Error('OpenRouter client not initialized');
    }

    // Prepare the image URL - handle both http/https URLs and data URLs
    const imageContent = imageUrl.startsWith('data:')
      ? { url: imageUrl }
      : { url: imageUrl };

    console.log('Analyzing image with model:', model);

    const response = await client.chat.completions.create({
      model: OPENROUTER_MODELS[model as keyof typeof OPENROUTER_MODELS] || model,
      messages: [
        {
          role: "user",
          content: [
            { type: "text", text: prompt },
            { type: "image_url", image_url: imageContent }
          ]
        }
      ],
      max_tokens: 2048
    });

    return response.choices[0].message.content;
  } catch (error) {
    console.error('Error analyzing image:', error);
    throw error;
  }
}

export async function processFile(file: File, prompt: string, model = DEFAULT_MODEL) {
  try {
    const client = initializeOpenRouter();
    if (!client) {
      throw new Error('OpenRouter client not initialized');
    }

    // For image files, we can use the analyzeImage function
    if (file.type.startsWith('image/')) {
      // Convert the file to a base64 data URL that can be sent to the API
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = async (event) => {
          try {
            if (event.target?.result) {
              const dataUrl = event.target.result as string;
              const response = await analyzeImage(dataUrl, prompt, model);
              resolve(response);
            } else {
              reject(new Error('Failed to read image file'));
            }
          } catch (error) {
            reject(error);
          }
        };
        reader.onerror = () => reject(new Error('Failed to read image file'));
        reader.readAsDataURL(file);
      });
    }

    // For text files, we can read the content and include it in the prompt
    if (file.type.startsWith('text/') ||
        file.type === 'application/json' ||
        file.type === 'application/xml' ||
        file.name.endsWith('.md') ||
        file.name.endsWith('.txt') ||
        file.name.endsWith('.json') ||
        file.name.endsWith('.xml') ||
        file.name.endsWith('.csv')) {

      const text = await file.text();
      const fileContent = `File content of ${file.name}:\n\n${text}`;

      const response = await client.chat.completions.create({
        model: OPENROUTER_MODELS[model as keyof typeof OPENROUTER_MODELS] || model,
        messages: [
          {
            role: "user",
            content: [
              { type: "text", text: prompt },
              { type: "text", text: fileContent }
            ]
          }
        ],
        max_tokens: 2048
      });

      return response.choices[0].message.content;
    }

    // For other file types, we just include the file information
    const fileInfo = `File: ${file.name} (${file.type}, ${(file.size / 1024).toFixed(2)} KB)`;

    const response = await client.chat.completions.create({
      model: OPENROUTER_MODELS[model as keyof typeof OPENROUTER_MODELS] || model,
      messages: [
        {
          role: "user",
          content: [
            { type: "text", text: prompt },
            { type: "text", text: fileInfo }
          ]
        }
      ],
      max_tokens: 2048
    });

    return response.choices[0].message.content;
  } catch (error) {
    console.error('Error processing file:', error);
    throw error;
  }
}
