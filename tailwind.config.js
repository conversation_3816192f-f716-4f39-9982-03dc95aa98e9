/** @type {import('tailwindcss').Config} */
export default {
  content: ['./index.html', './src/**/*.{js,ts,jsx,tsx}'],
  theme: {
    extend: {
      colors: {
        'neon-green': '#00ff94',
        'electric-blue': '#00d1ff',
        'base-black': '#000000',
        'rich-dark': '#13111C',
        'warning': '#ffd700',
        'error': '#ff4444',
      },
      fontSize: {
        'hero': '3.5rem',
        'micro': '0.75rem',
      },
      animation: {
        'glow': 'glow 1.5s ease-in-out infinite alternate',
      },
      keyframes: {
        glow: {
          'from': { 'box-shadow': '0 0 10px #00ff94, 0 0 20px #00ff94, 0 0 30px #00ff94' },
          'to': { 'box-shadow': '0 0 20px #00ff94, 0 0 30px #00ff94, 0 0 40px #00ff94' }
        }
      }
    },
  },
  plugins: [],
};