/**
 * Voice Agent Service
 * 
 * This module provides a service for managing voice agents.
 * This is a placeholder implementation until the full agent system is complete.
 */

import { AgentSettings } from '../../types';
import { VoiceAgent } from './voice-agent';
import { VapiService } from '../integrations/vapi-service';

// Voice agent instance
let voiceAgent: VoiceAgent | null = null;

// VAPI service instance
let vapiService: VapiService | null = null;

/**
 * Initialize the voice agent service
 */
export async function initializeVoiceAgentService(agentConfig: AgentSettings): Promise<void> {
  try {
    // Ensure the agent config has the correct MCP server ID
    const enhancedConfig: AgentSettings = {
      ...agentConfig,
      mcpServerId: 'vapi', // Explicitly set VAPI as the MCP server
      systemPrompt: enhanceSystemPrompt(agentConfig.systemPrompt)
    };
    
    console.log('Initializing Voice Agent Service with VAPI integration');
    
    // Create and initialize voice agent with enhanced config
    voiceAgent = new VoiceAgent(enhancedConfig);
    
    // Initialize the voice agent
    await voiceAgent.initialize();
    
    console.log('Voice Agent Service initialized successfully');
  } catch (error) {
    console.error('Error initializing Voice Agent Service:', error);
    throw error; // Re-throw to allow proper error handling
  }
}

/**
 * Enhance the system prompt with MCP tool usage instructions
 */
function enhanceSystemPrompt(originalPrompt: string): string {
  // Add explicit instructions about using VAPI tools
  return `${originalPrompt}

IMPORTANT: You MUST use the VAPI tools provided to you when handling calls. 
Do not try to simulate or describe what you would do - actually use the tools.

When answering calls:
1. Use the 'handle_call' tool to manage the call
2. Use the 'schedule_appointment' tool to book appointments
3. Use the 'log_call_summary' tool to record call details

Format your tool calls exactly as shown in the tool documentation.
Always include all required parameters.
`;
}

/**
 * Process an incoming call
 */
export async function processIncomingCall(callId: string): Promise<void> {
  if (!voiceAgent) {
    throw new Error('Voice Agent Service not initialized');
  }
  
  await voiceAgent.processIncomingCall(callId);
}

/**
 * Handle call completion
 */
export async function handleCallCompletion(callId: string, callData: any): Promise<void> {
  if (!voiceAgent) {
    throw new Error('Voice Agent Service not initialized');
  }
  
  await voiceAgent.handleCallCompletion(callId, callData);
}

/**
 * Get the voice agent
 */
export function getVoiceAgent(): VoiceAgent | null {
  return voiceAgent;
}
