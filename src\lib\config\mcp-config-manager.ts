/**
 * MCP Configuration Manager
 * 
 * This module provides comprehensive configuration management for MCP servers
 * with support for environment variables, config files, and runtime reconfiguration.
 */

import EventEmitter from 'eventemitter3';
import { MCPServerConfig, MCPRegistryConfig, ToolRoutingRule } from '../mcp/types';

/**
 * Configuration Source Types
 */
type ConfigSource = 'environment' | 'file' | 'runtime' | 'default';

/**
 * Configuration Change Event
 */
interface ConfigChangeEvent {
  source: ConfigSource;
  type: 'server-added' | 'server-removed' | 'server-updated' | 'routing-updated' | 'global-updated';
  data: any;
  timestamp: number;
}

/**
 * MCP Configuration Manager
 */
export class MCPConfigManager extends EventEmitter {
  private static instance: MCPConfigManager;
  private config: MCPRegistryConfig;
  private configSources: Map<string, ConfigSource> = new Map();
  private configWatchers: Map<string, NodeJS.Timeout> = new Map();
  private isInitialized: boolean = false;

  /**
   * Get the singleton instance
   */
  static getInstance(): MCPConfigManager {
    if (!MCPConfigManager.instance) {
      MCPConfigManager.instance = new MCPConfigManager();
    }
    return MCPConfigManager.instance;
  }

  constructor() {
    super();
    this.config = this.getDefaultConfig();
  }

  /**
   * Initialize the configuration manager
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    try {
      // Load configuration from various sources
      await this.loadFromEnvironment();
      await this.loadFromConfigFile();
      
      // Start configuration watching
      this.startConfigWatching();
      
      this.isInitialized = true;
      this.emit('initialized', this.config);
      
      console.log('MCP Configuration Manager initialized');
    } catch (error) {
      console.error('Error initializing MCP Configuration Manager:', error);
      throw error;
    }
  }

  /**
   * Get the current configuration
   */
  getConfig(): MCPRegistryConfig {
    return JSON.parse(JSON.stringify(this.config)); // Deep clone
  }

  /**
   * Get server configurations
   */
  getServerConfigs(): MCPServerConfig[] {
    return [...this.config.servers];
  }

  /**
   * Get a specific server configuration
   */
  getServerConfig(serverId: string): MCPServerConfig | undefined {
    return this.config.servers.find(server => server.id === serverId);
  }

  /**
   * Add a new server configuration
   */
  addServerConfig(serverConfig: MCPServerConfig, source: ConfigSource = 'runtime'): void {
    // Check if server already exists
    const existingIndex = this.config.servers.findIndex(server => server.id === serverConfig.id);
    
    if (existingIndex !== -1) {
      throw new Error(`Server with ID ${serverConfig.id} already exists`);
    }

    // Add the server
    this.config.servers.push(serverConfig);
    this.configSources.set(serverConfig.id, source);
    
    // Emit change event
    this.emitConfigChange('server-added', serverConfig, source);
    
    console.log(`Added server configuration: ${serverConfig.name} (${serverConfig.id})`);
  }

  /**
   * Update a server configuration
   */
  updateServerConfig(serverId: string, updates: Partial<MCPServerConfig>, source: ConfigSource = 'runtime'): void {
    const serverIndex = this.config.servers.findIndex(server => server.id === serverId);
    
    if (serverIndex === -1) {
      throw new Error(`Server with ID ${serverId} not found`);
    }

    // Update the server
    const oldConfig = this.config.servers[serverIndex];
    this.config.servers[serverIndex] = { ...oldConfig, ...updates };
    this.configSources.set(serverId, source);
    
    // Emit change event
    this.emitConfigChange('server-updated', {
      serverId,
      oldConfig,
      newConfig: this.config.servers[serverIndex],
      updates
    }, source);
    
    console.log(`Updated server configuration: ${serverId}`);
  }

  /**
   * Remove a server configuration
   */
  removeServerConfig(serverId: string, source: ConfigSource = 'runtime'): void {
    const serverIndex = this.config.servers.findIndex(server => server.id === serverId);
    
    if (serverIndex === -1) {
      throw new Error(`Server with ID ${serverId} not found`);
    }

    // Remove the server
    const removedServer = this.config.servers.splice(serverIndex, 1)[0];
    this.configSources.delete(serverId);
    
    // Emit change event
    this.emitConfigChange('server-removed', removedServer, source);
    
    console.log(`Removed server configuration: ${serverId}`);
  }

  /**
   * Get routing rules
   */
  getRoutingRules(): ToolRoutingRule[] {
    return [...this.config.toolRouting.rules];
  }

  /**
   * Add a routing rule
   */
  addRoutingRule(rule: ToolRoutingRule, source: ConfigSource = 'runtime'): void {
    // Check if rule already exists
    const existingIndex = this.config.toolRouting.rules.findIndex(r => r.id === rule.id);
    
    if (existingIndex !== -1) {
      throw new Error(`Routing rule with ID ${rule.id} already exists`);
    }

    // Add the rule
    this.config.toolRouting.rules.push(rule);
    
    // Sort by priority
    this.config.toolRouting.rules.sort((a, b) => b.priority - a.priority);
    
    // Emit change event
    this.emitConfigChange('routing-updated', { action: 'added', rule }, source);
    
    console.log(`Added routing rule: ${rule.name} (${rule.id})`);
  }

  /**
   * Update a routing rule
   */
  updateRoutingRule(ruleId: string, updates: Partial<ToolRoutingRule>, source: ConfigSource = 'runtime'): void {
    const ruleIndex = this.config.toolRouting.rules.findIndex(rule => rule.id === ruleId);
    
    if (ruleIndex === -1) {
      throw new Error(`Routing rule with ID ${ruleId} not found`);
    }

    // Update the rule
    const oldRule = this.config.toolRouting.rules[ruleIndex];
    this.config.toolRouting.rules[ruleIndex] = { ...oldRule, ...updates };
    
    // Re-sort by priority if priority was updated
    if (updates.priority !== undefined) {
      this.config.toolRouting.rules.sort((a, b) => b.priority - a.priority);
    }
    
    // Emit change event
    this.emitConfigChange('routing-updated', {
      action: 'updated',
      ruleId,
      oldRule,
      newRule: this.config.toolRouting.rules[ruleIndex],
      updates
    }, source);
    
    console.log(`Updated routing rule: ${ruleId}`);
  }

  /**
   * Remove a routing rule
   */
  removeRoutingRule(ruleId: string, source: ConfigSource = 'runtime'): void {
    const ruleIndex = this.config.toolRouting.rules.findIndex(rule => rule.id === ruleId);
    
    if (ruleIndex === -1) {
      throw new Error(`Routing rule with ID ${ruleId} not found`);
    }

    // Remove the rule
    const removedRule = this.config.toolRouting.rules.splice(ruleIndex, 1)[0];
    
    // Emit change event
    this.emitConfigChange('routing-updated', { action: 'removed', rule: removedRule }, source);
    
    console.log(`Removed routing rule: ${ruleId}`);
  }

  /**
   * Update global configuration
   */
  updateGlobalConfig(updates: Partial<Omit<MCPRegistryConfig, 'servers'>>, source: ConfigSource = 'runtime'): void {
    const oldConfig = { ...this.config };
    
    // Update monitoring settings
    if (updates.monitoring) {
      this.config.monitoring = { ...this.config.monitoring, ...updates.monitoring };
    }
    
    // Update performance settings
    if (updates.performance) {
      this.config.performance = { ...this.config.performance, ...updates.performance };
    }
    
    // Update tool routing default strategy
    if (updates.toolRouting) {
      this.config.toolRouting = { ...this.config.toolRouting, ...updates.toolRouting };
    }
    
    // Emit change event
    this.emitConfigChange('global-updated', {
      oldConfig,
      newConfig: this.config,
      updates
    }, source);
    
    console.log('Updated global configuration');
  }

  /**
   * Export configuration to JSON
   */
  exportConfig(): string {
    return JSON.stringify(this.config, null, 2);
  }

  /**
   * Import configuration from JSON
   */
  importConfig(configJson: string, source: ConfigSource = 'runtime'): void {
    try {
      const importedConfig = JSON.parse(configJson) as MCPRegistryConfig;
      
      // Validate the configuration
      this.validateConfig(importedConfig);
      
      // Update the configuration
      const oldConfig = this.config;
      this.config = importedConfig;
      
      // Update config sources
      for (const server of this.config.servers) {
        this.configSources.set(server.id, source);
      }
      
      // Emit change event
      this.emitConfigChange('global-updated', {
        oldConfig,
        newConfig: this.config,
        imported: true
      }, source);
      
      console.log('Imported configuration successfully');
    } catch (error) {
      throw new Error(`Failed to import configuration: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Reset configuration to defaults
   */
  resetToDefaults(): void {
    const oldConfig = this.config;
    this.config = this.getDefaultConfig();
    this.configSources.clear();
    
    // Emit change event
    this.emitConfigChange('global-updated', {
      oldConfig,
      newConfig: this.config,
      reset: true
    }, 'default');
    
    console.log('Reset configuration to defaults');
  }

  /**
   * Shutdown the configuration manager
   */
  async shutdown(): Promise<void> {
    // Stop config watchers
    for (const watcher of this.configWatchers.values()) {
      clearTimeout(watcher);
    }
    this.configWatchers.clear();
    
    this.isInitialized = false;
    this.removeAllListeners();
    
    console.log('MCP Configuration Manager shutdown');
  }

  // Private methods

  /**
   * Get default configuration
   */
  private getDefaultConfig(): MCPRegistryConfig {
    return {
      servers: [
        {
          id: 'context7-default',
          name: 'Context7 Default Server',
          type: 'context7',
          enabled: true,
          priority: 1,
          timeout: 30000,
          healthCheck: {
            enabled: true,
            interval: 30000,
            timeout: 5000,
            retries: 3
          },
          customConfig: {}
        }
      ],
      toolRouting: {
        defaultStrategy: 'priority',
        rules: []
      },
      monitoring: {
        enabled: true,
        metricsRetention: 24 * 60 * 60 * 1000, // 24 hours
        healthCheckInterval: 30000
      },
      performance: {
        maxConcurrentCalls: 10,
        defaultTimeout: 30000,
        cacheEnabled: true,
        cacheTTL: 5 * 60 * 1000 // 5 minutes
      }
    };
  }

  /**
   * Load configuration from environment variables
   */
  private async loadFromEnvironment(): Promise<void> {
    // Check for MCP_CONFIG environment variable
    const mcpConfigEnv = process.env.MCP_CONFIG;
    if (mcpConfigEnv) {
      try {
        const envConfig = JSON.parse(mcpConfigEnv);
        this.mergeConfig(envConfig, 'environment');
      } catch (error) {
        console.error('Error parsing MCP_CONFIG environment variable:', error);
      }
    }

    // Load individual server configurations from environment
    const serverTypes = ['context7', 'supabase', 'github', 'vapi', 'telnyx'];
    
    for (const serverType of serverTypes) {
      const envPrefix = `MCP_${serverType.toUpperCase()}`;
      const enabled = process.env[`${envPrefix}_ENABLED`];
      
      if (enabled === 'true') {
        const serverConfig: MCPServerConfig = {
          id: `${serverType}-env`,
          name: `${serverType} Environment Server`,
          type: serverType,
          enabled: true,
          priority: parseInt(process.env[`${envPrefix}_PRIORITY`] || '1'),
          endpoint: process.env[`${envPrefix}_ENDPOINT`],
          apiKey: process.env[`${envPrefix}_API_KEY`],
          timeout: parseInt(process.env[`${envPrefix}_TIMEOUT`] || '30000'),
          customConfig: {}
        };

        // Add health check configuration
        if (process.env[`${envPrefix}_HEALTH_CHECK`] === 'true') {
          serverConfig.healthCheck = {
            enabled: true,
            interval: parseInt(process.env[`${envPrefix}_HEALTH_INTERVAL`] || '30000'),
            timeout: parseInt(process.env[`${envPrefix}_HEALTH_TIMEOUT`] || '5000'),
            retries: parseInt(process.env[`${envPrefix}_HEALTH_RETRIES`] || '3')
          };
        }

        // Check if server already exists
        const existingIndex = this.config.servers.findIndex(server => server.id === serverConfig.id);
        if (existingIndex === -1) {
          this.config.servers.push(serverConfig);
          this.configSources.set(serverConfig.id, 'environment');
        }
      }
    }
  }

  /**
   * Load configuration from config file
   */
  private async loadFromConfigFile(): Promise<void> {
    // In a real implementation, this would read from actual config files
    // For now, we'll skip file loading
    console.log('Config file loading not implemented yet');
  }

  /**
   * Merge configuration from a source
   */
  private mergeConfig(sourceConfig: Partial<MCPRegistryConfig>, source: ConfigSource): void {
    if (sourceConfig.servers) {
      for (const serverConfig of sourceConfig.servers) {
        const existingIndex = this.config.servers.findIndex(server => server.id === serverConfig.id);
        if (existingIndex === -1) {
          this.config.servers.push(serverConfig);
          this.configSources.set(serverConfig.id, source);
        } else {
          // Update existing server
          this.config.servers[existingIndex] = { ...this.config.servers[existingIndex], ...serverConfig };
          this.configSources.set(serverConfig.id, source);
        }
      }
    }

    if (sourceConfig.toolRouting) {
      this.config.toolRouting = { ...this.config.toolRouting, ...sourceConfig.toolRouting };
    }

    if (sourceConfig.monitoring) {
      this.config.monitoring = { ...this.config.monitoring, ...sourceConfig.monitoring };
    }

    if (sourceConfig.performance) {
      this.config.performance = { ...this.config.performance, ...sourceConfig.performance };
    }
  }

  /**
   * Validate configuration
   */
  private validateConfig(config: MCPRegistryConfig): void {
    if (!config.servers || !Array.isArray(config.servers)) {
      throw new Error('Configuration must have a servers array');
    }

    for (const server of config.servers) {
      if (!server.id || !server.name || !server.type) {
        throw new Error('Each server must have id, name, and type');
      }
    }

    if (!config.toolRouting || !config.toolRouting.defaultStrategy) {
      throw new Error('Configuration must have toolRouting with defaultStrategy');
    }
  }

  /**
   * Start configuration watching
   */
  private startConfigWatching(): void {
    // Watch for environment variable changes (simplified)
    const watchInterval = parseInt(process.env.MCP_CONFIG_WATCH_INTERVAL || '60000'); // 1 minute
    
    const watcher = setInterval(async () => {
      try {
        await this.loadFromEnvironment();
      } catch (error) {
        console.error('Error during config watching:', error);
      }
    }, watchInterval);
    
    this.configWatchers.set('environment', watcher);
  }

  /**
   * Emit configuration change event
   */
  private emitConfigChange(type: ConfigChangeEvent['type'], data: any, source: ConfigSource): void {
    const event: ConfigChangeEvent = {
      source,
      type,
      data,
      timestamp: Date.now()
    };
    
    this.emit('config-changed', event);
    this.emit(type, event);
  }
}
