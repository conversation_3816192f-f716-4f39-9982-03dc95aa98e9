import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';

export default defineConfig({
  plugins: [react()],
  optimizeDeps: {
    exclude: [
      'playwright-core',
      'playwright',
      'chromium-bidi'
    ],
  },
  build: {
    commonjsOptions: {
      exclude: [
        'playwright-core',
        'playwright',
        'chromium-bidi'
      ],
    },
  },
  server: {
    fs: {
      // Allow serving files from one level up to the project root
      allow: ['..'],
    },
  },
});
