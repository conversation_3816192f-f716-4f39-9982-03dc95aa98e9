{"name": "ag3nt-server", "version": "1.0.0", "description": "Server for AG3NT application", "main": "mcp-server.js", "scripts": {"start": "node mcp-server.js", "dev": "nodemon mcp-server.js"}, "dependencies": {"@langchain/openai": "^0.5.10", "@langchain/pinecone": "^0.2.0", "@pinecone-database/pinecone": "^0.1.6", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2"}, "devDependencies": {"nodemon": "^3.0.1"}}