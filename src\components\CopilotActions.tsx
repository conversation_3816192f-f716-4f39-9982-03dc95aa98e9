"use client";

import React, { ReactNode } from "react";
import { useCopilotAction } from "@copilotkit/react-core";

interface CopilotActionsProps {
  children: ReactNode;
  onScheduleAppointment?: (appointmentData: any) => Promise<void>;
  onSubmitContactForm?: (formData: any) => Promise<void>;
  onRequestEstimate?: (estimateData: any) => Promise<void>;
}

/**
 * CopilotActions component that defines actions the AI assistant can perform
 */
export function CopilotActions({
  children,
  onScheduleAppointment,
  onSubmitContactForm,
  onRequestEstimate,
}: CopilotActionsProps) {
  // Schedule appointment action
  useCopilotAction({
    name: "scheduleAppointment",
    description: "Schedule a roofing appointment or estimate",
    parameters: [
      {
        name: "customerName",
        type: "string",
        description: "Customer's full name",
        required: true,
      },
      {
        name: "phoneNumber",
        type: "string",
        description: "Customer's phone number",
        required: true,
      },
      {
        name: "email",
        type: "string",
        description: "Customer's email address",
      },
      {
        name: "address",
        type: "string",
        description: "Customer's property address",
        required: true,
      },
      {
        name: "serviceType",
        type: "string",
        description: "Type of service needed (repair, replacement, inspection, etc.)",
        required: true,
      },
      {
        name: "preferredDate",
        type: "string",
        description: "Preferred appointment date (YYYY-MM-DD format)",
        required: true,
      },
      {
        name: "preferredTime",
        type: "string",
        description: "Preferred appointment time (morning, afternoon, specific time)",
        required: true,
      },
      {
        name: "notes",
        type: "string",
        description: "Additional notes or details about the appointment",
      },
    ],
    renderAndWaitForResponse: ({ args, status, respond }) => (
      <AppointmentConfirmation
        appointmentData={args}
        isExecuting={status === "executing"}
        onCancel={() => respond?.({ approved: false })}
        onConfirm={() => respond?.({ 
          approved: true, 
          metadata: { confirmedAt: new Date().toISOString() } 
        })}
      />
    ),
    handler: async ({ approved, metadata, ...args }) => {
      if (!approved) {
        return "Appointment scheduling was cancelled.";
      }
      
      if (onScheduleAppointment) {
        await onScheduleAppointment({ ...args, ...metadata });
      }
      
      return `Appointment scheduled successfully for ${args.customerName} on ${args.preferredDate} at ${args.preferredTime}.`;
    },
  });

  // Submit contact form action
  useCopilotAction({
    name: "submitContactForm",
    description: "Submit a contact form on behalf of the customer",
    parameters: [
      {
        name: "name",
        type: "string",
        description: "Customer's full name",
        required: true,
      },
      {
        name: "email",
        type: "string",
        description: "Customer's email address",
        required: true,
      },
      {
        name: "phone",
        type: "string",
        description: "Customer's phone number",
        required: true,
      },
      {
        name: "message",
        type: "string",
        description: "Customer's message or inquiry",
        required: true,
      },
    ],
    handler: async (args) => {
      if (onSubmitContactForm) {
        await onSubmitContactForm(args);
      }
      
      return `Contact form submitted successfully for ${args.name}. We will get back to you shortly.`;
    },
  });

  // Request estimate action
  useCopilotAction({
    name: "requestEstimate",
    description: "Request a free roofing estimate",
    parameters: [
      {
        name: "customerName",
        type: "string",
        description: "Customer's full name",
        required: true,
      },
      {
        name: "phoneNumber",
        type: "string",
        description: "Customer's phone number",
        required: true,
      },
      {
        name: "email",
        type: "string",
        description: "Customer's email address",
      },
      {
        name: "address",
        type: "string",
        description: "Property address for the estimate",
        required: true,
      },
      {
        name: "roofType",
        type: "string",
        description: "Type of roof (shingle, tile, metal, etc.)",
      },
      {
        name: "projectDescription",
        type: "string",
        description: "Description of the roofing project or issues",
        required: true,
      },
    ],
    handler: async (args) => {
      if (onRequestEstimate) {
        await onRequestEstimate(args);
      }
      
      return `Estimate request submitted successfully for ${args.customerName} at ${args.address}. We will contact you to schedule a free estimate.`;
    },
  });

  return <>{children}</>;
}

// Appointment confirmation component
function AppointmentConfirmation({ 
  appointmentData, 
  isExecuting, 
  onCancel, 
  onConfirm 
}: any) {
  return (
    <div className="bg-white rounded-lg p-4 border border-gray-200 shadow-md">
      <h3 className="text-lg font-semibold mb-2">Confirm Appointment</h3>
      
      <div className="space-y-2 mb-4">
        <p><strong>Name:</strong> {appointmentData.customerName}</p>
        <p><strong>Phone:</strong> {appointmentData.phoneNumber}</p>
        <p><strong>Address:</strong> {appointmentData.address}</p>
        <p><strong>Service:</strong> {appointmentData.serviceType}</p>
        <p><strong>Date:</strong> {appointmentData.preferredDate}</p>
        <p><strong>Time:</strong> {appointmentData.preferredTime}</p>
        {appointmentData.notes && (
          <p><strong>Notes:</strong> {appointmentData.notes}</p>
        )}
      </div>
      
      <div className="flex justify-end space-x-2">
        <button
          onClick={onCancel}
          disabled={isExecuting}
          className="px-4 py-2 bg-gray-200 rounded hover:bg-gray-300"
        >
          Cancel
        </button>
        <button
          onClick={onConfirm}
          disabled={isExecuting}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          {isExecuting ? "Confirming..." : "Confirm Appointment"}
        </button>
      </div>
    </div>
  );
}
