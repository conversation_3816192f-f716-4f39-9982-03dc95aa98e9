/**
 * Dynamic MCP Server Discovery
 *
 * This module provides automatic detection and registration of available MCP servers
 * with runtime configuration and hot-swapping capabilities.
 */

import EventEmitter from 'eventemitter3';
import { MCPServerAdapter } from './mcp-server-adapter';
import { MCPServerConfig, MCPServerStatus } from './types';
import { Context7Adapter } from './context7-adapter';

/**
 * MCP Server Factory
 */
export class MCPServerFactory {
  private static adapterRegistry: Map<string, new () => MCPServerAdapter> = new Map();

  /**
   * Register an adapter class for a server type
   */
  static registerAdapter(serverType: string, adapterClass: new () => MCPServerAdapter): void {
    this.adapterRegistry.set(serverType, adapterClass);
    console.log(`Registered MCP adapter for server type: ${serverType}`);
  }

  /**
   * Create an adapter instance for a server type
   */
  static createAdapter(serverType: string): MCPServerAdapter | null {
    const AdapterClass = this.adapterRegistry.get(serverType);
    if (!AdapterClass) {
      console.warn(`No adapter registered for server type: ${serverType}`);
      return null;
    }

    try {
      return new AdapterClass();
    } catch (error) {
      console.error(`Error creating adapter for server type ${serverType}:`, error);
      return null;
    }
  }

  /**
   * Get all registered server types
   */
  static getRegisteredTypes(): string[] {
    return Array.from(this.adapterRegistry.keys());
  }
}

/**
 * MCP Server Discovery Service
 */
export class MCPServerDiscovery extends EventEmitter {
  private static instance: MCPServerDiscovery;
  private discoveredServers: Map<string, MCPServerConfig> = new Map();
  private serverStatuses: Map<string, MCPServerStatus> = new Map();
  private configWatchers: Map<string, NodeJS.Timeout> = new Map();
  private discoveryInterval?: NodeJS.Timeout;
  private isRunning: boolean = false;

  /**
   * Get the singleton instance
   */
  static getInstance(): MCPServerDiscovery {
    if (!MCPServerDiscovery.instance) {
      MCPServerDiscovery.instance = new MCPServerDiscovery();
    }
    return MCPServerDiscovery.instance;
  }

  /**
   * Initialize the discovery service
   */
  async initialize(): Promise<void> {
    if (this.isRunning) {
      return;
    }

    // Register built-in adapters
    this.registerBuiltInAdapters();

    // Discover servers from environment variables
    await this.discoverFromEnvironment();

    // Discover servers from configuration files
    await this.discoverFromConfig();

    // Start periodic discovery
    this.startPeriodicDiscovery();

    this.isRunning = true;
    this.emit('discovery-started');

    console.log('MCP Server Discovery initialized');
  }

  /**
   * Shutdown the discovery service
   */
  async shutdown(): Promise<void> {
    if (!this.isRunning) {
      return;
    }

    // Stop periodic discovery
    if (this.discoveryInterval) {
      clearInterval(this.discoveryInterval);
    }

    // Stop config watchers
    for (const watcher of this.configWatchers.values()) {
      clearTimeout(watcher);
    }
    this.configWatchers.clear();

    this.isRunning = false;
    this.emit('discovery-stopped');

    console.log('MCP Server Discovery shutdown');
  }

  /**
   * Get all discovered servers
   */
  getDiscoveredServers(): MCPServerConfig[] {
    return Array.from(this.discoveredServers.values());
  }

  /**
   * Get server status
   */
  getServerStatus(serverId: string): MCPServerStatus | undefined {
    return this.serverStatuses.get(serverId);
  }

  /**
   * Get all server statuses
   */
  getAllServerStatuses(): MCPServerStatus[] {
    return Array.from(this.serverStatuses.values());
  }

  /**
   * Add a server configuration manually
   */
  addServer(config: MCPServerConfig): void {
    this.discoveredServers.set(config.id, config);
    this.updateServerStatus(config.id, 'initializing');
    this.emit('server-discovered', config);
  }

  /**
   * Remove a server configuration
   */
  removeServer(serverId: string): void {
    const config = this.discoveredServers.get(serverId);
    if (config) {
      this.discoveredServers.delete(serverId);
      this.serverStatuses.delete(serverId);
      this.emit('server-removed', config);
    }
  }

  /**
   * Update server configuration
   */
  updateServer(serverId: string, updates: Partial<MCPServerConfig>): void {
    const config = this.discoveredServers.get(serverId);
    if (config) {
      const updatedConfig = { ...config, ...updates };
      this.discoveredServers.set(serverId, updatedConfig);
      this.emit('server-updated', updatedConfig);
    }
  }

  /**
   * Register built-in adapters
   */
  private registerBuiltInAdapters(): void {
    MCPServerFactory.registerAdapter('context7', Context7Adapter);

    // Register other built-in adapters
    try {
      const { SupabaseAdapter } = require('./adapters/supabase-adapter');
      MCPServerFactory.registerAdapter('supabase', SupabaseAdapter);
    } catch (error) {
      console.warn('Supabase adapter not available:', error);
    }

    // Register additional adapters here as they're created
    // MCPServerFactory.registerAdapter('github', GitHubAdapter);
    // MCPServerFactory.registerAdapter('vapi', VapiAdapter);
    // MCPServerFactory.registerAdapter('telnyx', TelnyxAdapter);
  }

  /**
   * Discover servers from environment variables
   */
  private async discoverFromEnvironment(): Promise<void> {
    const envConfigs = this.parseEnvironmentConfig();

    for (const config of envConfigs) {
      this.addServer(config);
    }

    console.log(`Discovered ${envConfigs.length} servers from environment variables`);
  }

  /**
   * Parse MCP server configuration from environment variables
   */
  private parseEnvironmentConfig(): MCPServerConfig[] {
    const configs: MCPServerConfig[] = [];

    // Look for MCP_SERVERS environment variable
    const mcpServersEnv = import.meta.env.VITE_MCP_SERVERS;
    if (mcpServersEnv) {
      try {
        const serverConfigs = JSON.parse(mcpServersEnv);
        if (Array.isArray(serverConfigs)) {
          configs.push(...serverConfigs);
        }
      } catch (error) {
        console.error('Error parsing VITE_MCP_SERVERS environment variable:', error);
      }
    }

    // Look for individual server configurations
    const serverTypes = MCPServerFactory.getRegisteredTypes();
    for (const serverType of serverTypes) {
      const envPrefix = `VITE_MCP_${serverType.toUpperCase()}`;
      const enabled = import.meta.env[`${envPrefix}_ENABLED`];

      if (enabled === 'true') {
        const config: MCPServerConfig = {
          id: `${serverType}-env`,
          name: `${serverType} Environment Server`,
          type: serverType,
          enabled: true,
          priority: parseInt(import.meta.env[`${envPrefix}_PRIORITY`] || '1'),
          endpoint: import.meta.env[`${envPrefix}_ENDPOINT`],
          apiKey: import.meta.env[`${envPrefix}_API_KEY`],
          timeout: parseInt(import.meta.env[`${envPrefix}_TIMEOUT`] || '30000'),
          customConfig: {}
        };

        // Add health check configuration
        if (import.meta.env[`${envPrefix}_HEALTH_CHECK`] === 'true') {
          config.healthCheck = {
            enabled: true,
            interval: parseInt(import.meta.env[`${envPrefix}_HEALTH_INTERVAL`] || '30000'),
            timeout: parseInt(import.meta.env[`${envPrefix}_HEALTH_TIMEOUT`] || '5000'),
            retries: parseInt(import.meta.env[`${envPrefix}_HEALTH_RETRIES`] || '3')
          };
        }

        configs.push(config);
      }
    }

    return configs;
  }

  /**
   * Discover servers from configuration files
   */
  private async discoverFromConfig(): Promise<void> {
    // In a real implementation, this would read from config files
    // For now, we'll add some default configurations

    const defaultConfigs: MCPServerConfig[] = [
      {
        id: 'context7-default',
        name: 'Context7 Default Server',
        type: 'context7',
        enabled: true,
        priority: 1,
        timeout: 30000,
        healthCheck: {
          enabled: true,
          interval: 30000,
          timeout: 5000,
          retries: 3
        },
        customConfig: {}
      }
    ];

    for (const config of defaultConfigs) {
      if (!this.discoveredServers.has(config.id)) {
        this.addServer(config);
      }
    }

    console.log(`Discovered ${defaultConfigs.length} servers from configuration`);
  }

  /**
   * Start periodic discovery
   */
  private startPeriodicDiscovery(): void {
    const interval = parseInt(process.env.MCP_DISCOVERY_INTERVAL || '60000'); // 1 minute default

    this.discoveryInterval = setInterval(async () => {
      try {
        await this.performDiscovery();
      } catch (error) {
        console.error('Error during periodic discovery:', error);
      }
    }, interval);
  }

  /**
   * Perform discovery scan
   */
  private async performDiscovery(): Promise<void> {
    // Re-scan environment variables for changes
    const envConfigs = this.parseEnvironmentConfig();

    for (const config of envConfigs) {
      const existing = this.discoveredServers.get(config.id);
      if (!existing) {
        this.addServer(config);
      } else if (JSON.stringify(existing) !== JSON.stringify(config)) {
        this.updateServer(config.id, config);
      }
    }

    // Update server statuses
    for (const [serverId] of this.discoveredServers) {
      await this.checkServerHealth(serverId);
    }

    this.emit('discovery-scan-completed');
  }

  /**
   * Check server health
   */
  private async checkServerHealth(serverId: string): Promise<void> {
    const config = this.discoveredServers.get(serverId);
    if (!config) {
      return;
    }

    try {
      // Create adapter to check health
      const adapter = MCPServerFactory.createAdapter(config.type);
      if (!adapter) {
        this.updateServerStatus(serverId, 'error', 'No adapter available');
        return;
      }

      // Initialize adapter temporarily for health check
      await adapter.initialize(config);
      const health = await adapter.getHealth();
      await adapter.shutdown();

      // Update status based on health
      const status: MCPServerStatus['status'] =
        health.status === 'healthy' ? 'online' :
        health.status === 'degraded' ? 'degraded' : 'offline';

      this.updateServerStatus(serverId, status);
    } catch (error) {
      this.updateServerStatus(serverId, 'error', error instanceof Error ? error.message : String(error));
    }
  }

  /**
   * Update server status
   */
  private updateServerStatus(serverId: string, status: MCPServerStatus['status'], errorMessage?: string): void {
    const config = this.discoveredServers.get(serverId);
    if (!config) {
      return;
    }

    const serverStatus: MCPServerStatus = {
      id: serverId,
      status,
      health: {
        status: status === 'online' ? 'healthy' : status === 'degraded' ? 'degraded' : 'unhealthy',
        lastCheck: Date.now(),
        responseTime: 0,
        errorCount: 0,
        uptime: 0
      },
      toolCount: 0,
      lastActivity: Date.now(),
      errorMessage
    };

    this.serverStatuses.set(serverId, serverStatus);
    this.emit('server-status-changed', serverStatus);
  }
}
