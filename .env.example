# OpenRouter API Configuration
VITE_OPENROUTER_API_KEY=your_openrouter_api_key_here

# Pinecone Configuration (for RAG functionality)
VITE_PINECONE_API_KEY=your_pinecone_api_key_here
VITE_PINECONE_ENVIRONMENT=your_pinecone_environment
VITE_PINECONE_INDEX=your_pinecone_index_name
VITE_PINECONE_HOST=your_pinecone_host_url
VITE_PINECONE_METRIC=cosine
VITE_PINECONE_DIMENSIONS=1536
VITE_EMBEDDING_MODEL=text-embedding-ada-002

# MCP Server Configuration
# Context7 (Code analysis and documentation)
VITE_MCP_CONTEXT7_ENABLED=true
VITE_MCP_CONTEXT7_API_KEY=your_context7_api_key
VITE_MCP_CONTEXT7_ENDPOINT=https://api.context7.com
VITE_MCP_CONTEXT7_PRIORITY=1
VITE_MCP_CONTEXT7_HEALTH_CHECK=true
VITE_MCP_CONTEXT7_HEALTH_INTERVAL=30000
VITE_MCP_CONTEXT7_HEALTH_TIMEOUT=5000
VITE_MCP_CONTEXT7_HEALTH_RETRIES=3

# Supabase (Database operations)
VITE_MCP_SUPABASE_ENABLED=false
VITE_MCP_SUPABASE_API_KEY=your_supabase_anon_key
VITE_MCP_SUPABASE_ENDPOINT=https://your-project.supabase.co
VITE_MCP_SUPABASE_PRIORITY=2

# GitHub (Repository management)
VITE_MCP_GITHUB_ENABLED=false
VITE_MCP_GITHUB_API_KEY=your_github_token
VITE_MCP_GITHUB_ENDPOINT=https://api.github.com
VITE_MCP_GITHUB_PRIORITY=3

# VAPI (Voice processing)
VITE_MCP_VAPI_ENABLED=false
VITE_MCP_VAPI_API_KEY=your_vapi_api_key
VITE_MCP_VAPI_ENDPOINT=https://api.vapi.ai
VITE_MCP_VAPI_PRIORITY=4

# Telnyx (Telephony and messaging)
VITE_MCP_TELNYX_ENABLED=false
VITE_MCP_TELNYX_API_KEY=your_telnyx_api_key
VITE_MCP_TELNYX_ENDPOINT=https://api.telnyx.com
VITE_MCP_TELNYX_PRIORITY=5

# Global MCP Configuration
VITE_MCP_CONFIG_WATCH_INTERVAL=60000
VITE_MCP_DISCOVERY_INTERVAL=60000

# Application Configuration
VITE_APP_NAME=Phoenix Roofing Assistant
VITE_APP_VERSION=1.0.0
