import { NextRequest, NextResponse } from "next/server";
import { OpenAI } from "openai";
import { ChatCompletionMessageParam, ChatCompletionTool } from "openai/resources/chat/completions";
import { MCPToolRegistry } from "../../../../lib/mcp/mcp-tool-registry";

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY || "",
});

// Initialize MCP Tool Registry
const mcpToolRegistry = MCPToolRegistry.getInstance();

export async function POST(req: NextRequest) {
  try {
    // Parse request body
    const body = await req.json();
    const { messages, model = "gpt-4o" } = body;

    // Validate request
    if (!messages || !Array.isArray(messages)) {
      return NextResponse.json(
        { error: "Invalid request: messages array is required" },
        { status: 400 }
      );
    }

    // Format messages for OpenAI
    const formattedMessages: ChatCompletionMessageParam[] = messages.map((msg: any) => ({
      role: msg.role,
      content: msg.content,
    }));

    // Initialize MCP Tool Registry if not already initialized
    if (!mcpToolRegistry.getAllTools().length) {
      await mcpToolRegistry.initialize(["context7"]);
    }

    // Get all tools from the registry
    const mcpTools = mcpToolRegistry.getAllTools();

    // Format tools for OpenAI
    const tools: ChatCompletionTool[] = mcpTools.map((tool) => {
      const toolInfo = mcpToolRegistry.getToolInfo(tool.name);

      // Format parameters for OpenAI
      const parameters: any = {
        type: "object",
        properties: {},
        required: [],
      };

      if (toolInfo?.parameters) {
        for (const [paramName, paramInfo] of Object.entries(toolInfo.parameters)) {
          parameters.properties[paramName] = {
            type: paramInfo.type,
            description: paramInfo.description,
          };

          if (paramInfo.required) {
            parameters.required.push(paramName);
          }
        }
      }

      return {
        type: "function",
        function: {
          name: tool.name,
          description: tool.description,
          parameters,
        },
      };
    });

    // Call OpenAI API with tools
    const response = await openai.chat.completions.create({
      model,
      messages: formattedMessages,
      tools: tools.length > 0 ? tools : undefined,
      tool_choice: tools.length > 0 ? "auto" : undefined,
      temperature: 0.7,
      stream: false,
    });

    // Check if the model wants to call a tool
    const responseMessage = response.choices[0].message;

    if (responseMessage.tool_calls && responseMessage.tool_calls.length > 0) {
      // Process each tool call
      const toolResults = await Promise.all(
        responseMessage.tool_calls.map(async (toolCall) => {
          const toolName = toolCall.function.name;
          const toolArgs = JSON.parse(toolCall.function.arguments);

          try {
            // Call the tool
            const result = await mcpToolRegistry.callTool(toolName, toolArgs);

            return {
              tool_call_id: toolCall.id,
              role: "tool",
              name: toolName,
              content: JSON.stringify(result),
            };
          } catch (error) {
            console.error(`Error calling tool ${toolName}:`, error);

            return {
              tool_call_id: toolCall.id,
              role: "tool",
              name: toolName,
              content: JSON.stringify({ error: error instanceof Error ? error.message : String(error) }),
            };
          }
        })
      );

      // Add the tool results to the messages
      const newMessages = [
        ...formattedMessages,
        responseMessage,
        ...toolResults,
      ];

      // Call OpenAI API again with the tool results
      const secondResponse = await openai.chat.completions.create({
        model,
        messages: newMessages as ChatCompletionMessageParam[],
        temperature: 0.7,
        stream: false,
      });

      // Return the final response
      return NextResponse.json({
        message: secondResponse.choices[0].message,
      });
    }

    // Return the response if no tool calls
    return NextResponse.json({
      message: responseMessage,
    });
  } catch (error: any) {
    console.error("Error in chat API:", error);

    return NextResponse.json(
      { error: error.message || "An error occurred" },
      { status: 500 }
    );
  }
}
