/**
 * VAPI Integration Service
 * 
 * This module provides a service for integrating with VAPI for voice calls.
 */

import { MCPToolRegistry } from '../mcp/mcp-tool-registry';

/**
 * Interface for VAPI assistant configuration
 */
interface VapiAssistantConfig {
  name: string;
  description: string;
  model: {
    provider: string;
    model: string;
    messages: {
      role: string;
      content: string;
    }[];
    tools: any[];
  };
}

/**
 * Interface for VAPI phone number configuration
 */
interface VapiPhoneNumberConfig {
  phoneNumber: string;
  assistantId: string;
  webhookUrl?: string;
}

/**
 * VAPI Service class
 */
export class VapiService {
  private static instance: VapiService;
  private toolRegistry: MCPToolRegistry;
  private vapiToken: string;
  private baseUrl: string = 'https://api.vapi.ai';
  private assistants: Map<string, string> = new Map(); // Map of assistant name to ID

  /**
   * Private constructor (singleton pattern)
   */
  private constructor() {
    this.toolRegistry = MCPToolRegistry.getInstance();
    this.vapiToken = process.env.VAPI_TOKEN || '';
  }

  /**
   * Get the singleton instance
   */
  public static getInstance(): VapiService {
    if (!VapiService.instance) {
      VapiService.instance = new VapiService();
    }
    return VapiService.instance;
  }

  /**
   * Initialize the VAPI service
   */
  public async initialize(): Promise<void> {
    try {
      // Initialize the tool registry with VAPI server
      await this.toolRegistry.initialize(['vapi']);
      
      console.log('VAPI Service initialized');
    } catch (error) {
      console.error('Error initializing VAPI Service:', error);
    }
  }

  /**
   * Create a VAPI assistant
   */
  public async createAssistant(config: VapiAssistantConfig): Promise<string> {
    try {
      // Check if assistant already exists
      const existingId = this.assistants.get(config.name);
      if (existingId) {
        console.log(`Assistant ${config.name} already exists with ID ${existingId}`);
        return existingId;
      }

      // In a real implementation, you would call the VAPI API
      // For now, we'll simulate creating an assistant
      const assistantId = `asst_${Date.now()}`;
      
      // Store the assistant ID
      this.assistants.set(config.name, assistantId);
      
      console.log(`Created VAPI assistant ${config.name} with ID ${assistantId}`);
      
      return assistantId;
    } catch (error) {
      console.error('Error creating VAPI assistant:', error);
      throw error;
    }
  }

  /**
   * Configure a phone number to use a specific assistant
   */
  public async configurePhoneNumber(config: VapiPhoneNumberConfig): Promise<void> {
    try {
      // In a real implementation, you would call the VAPI API
      // For now, we'll just log the configuration
      console.log(`Configured phone number ${config.phoneNumber} to use assistant ${config.assistantId}`);
      
      if (config.webhookUrl) {
        console.log(`Set webhook URL for phone number ${config.phoneNumber} to ${config.webhookUrl}`);
      }
    } catch (error) {
      console.error('Error configuring VAPI phone number:', error);
      throw error;
    }
  }

  /**
   * Get default tools for VAPI assistants
   */
  public getDefaultTools(): any[] {
    return [
      {
        type: 'transferCall',
        destinations: [
          {
            type: 'number',
            number: '+16054440129' // Replace with your actual phone number
          }
        ]
      },
      {
        type: 'endCall'
      }
    ];
  }

  /**
   * Make an API call to VAPI
   */
  private async callVapiApi(endpoint: string, data: any, method: string = 'POST'): Promise<any> {
    try {
      const response = await fetch(`${this.baseUrl}${endpoint}`, {
        method,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.vapiToken}`
        },
        body: JSON.stringify(data)
      });

      if (!response.ok) {
        throw new Error(`VAPI API error: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error(`Error calling VAPI API ${endpoint}:`, error);
      throw error;
    }
  }

  /**
   * Get call details
   */
  public async getCallDetails(callId: string): Promise<any> {
    try {
      // In a real implementation, you would call the VAPI API
      // For now, we'll return mock data
      return {
        id: callId,
        status: 'completed',
        duration: 120,
        from: '+16054440129',
        to: '+16054440130',
        transcript: 'Hello, I would like to schedule a roof inspection...'
      };
    } catch (error) {
      console.error(`Error getting call details for ${callId}:`, error);
      throw error;
    }
  }
}
