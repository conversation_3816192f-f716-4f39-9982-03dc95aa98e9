# AG3NT - LangGraph Agent with OpenRouter Integration

AG3NT is a chat interface that uses LangGraph agents with OpenRouter integration to access various LLM models.

## Features

- **LangGraph Agent**: Uses LangGraph for agent orchestration
- **OpenRouter Integration**: Access to multiple LLM models through OpenRouter
- **Context7 Integration**: Enhanced context management
- **Model Selection**: Switch between different LLM models
- **File Upload**: Support for uploading files to the chat
- **MCP (Model Context Protocol)**: Support for multiple MCP servers including Context7, Supabase, GitHub, Vapi, and Telnyx

## Setup

1. Clone the repository
2. Install dependencies:
   ```
   npm install
   cd server
   npm install
   cd ..
   pip install -U langgraph langchain langchain_openai openai
   ```
3. Set up environment variables in `.env`:
   ```
   VITE_OPENROUTER_API_KEY=your_openrouter_api_key
   CONTEXT7_API_KEY=your_context7_api_key
   ```
4. Start the MCP server API:
   ```
   start-mcp-server.bat
   ```
   or on Linux/Mac:
   ```
   cd server
   node mcp-server.js
   ```
5. Run the development server:
   ```
   npm run dev
   ```

## Architecture

### Frontend
- React with TypeScript
- Tailwind CSS for styling

### Backend
- LangGraph for agent orchestration
- OpenRouter for LLM model access
- Context7 for enhanced context management
- MCP (Model Context Protocol) for agent tool access
- Express server for MCP server management

### Models
- Default: Anthropic Claude 3.7 Sonnet
- Other available models:
  - Anthropic Claude 3.5 Sonnet
  - Anthropic Claude 3 Opus
  - OpenAI GPT-4o
  - OpenAI GPT-4 Turbo
  - Mistral Large
  - Mistral Mixtral 8x7B
  - Meta Llama 3 70B
  - Meta Llama 3 8B
  - Google Gemini Pro

## Usage

1. Start a new chat by clicking the "New Chat" button
2. Select a model from the dropdown in the header
3. Type your message and press send
4. Upload files using the paperclip icon

## Implementation Details

### LangGraph Agent
The LangGraph agent is implemented in Python and exposed to the frontend through a bridge file. It uses OpenRouter to access various LLM models and Context7 for enhanced context management.

### OpenRouter Integration
The OpenRouter integration is implemented in TypeScript and provides access to various LLM models. It uses the OpenAI client with the OpenRouter API endpoint.

### Context7 Integration
The Context7 integration is implemented in Python and provides enhanced context management for the LangGraph agent.

### MCP (Model Context Protocol) Integration
The MCP integration allows agents to access various tools from different providers:
- **Context7**: For code context and documentation
- **Supabase**: For database access and management
- **GitHub**: For repository access and management
- **Vapi**: For voice and audio processing
- **Telnyx**: For telephony and messaging

### Model Selection
The model selection is implemented in the frontend and allows switching between different LLM models provided by OpenRouter.

## Future Improvements

- Add streaming support for all models
- Implement more advanced agent capabilities
- Add support for more file types
- Implement conversation history persistence
- Add more MCP server integrations
- Improve agent collaboration using MCP tools
