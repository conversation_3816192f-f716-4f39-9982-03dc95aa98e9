// Chat-related types
export interface MessageContent {
  type: 'text' | 'image' | 'file';
  content: string;
  fileType?: string;
  fileSize?: number;
}

export interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string | MessageContent[];
  timestamp: Date;
  processing?: boolean;
  agentId?: string;
  agentName?: string;
}

export interface Conversation {
  id: string;
  title: string;
  messages: Message[];
  timestamp: Date;
}

// Settings-related types
export interface Settings {
  defaultModel: string;
  theme: 'dark' | 'light';
  apiKeys: {
    openRouter?: string;
    pinecone?: string;
  };
  maxTokens: number;
  temperature: number;
  agents: AgentSettings[];
  knowledgeBase?: KnowledgeBaseSettings;
}

// Agent-related types
export interface AgentSettings {
  id: string;
  name: string;
  description: string;
  systemPrompt: string;
  model: string;
  mcpServerUrl?: string;
  mcpServerId?: string;
  enabled: boolean;
  icon?: string;
  temperature?: number;
  maxTokens?: number;
  topP?: number;
  frequencyPenalty?: number;
  presencePenalty?: number;
}

// Knowledge base settings
export interface KnowledgeBaseSettings {
  companyInfo: CompanyInfo;
  pineconeEnvironment?: string;
  pineconeIndex?: string;
  pineconeHost?: string;
  pineconeMetric?: string;
  pineconeDimensions?: number;
  embeddingModel?: string;
}

export interface CompanyInfo {
  name?: string;
  foundedYear?: number;
  description?: string;
  industry?: string;
  size?: string;
  website?: string;
  email?: string;
  phone?: string;
  address?: string;
  products?: string;
  targetAudience?: string;
  uniqueSellingProposition?: string;
  coreValues?: string;
  missionStatement?: string;
  visionStatement?: string;
  socialMedia?: {
    linkedin?: string;
    twitter?: string;
  };
  achievements?: string;
  faq?: string;
}