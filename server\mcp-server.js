/**
 * MCP Server API
 * 
 * This module provides an API for starting and stopping MCP servers.
 */

const express = require('express');
const cors = require('cors');
const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

const app = express();
app.use(cors());
app.use(express.json());

// Map to store running MCP server processes
const mcpServers = new Map();

// Default MCP server ports
const mcpServerPorts = {
  "context7": 3100,
  "supabase": 3101,
  "github": 3102,
  "vapi": 3103,
  "telnyx": 3104
};

// Load MCP server configurations
const configPath = path.join(__dirname, '..', 'src', 'lib', 'mcp', 'mcp-config.json');
let mcpServerConfigs = {};

try {
  if (fs.existsSync(configPath)) {
    const configData = fs.readFileSync(configPath, 'utf8');
    mcpServerConfigs = JSON.parse(configData).mcpServers || {};
  } else {
    console.warn('MCP server configuration file not found. Using default configurations.');
    // Default configurations
    mcpServerConfigs = {
      "context7": {
        "command": "npx",
        "args": ["-y", "@upstash/context7-mcp@latest"]
      },
      "supabase": {
        "command": "cmd",
        "args": ["/c", "npx", "-y", "@supabase/mcp-server-supabase@latest", "--access-token", "********************************************"]
      },
      "github": {
        "command": "npx",
        "args": ["-y", "@modelcontextprotocol/server-github"],
        "env": {
          "GITHUB_PERSONAL_ACCESS_TOKEN": "*********************************************************************************************"
        }
      },
      "vapi": {
        "command": "npx",
        "args": ["-y", "@vapi-ai/mcp-server"],
        "env": {
          "VAPI_TOKEN": "<your_vapi_token>"
        }
      },
      "telnyx": {
        "command": "uvx",
        "args": ["--from", "git+https://github.com/team-telnyx/telnyx-mcp-server.git", "telnyx-mcp-server"],
        "env": {
          "TELNYX_API_KEY": "**********************************************************"
        }
      }
    };
  }
} catch (error) {
  console.error('Error loading MCP server configurations:', error);
  // Use default configurations
  mcpServerConfigs = {};
}

// API endpoint to start an MCP server
app.post('/api/mcp/start', (req, res) => {
  const { serverId } = req.body;
  
  if (!serverId) {
    return res.status(400).json({ success: false, error: 'Server ID is required' });
  }
  
  // Check if server is already running
  if (mcpServers.has(serverId) && mcpServers.get(serverId).running) {
    const port = mcpServerPorts[serverId] || 3100;
    return res.json({ 
      success: true, 
      message: `MCP server ${serverId} is already running`,
      url: `http://localhost:${port}`
    });
  }
  
  // Check if server config exists
  if (!mcpServerConfigs[serverId]) {
    return res.status(404).json({ 
      success: false, 
      error: `MCP server configuration not found for: ${serverId}` 
    });
  }
  
  const serverConfig = mcpServerConfigs[serverId];
  const port = mcpServerPorts[serverId] || 3100;
  
  try {
    console.log(`Starting MCP server: ${serverId} on port ${port}`);
    
    // Set environment variables for the process
    const env = { ...process.env, PORT: port.toString() };
    
    // Add any server-specific environment variables
    if (serverConfig.env) {
      Object.assign(env, serverConfig.env);
    }
    
    // Spawn the process
    const serverProcess = spawn(
      serverConfig.command,
      serverConfig.args,
      { 
        env,
        shell: true,
        stdio: 'pipe'
      }
    );
    
    // Store the server process
    mcpServers.set(serverId, {
      process: serverProcess,
      running: true,
      url: `http://localhost:${port}`
    });
    
    // Handle process output
    serverProcess.stdout.on('data', (data) => {
      console.log(`[${serverId}] ${data.toString().trim()}`);
    });
    
    serverProcess.stderr.on('data', (data) => {
      console.error(`[${serverId}] Error: ${data.toString().trim()}`);
    });
    
    // Handle process exit
    serverProcess.on('close', (code) => {
      console.log(`MCP server ${serverId} exited with code ${code}`);
      if (mcpServers.has(serverId)) {
        mcpServers.get(serverId).running = false;
      }
    });
    
    // Return success response
    res.json({ 
      success: true, 
      message: `MCP server ${serverId} started`,
      url: `http://localhost:${port}`
    });
  } catch (error) {
    console.error(`Error starting MCP server ${serverId}:`, error);
    res.status(500).json({ 
      success: false, 
      error: `Failed to start MCP server: ${error.message}` 
    });
  }
});

// API endpoint to stop an MCP server
app.post('/api/mcp/stop', (req, res) => {
  const { serverId } = req.body;
  
  if (!serverId) {
    return res.status(400).json({ success: false, error: 'Server ID is required' });
  }
  
  // Check if server is running
  if (!mcpServers.has(serverId) || !mcpServers.get(serverId).running) {
    return res.json({ 
      success: true, 
      message: `MCP server ${serverId} is not running` 
    });
  }
  
  try {
    const serverInfo = mcpServers.get(serverId);
    
    // Kill the process
    if (serverInfo.process) {
      serverInfo.process.kill();
      serverInfo.running = false;
    }
    
    res.json({ 
      success: true, 
      message: `MCP server ${serverId} stopped` 
    });
  } catch (error) {
    console.error(`Error stopping MCP server ${serverId}:`, error);
    res.status(500).json({ 
      success: false, 
      error: `Failed to stop MCP server: ${error.message}` 
    });
  }
});

// API endpoint to get status of all MCP servers
app.get('/api/mcp/status', (req, res) => {
  const status = {};
  
  for (const [serverId, serverInfo] of mcpServers.entries()) {
    status[serverId] = {
      running: serverInfo.running,
      url: serverInfo.url
    };
  }
  
  res.json({ 
    success: true, 
    servers: status 
  });
});

// Start the server
const PORT = process.env.MCP_API_PORT || 3000;
app.listen(PORT, () => {
  console.log(`MCP Server API running on port ${PORT}`);
});
