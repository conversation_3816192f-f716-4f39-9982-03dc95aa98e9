"use client";

import React, { useState, useEffect } from "react";
import { CopilotToolMonitor } from "../../../components/CopilotToolMonitor";

export default function CopilotSettingsPage() {
  const [settings, setSettings] = useState({
    enabled: true,
    model: "gpt-4o",
    systemPrompt: `You are a helpful AI assistant for Phoenix Roofing and Repair.
    
Phoenix Roofing and Repair is located at 532 E. Maryland Suite F, Phoenix, AZ 85012.
Our phone number is (602) 837-ROOF (7663).
We offer FREE estimates for all roofing services.

Help users with their roofing needs, answer questions, and assist with scheduling appointments.
Use the available tools to access information and perform actions when needed.`,
    enabledServers: {
      context7: true,
      supabase: true,
      github: true,
      vapi: true,
      telnyx: true,
    },
  });

  // Save settings
  const saveSettings = async () => {
    try {
      // In a real implementation, you would save the settings to a database or API
      localStorage.setItem("copilotSettings", JSON.stringify(settings));
      alert("Settings saved successfully!");
    } catch (error) {
      console.error("Error saving settings:", error);
      alert("Failed to save settings.");
    }
  };

  // Load settings
  useEffect(() => {
    try {
      const savedSettings = localStorage.getItem("copilotSettings");
      if (savedSettings) {
        setSettings(JSON.parse(savedSettings));
      }
    } catch (error) {
      console.error("Error loading settings:", error);
    }
  }, []);

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-6">Copilot Settings</h1>

      <div className="bg-white rounded-lg shadow p-6 mb-8">
        <h2 className="text-xl font-semibold mb-4">General Settings</h2>

        <div className="mb-4">
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={settings.enabled}
              onChange={(e) =>
                setSettings({ ...settings, enabled: e.target.checked })
              }
              className="mr-2"
            />
            <span>Enable Copilot</span>
          </label>
        </div>

        <div className="mb-4">
          <label className="block mb-2">Model</label>
          <select
            value={settings.model}
            onChange={(e) =>
              setSettings({ ...settings, model: e.target.value })
            }
            className="w-full p-2 border rounded"
          >
            <option value="gpt-4o">GPT-4o</option>
            <option value="gpt-4-turbo">GPT-4 Turbo</option>
            <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
          </select>
        </div>

        <div className="mb-4">
          <label className="block mb-2">System Prompt</label>
          <textarea
            value={settings.systemPrompt}
            onChange={(e) =>
              setSettings({ ...settings, systemPrompt: e.target.value })
            }
            className="w-full p-2 border rounded h-40"
          />
        </div>
      </div>

      <div className="bg-white rounded-lg shadow p-6 mb-8">
        <h2 className="text-xl font-semibold mb-4">MCP Server Settings</h2>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={settings.enabledServers.context7}
                onChange={(e) =>
                  setSettings({
                    ...settings,
                    enabledServers: {
                      ...settings.enabledServers,
                      context7: e.target.checked,
                    },
                  })
                }
                className="mr-2"
              />
              <span>Context7 (Code Context)</span>
            </label>
          </div>

          <div>
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={settings.enabledServers.supabase}
                onChange={(e) =>
                  setSettings({
                    ...settings,
                    enabledServers: {
                      ...settings.enabledServers,
                      supabase: e.target.checked,
                    },
                  })
                }
                className="mr-2"
              />
              <span>Supabase (Database)</span>
            </label>
          </div>

          <div>
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={settings.enabledServers.github}
                onChange={(e) =>
                  setSettings({
                    ...settings,
                    enabledServers: {
                      ...settings.enabledServers,
                      github: e.target.checked,
                    },
                  })
                }
                className="mr-2"
              />
              <span>GitHub (Repository)</span>
            </label>
          </div>

          <div>
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={settings.enabledServers.vapi}
                onChange={(e) =>
                  setSettings({
                    ...settings,
                    enabledServers: {
                      ...settings.enabledServers,
                      vapi: e.target.checked,
                    },
                  })
                }
                className="mr-2"
              />
              <span>VAPI (Voice)</span>
            </label>
          </div>

          <div>
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={settings.enabledServers.telnyx}
                onChange={(e) =>
                  setSettings({
                    ...settings,
                    enabledServers: {
                      ...settings.enabledServers,
                      telnyx: e.target.checked,
                    },
                  })
                }
                className="mr-2"
              />
              <span>Telnyx (Telephony)</span>
            </label>
          </div>
        </div>
      </div>

      <div className="flex justify-end mb-8">
        <button
          onClick={saveSettings}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          Save Settings
        </button>
      </div>

      <CopilotToolMonitor />
    </div>
  );
}
