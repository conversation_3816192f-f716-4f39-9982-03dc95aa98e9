import { initializeOpenRouter, getChatCompletion, OPENROUTER_MODELS, DEFAULT_MODEL } from './openrouter';

// Agent state interface
export interface AgentState {
  messages: any[];
  currentModel: string;
  thinking?: string;
}

// Initialize the agent with the default model
export function initializeAgent(model = DEFAULT_MODEL) {
  // Initialize the OpenRouter client with the specified model
  initializeOpenRouter(model);
  
  // Return the initial state
  return {
    messages: [],
    currentModel: model,
    thinking: undefined
  };
}

// Process a user message
export async function processMessage(state: AgentState, userMessage: string) {
  try {
    // Add the user message to the state
    const updatedMessages = [...state.messages, {
      role: 'user',
      content: userMessage
    }];
    
    // Get the response from the model
    const stream = await getChatCompletion(updatedMessages, state.currentModel);
    
    // Return the stream and updated state
    return {
      stream,
      state: {
        ...state,
        messages: updatedMessages
      }
    };
  } catch (error) {
    console.error('Error processing message:', error);
    throw error;
  }
}

// Change the model
export function changeModel(state: AgentState, newModel: string) {
  if (!OPENROUTER_MODELS[newModel as keyof typeof OPENROUTER_MODELS] && newModel !== DEFAULT_MODEL) {
    console.warn(`Model ${newModel} not found in available models. Using default model.`);
    newModel = DEFAULT_MODEL;
  }
  
  // Initialize the OpenRouter client with the new model
  initializeOpenRouter(newModel);
  
  // Return the updated state
  return {
    ...state,
    currentModel: newModel
  };
}

// Get available models
export function getAvailableModels() {
  return Object.keys(OPENROUTER_MODELS).map(key => ({
    id: key,
    name: OPENROUTER_MODELS[key as keyof typeof OPENROUTER_MODELS]
  }));
}
