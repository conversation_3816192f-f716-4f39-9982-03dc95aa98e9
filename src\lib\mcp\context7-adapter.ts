/**
 * Enhanced Context7 MCP Adapter
 *
 * This module provides an enhanced adapter for the Context7 MCP server
 * with full support for the universal MCP adapter interface.
 */

import { BaseMCPAdapter } from './base-adapter';
import { MCPServerCapabilities } from './mcp-server-adapter';
import { MCPTool, MCPToolInfo, MCPServerConfig } from './types';

/**
 * Enhanced Context7 MCP Server Adapter
 */
export class Context7Adapter extends BaseMCPAdapter {
  private apiKey: string = '';
  private baseUrl: string = '';

  constructor() {
    super(
      'context7',
      'Context7 Code Context Server',
      '1.0.0',
      {
        supportsStreaming: false,
        supportsAuthentication: true,
        supportsBatching: false,
        maxConcurrentRequests: 10,
        rateLimit: {
          requests: 100,
          window: 60000 // 1 minute
        }
      }
    );
  }

  /**
   * Perform adapter-specific initialization
   */
  protected async doInitialize(): Promise<void> {
    this.apiKey = this.config.apiKey || process.env.CONTEXT7_API_KEY || '';
    this.baseUrl = this.config.endpoint || 'https://api.context7.com';

    if (!this.apiKey) {
      console.warn('Context7 API key not provided, using mock responses');
    }
  }

  /**
   * Perform adapter-specific shutdown
   */
  protected async doShutdown(): Promise<void> {
    // Cleanup any resources if needed
  }

  /**
   * Load tools from the Context7 server
   */
  protected async loadTools(): Promise<void> {
    // Define the tools provided by Context7
    this.tools = [
      {
        name: 'context7_search_code',
        description: 'Search for code in the codebase using natural language queries',
        serverId: this.id,
        category: 'code-analysis',
        tags: ['search', 'code', 'semantic'],
        priority: 1
      },
      {
        name: 'context7_get_file_content',
        description: 'Get the content of a file in the codebase',
        serverId: this.id,
        category: 'file-operations',
        tags: ['file', 'content', 'read'],
        priority: 2
      },
      {
        name: 'context7_get_documentation',
        description: 'Get documentation for a library or package',
        serverId: this.id,
        category: 'documentation',
        tags: ['docs', 'library', 'reference'],
        priority: 3
      },
      {
        name: 'context7_analyze_code',
        description: 'Analyze code structure and dependencies',
        serverId: this.id,
        category: 'code-analysis',
        tags: ['analysis', 'structure', 'dependencies'],
        priority: 2
      },
      {
        name: 'context7_find_similar',
        description: 'Find similar code patterns or implementations',
        serverId: this.id,
        category: 'code-analysis',
        tags: ['similarity', 'patterns', 'matching'],
        priority: 3
      }
    ];

    // Define detailed tool info for each tool
    this.toolInfoMap.set('context7_search_code', {
      name: 'context7_search_code',
      description: 'Search for code in the codebase using natural language queries with semantic understanding',
      serverId: this.id,
      category: 'code-analysis',
      tags: ['search', 'code', 'semantic'],
      parameters: {
        query: {
          type: 'string',
          description: 'The natural language query to search for code',
          required: true,
          minLength: 3,
          maxLength: 500
        },
        limit: {
          type: 'number',
          description: 'The maximum number of results to return',
          required: false,
          default: 5,
          minimum: 1,
          maximum: 50
        },
        fileTypes: {
          type: 'string',
          description: 'Comma-separated list of file extensions to search (e.g., "ts,js,tsx")',
          required: false
        },
        includeTests: {
          type: 'boolean',
          description: 'Whether to include test files in the search',
          required: false,
          default: false
        }
      },
      examples: [
        {
          description: 'Search for authentication logic',
          parameters: { query: 'user authentication login', limit: 10 },
          expectedResult: { results: [], totalCount: 0 }
        }
      ],
      timeout: 10000,
      retryPolicy: {
        maxRetries: 2,
        backoffMultiplier: 1.5,
        initialDelay: 1000
      }
    });

    this.toolInfoMap.set('context7_get_file_content', {
      name: 'context7_get_file_content',
      description: 'Get the complete content of a file in the codebase with syntax highlighting',
      serverId: this.id,
      category: 'file-operations',
      tags: ['file', 'content', 'read'],
      parameters: {
        filePath: {
          type: 'string',
          description: 'The relative path to the file from the project root',
          required: true,
          pattern: '^[^/].*$' // Should not start with /
        },
        includeMetadata: {
          type: 'boolean',
          description: 'Whether to include file metadata (size, modified date, etc.)',
          required: false,
          default: false
        }
      },
      examples: [
        {
          description: 'Get content of a TypeScript file',
          parameters: { filePath: 'src/components/Button.tsx', includeMetadata: true }
        }
      ],
      timeout: 5000
    });

    this.toolInfoMap.set('context7_get_documentation', {
      name: 'context7_get_documentation',
      description: 'Get comprehensive documentation for a library, package, or API',
      serverId: this.id,
      category: 'documentation',
      tags: ['docs', 'library', 'reference'],
      parameters: {
        libraryName: {
          type: 'string',
          description: 'The name of the library or package',
          required: true,
          minLength: 1,
          maxLength: 100
        },
        topic: {
          type: 'string',
          description: 'The specific topic or section to get documentation for',
          required: false,
          maxLength: 200
        },
        version: {
          type: 'string',
          description: 'Specific version of the library (if applicable)',
          required: false
        },
        format: {
          type: 'string',
          description: 'Preferred documentation format',
          required: false,
          enum: ['markdown', 'html', 'text'],
          default: 'markdown'
        }
      },
      examples: [
        {
          description: 'Get React documentation for hooks',
          parameters: { libraryName: 'react', topic: 'hooks', format: 'markdown' }
        }
      ],
      timeout: 15000
    });

    this.toolInfoMap.set('context7_analyze_code', {
      name: 'context7_analyze_code',
      description: 'Analyze code structure, dependencies, and complexity metrics',
      serverId: this.id,
      category: 'code-analysis',
      tags: ['analysis', 'structure', 'dependencies'],
      parameters: {
        filePath: {
          type: 'string',
          description: 'Path to the file or directory to analyze',
          required: true
        },
        analysisType: {
          type: 'string',
          description: 'Type of analysis to perform',
          required: false,
          enum: ['structure', 'dependencies', 'complexity', 'all'],
          default: 'all'
        },
        includeSubdirectories: {
          type: 'boolean',
          description: 'Whether to analyze subdirectories recursively',
          required: false,
          default: false
        }
      },
      timeout: 20000
    });

    this.toolInfoMap.set('context7_find_similar', {
      name: 'context7_find_similar',
      description: 'Find similar code patterns, functions, or implementations',
      serverId: this.id,
      category: 'code-analysis',
      tags: ['similarity', 'patterns', 'matching'],
      parameters: {
        codeSnippet: {
          type: 'string',
          description: 'The code snippet to find similar patterns for',
          required: true,
          minLength: 10,
          maxLength: 5000
        },
        similarityThreshold: {
          type: 'number',
          description: 'Minimum similarity score (0.0 to 1.0)',
          required: false,
          default: 0.7,
          minimum: 0.0,
          maximum: 1.0
        },
        maxResults: {
          type: 'number',
          description: 'Maximum number of similar patterns to return',
          required: false,
          default: 10,
          minimum: 1,
          maximum: 100
        }
      },
      timeout: 15000
    });
  }

  /**
   * Perform a health check
   */
  protected async performHealthCheck(): Promise<void> {
    // Simple health check - try to search for a basic query
    if (this.apiKey) {
      // In a real implementation, this would make an actual API call
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }

  /**
   * Handle configuration updates
   */
  protected async doConfigUpdate(oldConfig: MCPServerConfig, newConfig: MCPServerConfig): Promise<void> {
    if (oldConfig.apiKey !== newConfig.apiKey) {
      this.apiKey = newConfig.apiKey || '';
    }

    if (oldConfig.endpoint !== newConfig.endpoint) {
      this.baseUrl = newConfig.endpoint || 'https://api.context7.com';
    }
  }

  /**
   * Execute the actual tool call
   */
  protected async doCallTool(toolName: string, params: Record<string, any>, context?: {
    agentId?: string;
    conversationId?: string;
    timeout?: number;
  }): Promise<any> {
    switch (toolName) {
      case 'context7_search_code':
        return await this.searchCode(params.query, params.limit, params.fileTypes, params.includeTests);
      case 'context7_get_file_content':
        return await this.getFileContent(params.filePath, params.includeMetadata);
      case 'context7_get_documentation':
        return await this.getDocumentation(params.libraryName, params.topic, params.version, params.format);
      case 'context7_analyze_code':
        return await this.analyzeCode(params.filePath, params.analysisType, params.includeSubdirectories);
      case 'context7_find_similar':
        return await this.findSimilar(params.codeSnippet, params.similarityThreshold, params.maxResults);
      default:
        throw new Error(`Unknown tool: ${toolName}`);
    }
  }

  /**
   * Search for code in the codebase
   */
  private async searchCode(query: string, limit: number = 5, fileTypes?: string, includeTests: boolean = false): Promise<any> {
    console.log(`Context7: Searching for code with query: "${query}", limit: ${limit}, fileTypes: ${fileTypes}, includeTests: ${includeTests}`);

    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 200 + Math.random() * 300));

    // Mock response with more realistic data
    return {
      query,
      results: [
        {
          filePath: 'src/lib/mcp/mcp-tool-registry.ts',
          content: `// MCP Tool Registry implementation
export class MCPToolRegistry {
  private static instance: MCPToolRegistry;
  private adapters: Map<string, MCPServerAdapter> = new Map();

  public static getInstance(): MCPToolRegistry {
    if (!MCPToolRegistry.instance) {
      MCPToolRegistry.instance = new MCPToolRegistry();
    }
    return MCPToolRegistry.instance;
  }
}`,
          relevance: 0.95,
          lineNumbers: [1, 15],
          language: 'typescript'
        },
        {
          filePath: 'src/lib/agents/agent-manager.ts',
          content: `// Agent Manager implementation
export class AgentManager {
  private configs: AgentSettings[] = [];
  private activeMCPServers: Set<string> = new Set();

  public async initialize(configs: AgentSettings[]): Promise<void> {
    this.configs = configs;
    console.log('Agent Manager initialized with', configs.length, 'agents');
  }
}`,
          relevance: 0.85,
          lineNumbers: [20, 35],
          language: 'typescript'
        }
      ],
      totalCount: 2,
      searchTime: Math.random() * 500 + 100,
      filters: {
        fileTypes: fileTypes?.split(',') || [],
        includeTests
      }
    };
  }

  /**
   * Get the content of a file
   */
  private async getFileContent(filePath: string, includeMetadata: boolean = false): Promise<any> {
    console.log(`Context7: Getting file content for: ${filePath}, includeMetadata: ${includeMetadata}`);

    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 100 + Math.random() * 200));

    const content = `// Mock content for ${filePath}
// This is a simulated file content response from Context7

export interface MockInterface {
  id: string;
  name: string;
  description?: string;
}

export class MockClass implements MockInterface {
  constructor(
    public id: string,
    public name: string,
    public description?: string
  ) {}

  public toString(): string {
    return \`\${this.name} (\${this.id})\`;
  }
}`;

    const result: any = {
      filePath,
      content,
      language: this.getLanguageFromPath(filePath),
      lineCount: content.split('\n').length
    };

    if (includeMetadata) {
      result.metadata = {
        size: content.length,
        lastModified: new Date().toISOString(),
        encoding: 'utf-8',
        permissions: 'read-only'
      };
    }

    return result;
  }

  /**
   * Get documentation for a library
   */
  private async getDocumentation(libraryName: string, topic?: string, version?: string, format: string = 'markdown'): Promise<any> {
    console.log(`Context7: Getting documentation for: ${libraryName}, topic: ${topic || 'general'}, version: ${version || 'latest'}, format: ${format}`);

    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 300 + Math.random() * 500));

    const documentation = `# ${libraryName} Documentation${version ? ` (v${version})` : ''}

${topic ? `## ${topic}\n\n` : ''}This is comprehensive documentation for ${libraryName}.

## Overview
${libraryName} is a powerful library that provides various functionality for modern applications.

## Installation
\`\`\`bash
npm install ${libraryName}
\`\`\`

## Basic Usage
\`\`\`javascript
import { ${libraryName} } from '${libraryName}';

const instance = new ${libraryName}();
instance.initialize();
\`\`\`

## API Reference
- \`initialize()\` - Initialize the library
- \`configure(options)\` - Configure the library with options
- \`destroy()\` - Clean up resources

## Examples
See the examples directory for more detailed usage examples.`;

    return {
      libraryName,
      topic: topic || 'general',
      version: version || 'latest',
      format,
      documentation,
      sections: ['Overview', 'Installation', 'Basic Usage', 'API Reference', 'Examples'],
      lastUpdated: new Date().toISOString()
    };
  }

  /**
   * Analyze code structure and dependencies
   */
  private async analyzeCode(filePath: string, analysisType: string = 'all', includeSubdirectories: boolean = false): Promise<any> {
    console.log(`Context7: Analyzing code at: ${filePath}, type: ${analysisType}, includeSubdirectories: ${includeSubdirectories}`);

    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500 + Math.random() * 1000));

    return {
      filePath,
      analysisType,
      includeSubdirectories,
      results: {
        structure: {
          classes: 3,
          interfaces: 2,
          functions: 15,
          variables: 8,
          imports: 12,
          exports: 5
        },
        dependencies: [
          { name: 'react', version: '^18.0.0', type: 'production' },
          { name: 'typescript', version: '^5.0.0', type: 'development' },
          { name: '@types/node', version: '^20.0.0', type: 'development' }
        ],
        complexity: {
          cyclomaticComplexity: 12,
          cognitiveComplexity: 8,
          maintainabilityIndex: 85,
          linesOfCode: 245,
          technicalDebt: 'low'
        },
        issues: [
          { type: 'warning', message: 'Unused import detected', line: 5 },
          { type: 'info', message: 'Consider extracting this function', line: 42 }
        ]
      },
      analyzedAt: new Date().toISOString()
    };
  }

  /**
   * Find similar code patterns
   */
  private async findSimilar(codeSnippet: string, similarityThreshold: number = 0.7, maxResults: number = 10): Promise<any> {
    console.log(`Context7: Finding similar code patterns, threshold: ${similarityThreshold}, maxResults: ${maxResults}`);

    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 400 + Math.random() * 600));

    return {
      codeSnippet,
      similarityThreshold,
      maxResults,
      matches: [
        {
          filePath: 'src/components/Button.tsx',
          similarity: 0.92,
          matchedLines: [15, 25],
          context: 'React component with similar prop handling'
        },
        {
          filePath: 'src/utils/helpers.ts',
          similarity: 0.78,
          matchedLines: [8, 12],
          context: 'Utility function with similar logic pattern'
        }
      ],
      searchTime: Math.random() * 800 + 200,
      totalScanned: 1247
    };
  }

  /**
   * Get programming language from file path
   */
  private getLanguageFromPath(filePath: string): string {
    const extension = filePath.split('.').pop()?.toLowerCase();
    const languageMap: Record<string, string> = {
      'ts': 'typescript',
      'tsx': 'typescript',
      'js': 'javascript',
      'jsx': 'javascript',
      'py': 'python',
      'java': 'java',
      'cpp': 'cpp',
      'c': 'c',
      'cs': 'csharp',
      'go': 'go',
      'rs': 'rust',
      'php': 'php',
      'rb': 'ruby',
      'swift': 'swift',
      'kt': 'kotlin'
    };
    return languageMap[extension || ''] || 'text';
  }
}
