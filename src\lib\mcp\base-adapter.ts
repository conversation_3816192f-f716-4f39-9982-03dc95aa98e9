/**
 * Base MCP Adapter
 * 
 * This module provides a base implementation for MCP server adapters
 * with common functionality like health checking, statistics, and event handling.
 */

import EventEmitter from 'eventemitter3';
import { MCPServerAdapter, MCPServerHealth, MCPServerCapabilities } from './mcp-server-adapter';
import { MCPTool, MCPToolInfo, MCPServerConfig, MCPToolResult } from './types';

/**
 * Base MCP Adapter class that provides common functionality
 */
export abstract class BaseMCPAdapter extends EventEmitter implements MCPServerAdapter {
  protected config: MCPServerConfig;
  protected tools: MCPTool[] = [];
  protected toolInfoMap: Map<string, MCPToolInfo> = new Map();
  protected toolStats: Map<string, {
    callCount: number;
    successCount: number;
    errorCount: number;
    totalResponseTime: number;
    lastUsed: number;
  }> = new Map();
  protected health: MCPServerHealth;
  protected initialized: boolean = false;
  protected healthCheckInterval?: NodeJS.Timeout;

  constructor(
    public readonly id: string,
    public readonly name: string,
    public readonly version: string,
    public readonly capabilities: MCPServerCapabilities
  ) {
    super();
    this.health = {
      status: 'unhealthy',
      lastCheck: 0,
      responseTime: 0,
      errorCount: 0,
      uptime: 0
    };
  }

  /**
   * Initialize the adapter with configuration
   */
  async initialize(config: MCPServerConfig): Promise<void> {
    this.config = config;
    
    try {
      // Perform adapter-specific initialization
      await this.doInitialize();
      
      // Load tools
      await this.loadTools();
      
      // Start health monitoring if enabled
      if (config.healthCheck?.enabled) {
        this.startHealthMonitoring();
      }
      
      this.initialized = true;
      this.health.status = 'healthy';
      this.health.uptime = Date.now();
      
      this.emit('config-updated', { config });
      
      console.log(`MCP Adapter ${this.name} (${this.id}) initialized successfully`);
    } catch (error) {
      this.health.status = 'unhealthy';
      this.emit('error', { error, adapter: this.id });
      throw error;
    }
  }

  /**
   * Shutdown the adapter and cleanup resources
   */
  async shutdown(): Promise<void> {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
    }
    
    await this.doShutdown();
    
    this.initialized = false;
    this.removeAllListeners();
    
    console.log(`MCP Adapter ${this.name} (${this.id}) shutdown successfully`);
  }

  /**
   * Check if the adapter is initialized and ready
   */
  isReady(): boolean {
    return this.initialized && this.health.status !== 'unhealthy';
  }

  /**
   * Get current health status
   */
  async getHealth(): Promise<MCPServerHealth> {
    if (!this.initialized) {
      return {
        status: 'unhealthy',
        lastCheck: Date.now(),
        responseTime: 0,
        errorCount: this.health.errorCount,
        uptime: 0
      };
    }

    try {
      const startTime = Date.now();
      await this.performHealthCheck();
      const responseTime = Date.now() - startTime;
      
      this.health = {
        status: responseTime < 1000 ? 'healthy' : 'degraded',
        lastCheck: Date.now(),
        responseTime,
        errorCount: this.health.errorCount,
        uptime: Date.now() - (this.health.uptime || Date.now())
      };
    } catch (error) {
      this.health.status = 'unhealthy';
      this.health.errorCount++;
      this.health.lastCheck = Date.now();
    }

    return this.health;
  }

  /**
   * Get server configuration
   */
  getConfig(): MCPServerConfig {
    return { ...this.config };
  }

  /**
   * Update server configuration (hot reload)
   */
  async updateConfig(config: Partial<MCPServerConfig>): Promise<void> {
    const oldConfig = this.config;
    this.config = { ...this.config, ...config };
    
    try {
      await this.doConfigUpdate(oldConfig, this.config);
      this.emit('config-updated', { config: this.config, oldConfig });
    } catch (error) {
      this.config = oldConfig; // Rollback on error
      throw error;
    }
  }

  /**
   * Get all tools provided by this adapter
   */
  getTools(): MCPTool[] {
    return [...this.tools];
  }

  /**
   * Get tool info for a specific tool
   */
  getToolInfo(toolName: string): MCPToolInfo | undefined {
    return this.toolInfoMap.get(toolName);
  }

  /**
   * Validate tool parameters before execution
   */
  async validateToolParameters(toolName: string, params: Record<string, any>): Promise<{
    valid: boolean;
    errors?: string[];
  }> {
    const toolInfo = this.getToolInfo(toolName);
    if (!toolInfo) {
      return { valid: false, errors: [`Tool ${toolName} not found`] };
    }

    const errors: string[] = [];
    
    if (toolInfo.parameters) {
      // Check required parameters
      for (const [paramName, paramInfo] of Object.entries(toolInfo.parameters)) {
        if (paramInfo.required && !(paramName in params)) {
          errors.push(`Required parameter '${paramName}' is missing`);
        }
        
        if (paramName in params) {
          const value = params[paramName];
          
          // Type validation
          if (paramInfo.type === 'string' && typeof value !== 'string') {
            errors.push(`Parameter '${paramName}' must be a string`);
          } else if (paramInfo.type === 'number' && typeof value !== 'number') {
            errors.push(`Parameter '${paramName}' must be a number`);
          } else if (paramInfo.type === 'boolean' && typeof value !== 'boolean') {
            errors.push(`Parameter '${paramName}' must be a boolean`);
          }
          
          // Additional validations
          if (paramInfo.enum && !paramInfo.enum.includes(value)) {
            errors.push(`Parameter '${paramName}' must be one of: ${paramInfo.enum.join(', ')}`);
          }
          
          if (paramInfo.pattern && typeof value === 'string' && !new RegExp(paramInfo.pattern).test(value)) {
            errors.push(`Parameter '${paramName}' does not match required pattern`);
          }
          
          if (paramInfo.minimum !== undefined && typeof value === 'number' && value < paramInfo.minimum) {
            errors.push(`Parameter '${paramName}' must be at least ${paramInfo.minimum}`);
          }
          
          if (paramInfo.maximum !== undefined && typeof value === 'number' && value > paramInfo.maximum) {
            errors.push(`Parameter '${paramName}' must be at most ${paramInfo.maximum}`);
          }
        }
      }
    }

    return { valid: errors.length === 0, errors: errors.length > 0 ? errors : undefined };
  }

  /**
   * Call a tool with the given parameters
   */
  async callTool(toolName: string, params: Record<string, any>, context?: {
    agentId?: string;
    conversationId?: string;
    timeout?: number;
  }): Promise<MCPToolResult> {
    const startTime = Date.now();
    const toolInfo = this.getToolInfo(toolName);
    
    if (!toolInfo) {
      return {
        success: false,
        error: {
          code: 'TOOL_NOT_FOUND',
          message: `Tool ${toolName} not found`
        },
        metadata: {
          executionTime: Date.now() - startTime,
          serverId: this.id,
          toolName,
          timestamp: Date.now()
        }
      };
    }

    // Validate parameters
    const validation = await this.validateToolParameters(toolName, params);
    if (!validation.valid) {
      return {
        success: false,
        error: {
          code: 'INVALID_PARAMETERS',
          message: 'Parameter validation failed',
          details: validation.errors
        },
        metadata: {
          executionTime: Date.now() - startTime,
          serverId: this.id,
          toolName,
          timestamp: Date.now()
        }
      };
    }

    try {
      // Update statistics
      this.updateToolStats(toolName, 'call');
      
      // Call the actual tool implementation
      const result = await this.doCallTool(toolName, params, context);
      
      // Update success statistics
      this.updateToolStats(toolName, 'success');
      
      const executionTime = Date.now() - startTime;
      
      // Emit tool called event
      this.emit('tool-called', {
        toolName,
        params,
        result,
        executionTime,
        success: true,
        context
      });
      
      return {
        success: true,
        data: result,
        metadata: {
          executionTime,
          serverId: this.id,
          toolName,
          timestamp: Date.now()
        }
      };
    } catch (error) {
      // Update error statistics
      this.updateToolStats(toolName, 'error');
      this.health.errorCount++;
      
      const executionTime = Date.now() - startTime;
      
      // Emit error event
      this.emit('error', {
        toolName,
        params,
        error,
        executionTime,
        context
      });
      
      return {
        success: false,
        error: {
          code: 'EXECUTION_ERROR',
          message: error instanceof Error ? error.message : String(error),
          details: error
        },
        metadata: {
          executionTime,
          serverId: this.id,
          toolName,
          timestamp: Date.now()
        }
      };
    }
  }

  /**
   * Get tool usage statistics
   */
  getToolStats(): Record<string, {
    callCount: number;
    successCount: number;
    errorCount: number;
    averageResponseTime: number;
    lastUsed: number;
  }> {
    const stats: Record<string, any> = {};
    
    for (const [toolName, toolStat] of this.toolStats.entries()) {
      stats[toolName] = {
        callCount: toolStat.callCount,
        successCount: toolStat.successCount,
        errorCount: toolStat.errorCount,
        averageResponseTime: toolStat.callCount > 0 ? toolStat.totalResponseTime / toolStat.callCount : 0,
        lastUsed: toolStat.lastUsed
      };
    }
    
    return stats;
  }

  /**
   * Reset tool statistics
   */
  resetToolStats(): void {
    this.toolStats.clear();
  }

  // Abstract methods that must be implemented by concrete adapters

  /**
   * Perform adapter-specific initialization
   */
  protected abstract doInitialize(): Promise<void>;

  /**
   * Perform adapter-specific shutdown
   */
  protected abstract doShutdown(): Promise<void>;

  /**
   * Load tools from the MCP server
   */
  protected abstract loadTools(): Promise<void>;

  /**
   * Perform a health check
   */
  protected abstract performHealthCheck(): Promise<void>;

  /**
   * Handle configuration updates
   */
  protected abstract doConfigUpdate(oldConfig: MCPServerConfig, newConfig: MCPServerConfig): Promise<void>;

  /**
   * Execute the actual tool call
   */
  protected abstract doCallTool(toolName: string, params: Record<string, any>, context?: {
    agentId?: string;
    conversationId?: string;
    timeout?: number;
  }): Promise<any>;

  // Helper methods

  /**
   * Update tool statistics
   */
  private updateToolStats(toolName: string, type: 'call' | 'success' | 'error'): void {
    if (!this.toolStats.has(toolName)) {
      this.toolStats.set(toolName, {
        callCount: 0,
        successCount: 0,
        errorCount: 0,
        totalResponseTime: 0,
        lastUsed: 0
      });
    }
    
    const stats = this.toolStats.get(toolName)!;
    
    switch (type) {
      case 'call':
        stats.callCount++;
        stats.lastUsed = Date.now();
        break;
      case 'success':
        stats.successCount++;
        break;
      case 'error':
        stats.errorCount++;
        break;
    }
  }

  /**
   * Start health monitoring
   */
  private startHealthMonitoring(): void {
    const interval = this.config.healthCheck?.interval || 30000; // 30 seconds default
    
    this.healthCheckInterval = setInterval(async () => {
      try {
        await this.getHealth();
        this.emit('health-changed', this.health);
      } catch (error) {
        console.error(`Health check failed for adapter ${this.id}:`, error);
      }
    }, interval);
  }
}
