/**
 * Enhanced Agent Manager with Memory and Collaboration
 * 
 * This module provides an enhanced agent management system with:
 * - Agent memory and context persistence
 * - Agent collaboration capabilities
 * - Intelligent tool assignment
 * - Performance optimization
 */

import EventEmitter from 'eventemitter3';
import { AgentSettings } from '../../types';
import { MCPToolRegistry } from '../mcp/mcp-tool-registry';
import { 
  AgentMemoryEntry, 
  AgentCollaborationRequest, 
  AgentCollaborationResponse,
  AgentToolAssignment 
} from '../mcp/types';

/**
 * Agent Context for maintaining conversation state
 */
interface AgentContext {
  agentId: string;
  conversationId: string;
  memory: AgentMemoryEntry[];
  activeTools: string[];
  collaborations: Map<string, AgentCollaborationRequest>;
  performance: {
    totalQueries: number;
    successfulQueries: number;
    averageResponseTime: number;
    lastActivity: number;
  };
}

/**
 * Enhanced Agent Manager
 */
export class EnhancedAgentManager extends EventEmitter {
  private static instance: EnhancedAgentManager;
  private configs: AgentSettings[] = [];
  private contexts: Map<string, AgentContext> = new Map();
  private toolAssignments: Map<string, AgentToolAssignment[]> = new Map();
  private collaborationQueue: AgentCollaborationRequest[] = [];
  private mcpRegistry: MCPToolRegistry;
  private memoryCleanupInterval?: NodeJS.Timeout;
  private performanceMonitorInterval?: NodeJS.Timeout;

  /**
   * Get the singleton instance
   */
  static getInstance(): EnhancedAgentManager {
    if (!EnhancedAgentManager.instance) {
      EnhancedAgentManager.instance = new EnhancedAgentManager();
    }
    return EnhancedAgentManager.instance;
  }

  constructor() {
    super();
    this.mcpRegistry = MCPToolRegistry.getInstance();
  }

  /**
   * Initialize the enhanced agent manager
   */
  async initialize(configs: AgentSettings[]): Promise<void> {
    this.configs = configs;
    
    // Initialize agent contexts
    for (const config of configs) {
      if (config.enabled) {
        this.initializeAgentContext(config.id);
      }
    }

    // Assign tools to agents based on their specialization
    await this.assignToolsToAgents();

    // Start memory cleanup
    this.startMemoryCleanup();

    // Start performance monitoring
    this.startPerformanceMonitoring();

    console.log(`Enhanced Agent Manager initialized with ${configs.length} agents`);
    this.emit('initialized', { agentCount: configs.length });
  }

  /**
   * Process a message with enhanced context and collaboration
   */
  async processMessage(message: string, agentId?: string, conversationId?: string): Promise<{
    response: string;
    agentId: string;
    toolsUsed: string[];
    collaborations: string[];
    confidence: number;
  }> {
    const startTime = Date.now();
    
    // Select the best agent for this message
    const selectedAgentId = agentId || await this.selectBestAgent(message);
    const selectedAgent = this.getAgentConfig(selectedAgentId);
    
    if (!selectedAgent) {
      throw new Error(`Agent not found: ${selectedAgentId}`);
    }

    // Get or create conversation context
    const contextId = conversationId || `conv_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const context = this.getOrCreateContext(selectedAgentId, contextId);

    // Store the user message in memory
    this.addToMemory(selectedAgentId, contextId, {
      type: 'context',
      content: { role: 'user', message },
      importance: 0.8,
      tags: ['user-input']
    });

    // Analyze message for tool requirements
    const requiredTools = await this.analyzeToolRequirements(message, selectedAgentId);
    
    // Check if collaboration is needed
    const collaborationNeeded = await this.checkCollaborationNeeds(message, selectedAgentId, requiredTools);
    const collaborations: string[] = [];

    let response = '';
    let toolsUsed: string[] = [];
    let confidence = 0.8;

    try {
      // Handle collaborations if needed
      if (collaborationNeeded.length > 0) {
        const collaborationResults = await this.handleCollaborations(collaborationNeeded, message, contextId);
        collaborations.push(...collaborationResults.map(r => r.respondingAgentId));
        
        // Incorporate collaboration results into context
        for (const result of collaborationResults) {
          this.addToMemory(selectedAgentId, contextId, {
            type: 'tool-result',
            content: result.data,
            importance: 0.9,
            tags: ['collaboration', result.respondingAgentId]
          });
        }
      }

      // Use tools to process the message
      if (requiredTools.length > 0) {
        const toolResults = await this.executeTools(requiredTools, message, selectedAgentId, contextId);
        toolsUsed = toolResults.map(r => r.toolName);
        
        // Store tool results in memory
        for (const result of toolResults) {
          this.addToMemory(selectedAgentId, contextId, {
            type: 'tool-result',
            content: result.data,
            importance: 0.7,
            tags: ['tool-result', result.toolName]
          });
        }
      }

      // Generate response using agent's context and memory
      response = await this.generateResponse(selectedAgentId, message, context, toolsUsed);
      
      // Store the response in memory
      this.addToMemory(selectedAgentId, contextId, {
        type: 'context',
        content: { role: 'assistant', message: response },
        importance: 0.8,
        tags: ['assistant-response']
      });

      // Update performance metrics
      this.updatePerformanceMetrics(selectedAgentId, Date.now() - startTime, true);

    } catch (error) {
      console.error(`Error processing message with agent ${selectedAgentId}:`, error);
      response = `I encountered an error while processing your request: ${error instanceof Error ? error.message : String(error)}`;
      confidence = 0.1;
      
      // Update performance metrics for failure
      this.updatePerformanceMetrics(selectedAgentId, Date.now() - startTime, false);
    }

    return {
      response,
      agentId: selectedAgentId,
      toolsUsed,
      collaborations,
      confidence
    };
  }

  /**
   * Get agent configuration
   */
  getAgentConfig(agentId: string): AgentSettings | undefined {
    return this.configs.find(config => config.id === agentId);
  }

  /**
   * Get all agent configurations
   */
  getAllAgentConfigs(): AgentSettings[] {
    return [...this.configs];
  }

  /**
   * Get agent memory for a conversation
   */
  getAgentMemory(agentId: string, conversationId: string): AgentMemoryEntry[] {
    const context = this.contexts.get(`${agentId}:${conversationId}`);
    return context ? [...context.memory] : [];
  }

  /**
   * Get agent performance metrics
   */
  getAgentPerformance(agentId: string): any {
    const contexts = Array.from(this.contexts.values()).filter(ctx => ctx.agentId === agentId);
    
    if (contexts.length === 0) {
      return null;
    }

    const totalQueries = contexts.reduce((sum, ctx) => sum + ctx.performance.totalQueries, 0);
    const successfulQueries = contexts.reduce((sum, ctx) => sum + ctx.performance.successfulQueries, 0);
    const avgResponseTime = contexts.reduce((sum, ctx) => sum + ctx.performance.averageResponseTime, 0) / contexts.length;
    const lastActivity = Math.max(...contexts.map(ctx => ctx.performance.lastActivity));

    return {
      totalQueries,
      successfulQueries,
      successRate: totalQueries > 0 ? successfulQueries / totalQueries : 0,
      averageResponseTime: avgResponseTime,
      lastActivity,
      activeConversations: contexts.length
    };
  }

  /**
   * Initialize agent context
   */
  private initializeAgentContext(agentId: string): void {
    // Agent contexts are created per conversation, not per agent
    // This method sets up agent-specific tool assignments
    this.assignToolsToAgent(agentId);
  }

  /**
   * Get or create conversation context
   */
  private getOrCreateContext(agentId: string, conversationId: string): AgentContext {
    const contextKey = `${agentId}:${conversationId}`;
    
    if (!this.contexts.has(contextKey)) {
      const context: AgentContext = {
        agentId,
        conversationId,
        memory: [],
        activeTools: this.getAgentTools(agentId),
        collaborations: new Map(),
        performance: {
          totalQueries: 0,
          successfulQueries: 0,
          averageResponseTime: 0,
          lastActivity: Date.now()
        }
      };
      
      this.contexts.set(contextKey, context);
    }

    return this.contexts.get(contextKey)!;
  }

  /**
   * Add entry to agent memory
   */
  private addToMemory(agentId: string, conversationId: string, entry: Omit<AgentMemoryEntry, 'id' | 'agentId' | 'conversationId' | 'timestamp'>): void {
    const context = this.getOrCreateContext(agentId, conversationId);
    
    const memoryEntry: AgentMemoryEntry = {
      id: `mem_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      agentId,
      conversationId,
      timestamp: Date.now(),
      ...entry
    };

    context.memory.push(memoryEntry);
    
    // Keep memory size manageable (last 100 entries)
    if (context.memory.length > 100) {
      context.memory = context.memory.slice(-100);
    }
  }

  /**
   * Select the best agent for a message
   */
  private async selectBestAgent(message: string): Promise<string> {
    // Simple agent selection based on keywords and specialization
    const messageLower = message.toLowerCase();
    
    // Check for code-related keywords
    if (messageLower.includes('code') || messageLower.includes('function') || messageLower.includes('class') || 
        messageLower.includes('bug') || messageLower.includes('debug') || messageLower.includes('implement')) {
      const generalAgent = this.configs.find(config => config.id === 'general-agent' && config.enabled);
      if (generalAgent) return generalAgent.id;
    }
    
    // Check for documentation keywords
    if (messageLower.includes('documentation') || messageLower.includes('docs') || messageLower.includes('guide') ||
        messageLower.includes('how to') || messageLower.includes('tutorial')) {
      const ragAgent = this.configs.find(config => config.id === 'rag-agent' && config.enabled);
      if (ragAgent) return ragAgent.id;
    }
    
    // Check for voice/call keywords
    if (messageLower.includes('call') || messageLower.includes('phone') || messageLower.includes('voice') ||
        messageLower.includes('appointment') || messageLower.includes('schedule')) {
      const voiceAgent = this.configs.find(config => config.id === 'voice-agent' && config.enabled);
      if (voiceAgent) return voiceAgent.id;
    }
    
    // Default to first enabled agent
    const defaultAgent = this.configs.find(config => config.enabled);
    return defaultAgent?.id || 'general-agent';
  }

  /**
   * Analyze tool requirements for a message
   */
  private async analyzeToolRequirements(message: string, agentId: string): Promise<string[]> {
    const messageLower = message.toLowerCase();
    const agentTools = this.getAgentTools(agentId);
    const requiredTools: string[] = [];

    // Check for code search requirements
    if (messageLower.includes('search') && (messageLower.includes('code') || messageLower.includes('function'))) {
      if (agentTools.includes('context7_search_code')) {
        requiredTools.push('context7_search_code');
      }
    }

    // Check for file content requirements
    if (messageLower.includes('file') && (messageLower.includes('content') || messageLower.includes('show'))) {
      if (agentTools.includes('context7_get_file_content')) {
        requiredTools.push('context7_get_file_content');
      }
    }

    // Check for documentation requirements
    if (messageLower.includes('documentation') || messageLower.includes('docs')) {
      if (agentTools.includes('context7_get_documentation')) {
        requiredTools.push('context7_get_documentation');
      }
    }

    return requiredTools;
  }

  /**
   * Check if collaboration with other agents is needed
   */
  private async checkCollaborationNeeds(message: string, agentId: string, requiredTools: string[]): Promise<AgentCollaborationRequest[]> {
    const collaborations: AgentCollaborationRequest[] = [];
    
    // If the current agent doesn't have required tools, find agents that do
    const agentTools = this.getAgentTools(agentId);
    const missingTools = requiredTools.filter(tool => !agentTools.includes(tool));
    
    for (const tool of missingTools) {
      const capableAgent = this.findAgentWithTool(tool);
      if (capableAgent && capableAgent !== agentId) {
        collaborations.push({
          id: `collab_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          requestingAgentId: agentId,
          targetAgentId: capableAgent,
          task: `Use tool ${tool} to help with: ${message}`,
          context: { originalMessage: message, requiredTool: tool },
          priority: 'medium',
          expectedResponse: 'data'
        });
      }
    }
    
    return collaborations;
  }

  /**
   * Handle agent collaborations
   */
  private async handleCollaborations(requests: AgentCollaborationRequest[], originalMessage: string, conversationId: string): Promise<AgentCollaborationResponse[]> {
    const responses: AgentCollaborationResponse[] = [];
    
    for (const request of requests) {
      try {
        // For now, simulate collaboration by calling the tool directly
        const tool = request.context.requiredTool;
        const result = await this.mcpRegistry.callTool(tool, { query: originalMessage }, request.targetAgentId, {
          conversationId,
          query: originalMessage,
          agentType: request.targetAgentId
        });
        
        responses.push({
          requestId: request.id,
          respondingAgentId: request.targetAgentId,
          success: true,
          data: result,
          metadata: {
            processingTime: 1000, // Placeholder
            toolsUsed: [tool],
            confidence: 0.8
          }
        });
      } catch (error) {
        responses.push({
          requestId: request.id,
          respondingAgentId: request.targetAgentId,
          success: false,
          error: error instanceof Error ? error.message : String(error)
        });
      }
    }
    
    return responses;
  }

  /**
   * Execute tools for an agent
   */
  private async executeTools(tools: string[], message: string, agentId: string, conversationId: string): Promise<Array<{ toolName: string; data: any }>> {
    const results: Array<{ toolName: string; data: any }> = [];
    
    for (const tool of tools) {
      try {
        const result = await this.mcpRegistry.callTool(tool, { query: message }, agentId, {
          conversationId,
          query: message,
          agentType: agentId
        });
        
        results.push({ toolName: tool, data: result });
      } catch (error) {
        console.error(`Error executing tool ${tool}:`, error);
        results.push({ 
          toolName: tool, 
          data: { error: error instanceof Error ? error.message : String(error) } 
        });
      }
    }
    
    return results;
  }

  /**
   * Generate response using agent context and memory
   */
  private async generateResponse(agentId: string, message: string, context: AgentContext, toolsUsed: string[]): Promise<string> {
    const agent = this.getAgentConfig(agentId);
    if (!agent) {
      throw new Error(`Agent configuration not found: ${agentId}`);
    }

    // Get recent memory for context
    const recentMemory = context.memory.slice(-10);
    const memoryContext = recentMemory.map(entry => entry.content).join('\n');

    // Build response based on agent type and available information
    let response = `[Agent ${agent.name}] `;
    
    if (toolsUsed.length > 0) {
      response += `I used the following tools to help answer your question: ${toolsUsed.join(', ')}. `;
    }
    
    // Add context-aware response
    if (message.toLowerCase().includes('code')) {
      response += "Based on the code analysis, I can help you with your development needs.";
    } else if (message.toLowerCase().includes('documentation')) {
      response += "I've gathered the relevant documentation to help answer your question.";
    } else {
      response += "I'm here to help with your roofing and technical needs.";
    }

    return response;
  }

  /**
   * Update performance metrics
   */
  private updatePerformanceMetrics(agentId: string, responseTime: number, success: boolean): void {
    // Update all contexts for this agent
    for (const context of this.contexts.values()) {
      if (context.agentId === agentId) {
        context.performance.totalQueries++;
        if (success) {
          context.performance.successfulQueries++;
        }
        
        // Update average response time
        const totalTime = context.performance.averageResponseTime * (context.performance.totalQueries - 1) + responseTime;
        context.performance.averageResponseTime = totalTime / context.performance.totalQueries;
        
        context.performance.lastActivity = Date.now();
      }
    }
  }

  /**
   * Assign tools to agents based on their specialization
   */
  private async assignToolsToAgents(): Promise<void> {
    const allTools = this.mcpRegistry.getAllTools();
    
    for (const agent of this.configs) {
      if (!agent.enabled) continue;
      
      const assignments: AgentToolAssignment[] = [];
      
      // Assign tools based on agent type and MCP server
      for (const tool of allTools) {
        let priority = 1;
        const conditions: any = {};
        
        // Agent-specific tool assignments
        if (agent.id === 'general-agent') {
          // General agent gets all tools with medium priority
          priority = 2;
        } else if (agent.id === 'rag-agent' && tool.category === 'documentation') {
          // RAG agent gets high priority for documentation tools
          priority = 5;
          conditions.keywords = ['documentation', 'docs', 'guide'];
        } else if (agent.id === 'voice-agent' && tool.serverId === 'vapi') {
          // Voice agent gets high priority for voice tools
          priority = 5;
          conditions.keywords = ['call', 'phone', 'voice'];
        }
        
        // MCP server specific assignments
        if (agent.mcpServerId === tool.serverId || agent.mcpServerId === 'all') {
          assignments.push({
            agentId: agent.id,
            toolName: tool.name,
            serverId: tool.serverId,
            priority,
            conditions
          });
        }
      }
      
      this.toolAssignments.set(agent.id, assignments);
      console.log(`Assigned ${assignments.length} tools to agent ${agent.name}`);
    }
  }

  /**
   * Assign tools to a specific agent
   */
  private assignToolsToAgent(agentId: string): void {
    // This will be called by assignToolsToAgents
  }

  /**
   * Get tools assigned to an agent
   */
  private getAgentTools(agentId: string): string[] {
    const assignments = this.toolAssignments.get(agentId) || [];
    return assignments.map(assignment => assignment.toolName);
  }

  /**
   * Find agent that has a specific tool
   */
  private findAgentWithTool(toolName: string): string | null {
    for (const [agentId, assignments] of this.toolAssignments) {
      if (assignments.some(assignment => assignment.toolName === toolName)) {
        return agentId;
      }
    }
    return null;
  }

  /**
   * Start memory cleanup process
   */
  private startMemoryCleanup(): void {
    this.memoryCleanupInterval = setInterval(() => {
      this.cleanupMemory();
    }, 5 * 60 * 1000); // Every 5 minutes
  }

  /**
   * Start performance monitoring
   */
  private startPerformanceMonitoring(): void {
    this.performanceMonitorInterval = setInterval(() => {
      this.monitorPerformance();
    }, 60 * 1000); // Every minute
  }

  /**
   * Cleanup old memory entries
   */
  private cleanupMemory(): void {
    const now = Date.now();
    const maxAge = 24 * 60 * 60 * 1000; // 24 hours
    
    for (const context of this.contexts.values()) {
      context.memory = context.memory.filter(entry => {
        const age = now - entry.timestamp;
        return age < maxAge || entry.importance > 0.8; // Keep important memories longer
      });
    }
  }

  /**
   * Monitor agent performance
   */
  private monitorPerformance(): void {
    for (const [contextKey, context] of this.contexts) {
      const inactiveTime = Date.now() - context.performance.lastActivity;
      
      // Remove inactive contexts after 1 hour
      if (inactiveTime > 60 * 60 * 1000) {
        this.contexts.delete(contextKey);
      }
    }
    
    this.emit('performance-update', {
      activeContexts: this.contexts.size,
      totalAgents: this.configs.length
    });
  }

  /**
   * Shutdown the enhanced agent manager
   */
  async shutdown(): Promise<void> {
    if (this.memoryCleanupInterval) {
      clearInterval(this.memoryCleanupInterval);
    }
    
    if (this.performanceMonitorInterval) {
      clearInterval(this.performanceMonitorInterval);
    }
    
    this.contexts.clear();
    this.toolAssignments.clear();
    this.removeAllListeners();
    
    console.log('Enhanced Agent Manager shutdown');
  }
}
