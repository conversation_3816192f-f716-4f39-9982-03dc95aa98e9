"use client";

import React, { useEffect } from "react";
import { useCopilotAction } from "@copilotkit/react-core";
import { useMCPTools } from "../lib/copilot/useMCPTools";

interface VoiceAgentCopilotProps {
  enabled?: boolean;
}

/**
 * VoiceAgentCopilot component that integrates the Voice Agent with CopilotKit
 */
export function VoiceAgentCopilot({ enabled = true }: VoiceAgentCopilotProps) {
  // Initialize MCP tools specifically for VAPI
  const { isInitialized } = useMCPTools({
    serverIds: ["vapi"],
    enabled,
  });

  // Handle incoming call action
  useCopilotAction({
    name: "handleIncomingCall",
    description: "Handle an incoming phone call",
    parameters: [
      {
        name: "callId",
        type: "string",
        description: "ID of the incoming call",
        required: true,
      },
      {
        name: "callerNumber",
        type: "string",
        description: "Phone number of the caller",
        required: true,
      },
      {
        name: "callerName",
        type: "string",
        description: "Name of the caller if available",
      },
    ],
    handler: async (args) => {
      try {
        // Call the voice agent service to handle the call
        const response = await fetch("/api/voice-agent/handle-call", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(args),
        });

        if (!response.ok) {
          throw new Error(`Failed to handle call: ${response.statusText}`);
        }

        return `Call ${args.callId} from ${args.callerName || args.callerNumber} is being handled by the voice agent.`;
      } catch (error) {
        console.error("Error handling incoming call:", error);
        return `Failed to handle call: ${error instanceof Error ? error.message : String(error)}`;
      }
    },
  });

  // Schedule appointment via voice call action
  useCopilotAction({
    name: "scheduleAppointmentViaCall",
    description: "Schedule an appointment from a phone call",
    parameters: [
      {
        name: "callId",
        type: "string",
        description: "ID of the call",
        required: true,
      },
      {
        name: "customerName",
        type: "string",
        description: "Customer's full name",
        required: true,
      },
      {
        name: "phoneNumber",
        type: "string",
        description: "Customer's phone number",
        required: true,
      },
      {
        name: "address",
        type: "string",
        description: "Customer's property address",
        required: true,
      },
      {
        name: "serviceType",
        type: "string",
        description: "Type of service needed",
        required: true,
      },
      {
        name: "preferredDate",
        type: "string",
        description: "Preferred appointment date",
        required: true,
      },
      {
        name: "preferredTime",
        type: "string",
        description: "Preferred appointment time",
        required: true,
      },
    ],
    handler: async (args) => {
      try {
        // Call the voice agent service to schedule the appointment
        const response = await fetch("/api/voice-agent/schedule-appointment", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(args),
        });

        if (!response.ok) {
          throw new Error(`Failed to schedule appointment: ${response.statusText}`);
        }

        return `Appointment scheduled successfully for ${args.customerName} on ${args.preferredDate} at ${args.preferredTime}.`;
      } catch (error) {
        console.error("Error scheduling appointment via call:", error);
        return `Failed to schedule appointment: ${error instanceof Error ? error.message : String(error)}`;
      }
    },
  });

  return null;
}
