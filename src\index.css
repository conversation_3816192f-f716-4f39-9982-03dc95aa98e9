@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --color-neon-green: #00ff94;
  --color-electric-blue: #00d1ff;
  --color-base-black: #000000;
  --color-rich-dark: #13111C;
  --color-warning: #ffd700;
  --color-error: #ff4444;
}

@layer base {
  body {
    @apply bg-base-black text-white antialiased;
    background: radial-gradient(circle, var(--color-rich-dark), var(--color-base-black));
    min-height: 100vh;
  }

  h1 {
    @apply text-[2rem] font-semibold;
  }

  h2 {
    @apply text-[1.5rem] font-semibold;
  }
}

@layer components {
  .btn-primary {
    @apply px-6 py-3 rounded-lg font-semibold text-black relative overflow-hidden transition-all duration-300;
    background: linear-gradient(to right, var(--color-neon-green), var(--color-electric-blue));
  }

  .btn-primary:hover {
    @apply shadow-[0_0_20px_rgba(0,255,148,0.5)];
  }

  .btn-secondary {
    @apply px-6 py-3 rounded-lg font-semibold border-2 border-neon-green
           bg-transparent transition-all duration-300;
  }

  .btn-secondary:hover {
    @apply shadow-[0_0_20px_rgba(0,255,148,0.3)] bg-neon-green/10;
  }

  .btn-ghost {
    @apply px-6 py-3 rounded-lg font-semibold text-gray-300
           transition-opacity duration-300 hover:opacity-80;
  }

  .glass-panel {
    @apply rounded-2xl border border-white/10 bg-white/5 backdrop-blur-lg
           p-6 transition-all duration-500;
  }

  .glass-panel:hover {
    @apply border-white/20 bg-white/10 shadow-[0_0_30px_rgba(0,255,148,0.1)];
  }

  .prose {
    @apply text-gray-300;
  }

  .prose p {
    @apply mb-4;
  }

  .prose ul {
    @apply mb-4 space-y-2;
  }

  .prose li {
    @apply text-gray-300;
  }

  /* Markdown formatting for agent responses */
  .whitespace-pre-line ul {
    @apply list-disc pl-6 mb-4;
  }

  .whitespace-pre-line ol {
    @apply list-decimal pl-6 mb-4;
  }

  .whitespace-pre-line li {
    @apply mb-2 last:mb-0;
  }

  .whitespace-pre-line h1,
  .whitespace-pre-line h2,
  .whitespace-pre-line h3,
  .whitespace-pre-line h4 {
    @apply mt-6 mb-4 font-bold;
  }

  .whitespace-pre-line h1 {
    @apply text-2xl;
  }

  .whitespace-pre-line h2 {
    @apply text-xl;
  }

  .whitespace-pre-line h3 {
    @apply text-lg;
  }

  .whitespace-pre-line blockquote {
    @apply border-l-4 border-white/20 pl-4 my-4 italic;
  }

  .whitespace-pre-line p {
    @apply mb-4 last:mb-0;
  }
}

@keyframes glow {
  from { box-shadow: 0 0 10px #00ff94, 0 0 20px #00ff94, 0 0 30px #00ff94; }
  to { box-shadow: 0 0 20px #00ff94, 0 0 30px #00ff94, 0 0 40px #00ff94; }
}

@keyframes fadeIn {
  0% { opacity: 0; transform: translateY(10px); }
  100% { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
  0% { opacity: 0; transform: translateX(20px); }
  100% { opacity: 1; transform: translateX(0); }
}

@keyframes slideOut {
  0% { opacity: 1; transform: translateX(0); }
  100% { opacity: 0; transform: translateX(-20px); }
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

@layer utilities {
  .animate-glow {
    animation: glow 1.5s ease-in-out infinite alternate;
  }

  .animate-fadeIn {
    animation: fadeIn 0.3s ease-out forwards;
  }

  .animate-slideIn {
    animation: slideIn 0.4s ease-out forwards;
  }

  .animate-slideOut {
    animation: slideOut 0.4s ease-out forwards;
  }

  .animate-pulse {
    animation: pulse 2s ease-in-out infinite;
  }

  .transition-smooth {
    @apply transition-all duration-500 ease-in-out;
  }

  .focus-section {
    @apply relative z-10 opacity-100;
  }

  .blur-section {
    @apply relative opacity-40 filter blur-[2px] pointer-events-none;
  }

  .section-active {
    @apply scale-100 opacity-100 shadow-[0_0_30px_rgba(0,255,148,0.1)];
  }

  .section-inactive {
    @apply scale-95 opacity-50;
  }

  /* Smooth scrolling for lesson sections */
  html {
    scroll-behavior: smooth;
  }

  /* Hide scrollbar for lesson viewer */
  .hide-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .hide-scrollbar::-webkit-scrollbar {
    display: none;
  }

  /* Snap scrolling */
  .snap-y {
    scroll-snap-type: y mandatory;
  }

  .snap-start {
    scroll-snap-align: start;
  }

  /* Smooth scrolling */
  .smooth-scroll {
    scroll-behavior: smooth;
  }

  /* Slide transitions */
  .slide-enter {
    transform: translateY(100%);
  }

  .slide-enter-active {
    transform: translateY(0);
    transition: transform 500ms ease-out;
  }

  .slide-exit {
    transform: translateY(0);
  }

  .slide-exit-active {
    transform: translateY(-100%);
    transition: transform 500ms ease-out;
  }

  /* Custom scrollbar */
  .custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: rgba(0, 255, 148, 0.3) rgba(255, 255, 255, 0.05);
  }

  .custom-scrollbar::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 4px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: rgba(0, 255, 148, 0.3);
    border-radius: 4px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 255, 148, 0.5);
  }
}