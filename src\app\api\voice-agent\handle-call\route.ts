import { NextRequest, NextResponse } from "next/server";
import { processIncomingCall } from "../../../../lib/agents/voice-agent-service";

export async function POST(req: NextRequest) {
  try {
    // Parse request body
    const body = await req.json();
    const { callId, callerNumber, callerName } = body;
    
    // Validate request
    if (!callId || !callerNumber) {
      return NextResponse.json(
        { error: "Invalid request: callId and callerNumber are required" },
        { status: 400 }
      );
    }
    
    // Process the incoming call
    await processIncomingCall(callId);
    
    // Return success response
    return NextResponse.json({
      success: true,
      message: `Call ${callId} from ${callerName || callerNumber} is being handled by the voice agent.`,
    });
  } catch (error: any) {
    console.error("Error handling call:", error);
    
    return NextResponse.json(
      { error: error.message || "An error occurred while handling the call" },
      { status: 500 }
    );
  }
}
