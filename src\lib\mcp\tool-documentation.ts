/**
 * Tool Documentation Helper
 * 
 * This module provides helper functions for generating tool documentation
 * to be included in agent system prompts.
 * This is a placeholder implementation until the full MCP implementation is complete.
 */

import { MCPToolRegistry } from './mcp-tool-registry';

/**
 * Generate tool documentation for an agent
 */
export function generateToolDocumentation(mcpServerId?: string): string {
  const toolRegistry = MCPToolRegistry.getInstance();
  const allTools = toolRegistry.getAllTools();
  
  // Filter tools by MCP server ID if provided
  const tools = mcpServerId 
    ? allTools.filter(tool => toolRegistry.getToolInfo(tool.name)?.serverId === mcpServerId)
    : allTools;
  
  if (tools.length === 0) {
    return "You don't have access to any tools.";
  }
  
  let documentation = "# Available Tools\n\n";
  documentation += "You have access to the following tools. Use them when appropriate to accomplish tasks.\n\n";
  
  // Add tool usage instructions
  documentation += "## How to Use Tools\n\n";
  documentation += "To use a tool, respond with the following format:\n\n";
  documentation += "```\n[TOOL] tool_name {\"param1\": \"value1\", \"param2\": \"value2\"} [/TOOL]\n```\n\n";
  documentation += "For example:\n\n";
  documentation += "```\n[TOOL] search_code {\"query\": \"function example\"} [/TOOL]\n```\n\n";
  
  // Add placeholder for tool documentation
  documentation += "## Tool Reference\n\n";
  documentation += "MCP implementation in progress. Tool documentation will be available soon.\n\n";
  
  return documentation;
}
