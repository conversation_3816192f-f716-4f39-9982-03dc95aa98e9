/**
 * Enhanced Agent Service with Comprehensive MCP Integration
 *
 * This module provides a service for initializing and managing agents with
 * full MCP (Model Context Protocol) integration, including intelligent routing,
 * error handling, and agent collaboration.
 */

import { AgentManager } from './agent-manager';
import { EnhancedAgentManager } from './enhanced-agent-manager';
import { MCPIntegrationService } from '../mcp/integration-service';
import { AgentSettings } from '../../types';
import { defaultAgents } from './default-agents';

// Service instances
let agentManager: AgentManager | null = null;
let enhancedAgentManager: EnhancedAgentManager | null = null;
let integrationService: MCPIntegrationService | null = null;
let useEnhancedMode: boolean = true; // Flag to enable enhanced mode

/**
 * Initialize the agent service with comprehensive MCP integration
 */
export async function initializeAgentService(agentConfigs?: AgentSettings[]): Promise<void> {
  try {
    // Use default agents if none are provided
    const configs = agentConfigs && agentConfigs.length > 0 ? agentConfigs : defaultAgents;

    console.log(`Initializing enhanced agent service with ${configs.length} agents:`,
      configs.map(config => `${config.id} (${config.name})`).join(', '));

    if (useEnhancedMode) {
      // Initialize the comprehensive MCP integration service
      integrationService = MCPIntegrationService.getInstance();
      await integrationService.initialize(configs);

      // Get the enhanced agent manager from the integration service
      enhancedAgentManager = EnhancedAgentManager.getInstance();

      console.log('Enhanced agent service with MCP integration initialized successfully');
    } else {
      // Fall back to basic agent manager
      if (!agentManager) {
        agentManager = AgentManager.getInstance();
      }
      await agentManager.initialize(configs);

      console.log('Basic agent service initialized successfully');
    }
  } catch (error) {
    console.error('Error initializing agent service:', error);

    // Fall back to basic mode if enhanced mode fails
    if (useEnhancedMode) {
      console.log('Falling back to basic agent service...');
      useEnhancedMode = false;

      if (!agentManager) {
        agentManager = AgentManager.getInstance();
      }
      await agentManager.initialize(configs);
    } else {
      throw error;
    }
  }
}

/**
 * Get the agent manager instance
 */
export function getAgentManager(): AgentManager {
  if (!agentManager) {
    agentManager = AgentManager.getInstance();
  }

  return agentManager;
}

/**
 * Process a message using the enhanced agent system with MCP integration
 */
export async function processAgentMessage(message: string, agentId?: string, conversationId?: string): Promise<{
  response: string;
  agentId: string;
  toolsUsed?: string[];
  collaborations?: string[];
  confidence?: number;
  metadata?: any;
}> {
  try {
    if (useEnhancedMode && enhancedAgentManager && integrationService) {
      // Use the enhanced integration service
      const result = await integrationService.processMessage(message, {
        agentId,
        conversationId,
        timeout: 30000
      });

      return {
        response: result.response,
        agentId: result.agentId,
        toolsUsed: result.toolsUsed,
        collaborations: result.collaborations,
        confidence: result.confidence,
        metadata: result.metadata
      };
    } else {
      // Fall back to basic agent manager
      if (!agentManager) {
        throw new Error('Agent service not initialized');
      }

      // Get the agent configuration
      const agentConfig = agentId
        ? agentManager.getAgentConfig(agentId)
        : agentManager.getAllAgentConfigs().find(config => config.enabled);

      if (!agentConfig) {
        throw new Error(`No enabled agent found${agentId ? ` with ID: ${agentId}` : ''}`);
      }

      // Use the basic agent manager to process the message
      const result = await agentManager.processMessage(message, agentConfig.id);

      return {
        response: result.response,
        agentId: result.agentId
      };
    }
  } catch (error) {
    console.error('Error processing message:', error);

    // Return user-friendly error message
    const errorMessage = error instanceof Error && error.message.includes('I encountered an error')
      ? error.message
      : `I encountered an error while processing your request. Please try again or contact support if the problem persists.`;

    return {
      response: errorMessage,
      agentId: agentId || 'general-agent',
      confidence: 0.1
    };
  }
}

/**
 * Get all available agent configurations
 */
export function getAvailableAgents(): AgentSettings[] {
  if (useEnhancedMode && enhancedAgentManager) {
    return enhancedAgentManager.getAllAgentConfigs();
  } else if (agentManager) {
    return agentManager.getAllAgentConfigs();
  }

  return [];
}

/**
 * Get comprehensive system status (enhanced mode only)
 */
export function getSystemStatus(): any {
  if (useEnhancedMode && integrationService) {
    return integrationService.getSystemInfo();
  }

  return {
    mode: 'basic',
    agentCount: agentManager?.getAllAgentConfigs().length || 0,
    initialized: !!agentManager
  };
}

/**
 * Get agent performance metrics (enhanced mode only)
 */
export function getAgentPerformance(agentId?: string): any {
  if (useEnhancedMode && enhancedAgentManager) {
    return enhancedAgentManager.getAgentPerformance(agentId);
  }

  return null;
}

/**
 * Get agent memory for a conversation (enhanced mode only)
 */
export function getAgentMemory(agentId: string, conversationId: string): any[] {
  if (useEnhancedMode && enhancedAgentManager) {
    return enhancedAgentManager.getAgentMemory(agentId, conversationId);
  }

  return [];
}

/**
 * Add a new MCP server (enhanced mode only)
 */
export async function addMCPServer(serverConfig: any): Promise<void> {
  if (useEnhancedMode && integrationService) {
    await integrationService.addServer(serverConfig);
  } else {
    throw new Error('Enhanced mode not available');
  }
}

/**
 * Remove an MCP server (enhanced mode only)
 */
export async function removeMCPServer(serverId: string): Promise<void> {
  if (useEnhancedMode && integrationService) {
    await integrationService.removeServer(serverId);
  } else {
    throw new Error('Enhanced mode not available');
  }
}

/**
 * Check if enhanced mode is active
 */
export function isEnhancedModeActive(): boolean {
  return useEnhancedMode && !!integrationService && !!enhancedAgentManager;
}

/**
 * Clean up resources when the agent service is no longer needed
 */
export async function cleanupAgentService(): Promise<void> {
  try {
    if (useEnhancedMode && integrationService) {
      console.log('Cleaning up enhanced agent service...');
      await integrationService.shutdown();
      integrationService = null;
      enhancedAgentManager = null;
    }

    if (agentManager) {
      console.log('Cleaning up basic agent service...');
      agentManager = null;
    }

    console.log('Agent service cleanup complete');
  } catch (error) {
    console.error('Error during agent service cleanup:', error);
  }
}
