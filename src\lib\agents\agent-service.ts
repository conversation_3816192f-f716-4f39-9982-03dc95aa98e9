/**
 * Agent Service
 *
 * This module provides a service for initializing and managing LangGraph agents.
 * It also supports MCP (Model Context Protocol) for enhanced agent capabilities.
 */

import { AgentManager } from './agent-manager';
import { AgentSettings } from '../../types';
import { defaultAgents } from './default-agents';

// Singleton instance of the agent manager
let agentManager: AgentManager | null = null;

/**
 * Initialize the agent service with the provided agent configurations
 */
export async function initializeAgentService(agentConfigs?: AgentSettings[]): Promise<void> {
  if (!agentManager) {
    agentManager = AgentManager.getInstance();
  }

  // Use default agents if none are provided
  const configs = agentConfigs && agentConfigs.length > 0 ? agentConfigs : defaultAgents;

  // Log the agent configurations
  console.log(`Initializing agent service with ${configs.length} agents:`,
    configs.map(config => `${config.id} (${config.name})`).join(', '));

  await agentManager.initialize(configs);
}

/**
 * Get the agent manager instance
 */
export function getAgentManager(): AgentManager {
  if (!agentManager) {
    agentManager = AgentManager.getInstance();
  }

  return agentManager;
}

/**
 * Process a message using the appropriate agent
 */
export async function processAgentMessage(message: string, agentId?: string): Promise<{
  response: string;
  agentId: string;
}> {
  if (!agentManager) {
    throw new Error('Agent service not initialized');
  }

  try {
    // Get the agent configuration
    const agentConfig = agentId
      ? agentManager.getAgentConfig(agentId)
      : agentManager.getAllAgentConfigs().find(config => config.enabled);

    if (!agentConfig) {
      throw new Error(`No enabled agent found${agentId ? ` with ID: ${agentId}` : ''}`);
    }

    // Use the agent manager to process the message
    const result = await agentManager.processMessage(message, agentConfig.id);

    return {
      response: result.response,
      agentId: result.agentId
    };
  } catch (error) {
    console.error('Error processing message:', error);

    return {
      response: `Error: ${error instanceof Error ? error.message : String(error)}`,
      agentId: agentId || 'general-agent'
    };
  }
}

/**
 * Get all available agent configurations
 */
export function getAvailableAgents(): AgentSettings[] {
  if (!agentManager) {
    return [];
  }

  return agentManager.getAllAgentConfigs();
}

/**
 * Clean up resources when the agent service is no longer needed
 */
export async function cleanupAgentService(): Promise<void> {
  if (agentManager) {
    // This is a placeholder implementation until the full agent system is complete
    console.log('Cleaning up agent service');
    agentManager = null;
  }
}
