/**
 * Enhanced Error Handling and Resilience System
 * 
 * This module provides comprehensive error handling, circuit breakers,
 * graceful degradation, and user feedback for the MCP system.
 */

import EventEmitter from 'eventemitter3';

/**
 * Error Types
 */
export enum MCPErrorType {
  CONNECTION_ERROR = 'CONNECTION_ERROR',
  TIMEOUT_ERROR = 'TIMEOUT_ERROR',
  AUTHENTICATION_ERROR = 'AUTHENTICATION_ERROR',
  RATE_LIMIT_ERROR = 'RATE_LIMIT_ERROR',
  TOOL_NOT_FOUND = 'TOOL_NOT_FOUND',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  SERVER_ERROR = 'SERVER_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

/**
 * Error Severity Levels
 */
export enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

/**
 * MCP Error Interface
 */
export interface MCPError {
  id: string;
  type: MCPErrorType;
  severity: ErrorSeverity;
  message: string;
  details?: any;
  serverId?: string;
  toolName?: string;
  agentId?: string;
  timestamp: number;
  retryable: boolean;
  userMessage: string;
}

/**
 * Circuit Breaker State
 */
enum CircuitBreakerState {
  CLOSED = 'closed',
  OPEN = 'open',
  HALF_OPEN = 'half_open'
}

/**
 * Circuit Breaker Configuration
 */
interface CircuitBreakerConfig {
  failureThreshold: number;
  recoveryTimeout: number;
  monitoringPeriod: number;
  minimumRequests: number;
}

/**
 * Circuit Breaker for MCP Servers
 */
class CircuitBreaker extends EventEmitter {
  private state: CircuitBreakerState = CircuitBreakerState.CLOSED;
  private failureCount: number = 0;
  private lastFailureTime: number = 0;
  private successCount: number = 0;
  private requestCount: number = 0;
  private config: CircuitBreakerConfig;

  constructor(
    private serverId: string,
    config: Partial<CircuitBreakerConfig> = {}
  ) {
    super();
    this.config = {
      failureThreshold: 5,
      recoveryTimeout: 60000, // 1 minute
      monitoringPeriod: 300000, // 5 minutes
      minimumRequests: 10,
      ...config
    };
  }

  /**
   * Execute a function with circuit breaker protection
   */
  async execute<T>(fn: () => Promise<T>): Promise<T> {
    if (this.state === CircuitBreakerState.OPEN) {
      if (this.shouldAttemptReset()) {
        this.state = CircuitBreakerState.HALF_OPEN;
        this.emit('state-changed', { serverId: this.serverId, state: this.state });
      } else {
        throw new Error(`Circuit breaker is OPEN for server ${this.serverId}`);
      }
    }

    try {
      const result = await fn();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure();
      throw error;
    }
  }

  /**
   * Get current state
   */
  getState(): CircuitBreakerState {
    return this.state;
  }

  /**
   * Get failure statistics
   */
  getStats(): {
    state: CircuitBreakerState;
    failureCount: number;
    successCount: number;
    requestCount: number;
    failureRate: number;
  } {
    return {
      state: this.state,
      failureCount: this.failureCount,
      successCount: this.successCount,
      requestCount: this.requestCount,
      failureRate: this.requestCount > 0 ? this.failureCount / this.requestCount : 0
    };
  }

  /**
   * Reset the circuit breaker
   */
  reset(): void {
    this.state = CircuitBreakerState.CLOSED;
    this.failureCount = 0;
    this.successCount = 0;
    this.requestCount = 0;
    this.lastFailureTime = 0;
    this.emit('reset', { serverId: this.serverId });
  }

  private onSuccess(): void {
    this.requestCount++;
    this.successCount++;
    
    if (this.state === CircuitBreakerState.HALF_OPEN) {
      this.state = CircuitBreakerState.CLOSED;
      this.failureCount = 0;
      this.emit('state-changed', { serverId: this.serverId, state: this.state });
    }
  }

  private onFailure(): void {
    this.requestCount++;
    this.failureCount++;
    this.lastFailureTime = Date.now();

    if (this.shouldOpenCircuit()) {
      this.state = CircuitBreakerState.OPEN;
      this.emit('state-changed', { serverId: this.serverId, state: this.state });
    }
  }

  private shouldOpenCircuit(): boolean {
    return (
      this.requestCount >= this.config.minimumRequests &&
      this.failureCount >= this.config.failureThreshold
    );
  }

  private shouldAttemptReset(): boolean {
    return Date.now() - this.lastFailureTime >= this.config.recoveryTimeout;
  }
}

/**
 * Enhanced Error Handler
 */
export class MCPErrorHandler extends EventEmitter {
  private static instance: MCPErrorHandler;
  private circuitBreakers: Map<string, CircuitBreaker> = new Map();
  private errorLog: MCPError[] = [];
  private retryQueues: Map<string, Array<() => Promise<any>>> = new Map();
  private degradationStrategies: Map<string, () => any> = new Map();

  /**
   * Get the singleton instance
   */
  static getInstance(): MCPErrorHandler {
    if (!MCPErrorHandler.instance) {
      MCPErrorHandler.instance = new MCPErrorHandler();
    }
    return MCPErrorHandler.instance;
  }

  /**
   * Initialize the error handler
   */
  initialize(): void {
    console.log('MCP Error Handler initialized');
  }

  /**
   * Handle an error with comprehensive error processing
   */
  async handleError(error: any, context: {
    serverId?: string;
    toolName?: string;
    agentId?: string;
    operation?: string;
  }): Promise<MCPError> {
    const mcpError = this.createMCPError(error, context);
    
    // Log the error
    this.logError(mcpError);
    
    // Emit error event
    this.emit('error', mcpError);
    
    // Handle circuit breaker logic
    if (mcpError.serverId) {
      this.updateCircuitBreaker(mcpError.serverId, false);
    }
    
    // Attempt recovery strategies
    await this.attemptRecovery(mcpError);
    
    return mcpError;
  }

  /**
   * Execute operation with error handling and circuit breaker protection
   */
  async executeWithProtection<T>(
    operation: () => Promise<T>,
    context: {
      serverId: string;
      toolName?: string;
      agentId?: string;
      timeout?: number;
      retries?: number;
    }
  ): Promise<T> {
    const circuitBreaker = this.getOrCreateCircuitBreaker(context.serverId);
    const maxRetries = context.retries || 3;
    let lastError: any;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        // Execute with circuit breaker protection
        const result = await circuitBreaker.execute(async () => {
          // Add timeout protection
          if (context.timeout) {
            return await this.withTimeout(operation(), context.timeout);
          }
          return await operation();
        });

        // Update circuit breaker on success
        this.updateCircuitBreaker(context.serverId, true);
        
        return result;
      } catch (error) {
        lastError = error;
        
        // Handle the error
        const mcpError = await this.handleError(error, context);
        
        // Check if we should retry
        if (attempt < maxRetries && mcpError.retryable) {
          const delay = this.calculateRetryDelay(attempt);
          console.log(`Retrying operation after ${delay}ms (attempt ${attempt + 1}/${maxRetries})`);
          await this.sleep(delay);
          continue;
        }
        
        // If not retryable or max retries reached, try degradation
        const degradedResult = await this.tryDegradation(context);
        if (degradedResult !== null) {
          return degradedResult;
        }
        
        // No recovery possible, throw the error
        throw mcpError;
      }
    }

    throw lastError;
  }

  /**
   * Register a degradation strategy for a server
   */
  registerDegradationStrategy(serverId: string, strategy: () => any): void {
    this.degradationStrategies.set(serverId, strategy);
  }

  /**
   * Get error statistics
   */
  getErrorStats(): {
    totalErrors: number;
    errorsByType: Record<MCPErrorType, number>;
    errorsBySeverity: Record<ErrorSeverity, number>;
    errorsByServer: Record<string, number>;
    recentErrors: MCPError[];
  } {
    const stats = {
      totalErrors: this.errorLog.length,
      errorsByType: {} as Record<MCPErrorType, number>,
      errorsBySeverity: {} as Record<ErrorSeverity, number>,
      errorsByServer: {} as Record<string, number>,
      recentErrors: this.errorLog.slice(-10)
    };

    // Initialize counters
    Object.values(MCPErrorType).forEach(type => {
      stats.errorsByType[type] = 0;
    });
    Object.values(ErrorSeverity).forEach(severity => {
      stats.errorsBySeverity[severity] = 0;
    });

    // Count errors
    for (const error of this.errorLog) {
      stats.errorsByType[error.type]++;
      stats.errorsBySeverity[error.severity]++;
      
      if (error.serverId) {
        stats.errorsByServer[error.serverId] = (stats.errorsByServer[error.serverId] || 0) + 1;
      }
    }

    return stats;
  }

  /**
   * Get circuit breaker status for all servers
   */
  getCircuitBreakerStatus(): Record<string, any> {
    const status: Record<string, any> = {};
    
    for (const [serverId, breaker] of this.circuitBreakers) {
      status[serverId] = breaker.getStats();
    }
    
    return status;
  }

  /**
   * Clear error log
   */
  clearErrorLog(): void {
    this.errorLog = [];
    this.emit('error-log-cleared');
  }

  /**
   * Reset circuit breaker for a server
   */
  resetCircuitBreaker(serverId: string): void {
    const breaker = this.circuitBreakers.get(serverId);
    if (breaker) {
      breaker.reset();
    }
  }

  // Private methods

  /**
   * Create MCP Error from generic error
   */
  private createMCPError(error: any, context: {
    serverId?: string;
    toolName?: string;
    agentId?: string;
    operation?: string;
  }): MCPError {
    const errorType = this.classifyError(error);
    const severity = this.determineSeverity(errorType, error);
    
    return {
      id: `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type: errorType,
      severity,
      message: error.message || String(error),
      details: error,
      serverId: context.serverId,
      toolName: context.toolName,
      agentId: context.agentId,
      timestamp: Date.now(),
      retryable: this.isRetryable(errorType, error),
      userMessage: this.generateUserMessage(errorType, context)
    };
  }

  /**
   * Classify error type
   */
  private classifyError(error: any): MCPErrorType {
    const message = error.message?.toLowerCase() || '';
    
    if (message.includes('timeout') || error.code === 'ETIMEDOUT') {
      return MCPErrorType.TIMEOUT_ERROR;
    }
    
    if (message.includes('connection') || message.includes('network') || error.code === 'ECONNREFUSED') {
      return MCPErrorType.CONNECTION_ERROR;
    }
    
    if (message.includes('unauthorized') || message.includes('authentication') || error.status === 401) {
      return MCPErrorType.AUTHENTICATION_ERROR;
    }
    
    if (message.includes('rate limit') || error.status === 429) {
      return MCPErrorType.RATE_LIMIT_ERROR;
    }
    
    if (message.includes('tool not found') || message.includes('unknown tool')) {
      return MCPErrorType.TOOL_NOT_FOUND;
    }
    
    if (message.includes('validation') || message.includes('invalid parameter')) {
      return MCPErrorType.VALIDATION_ERROR;
    }
    
    if (error.status >= 500) {
      return MCPErrorType.SERVER_ERROR;
    }
    
    return MCPErrorType.UNKNOWN_ERROR;
  }

  /**
   * Determine error severity
   */
  private determineSeverity(errorType: MCPErrorType, error: any): ErrorSeverity {
    switch (errorType) {
      case MCPErrorType.CONNECTION_ERROR:
      case MCPErrorType.SERVER_ERROR:
        return ErrorSeverity.HIGH;
      case MCPErrorType.AUTHENTICATION_ERROR:
        return ErrorSeverity.CRITICAL;
      case MCPErrorType.TIMEOUT_ERROR:
      case MCPErrorType.RATE_LIMIT_ERROR:
        return ErrorSeverity.MEDIUM;
      case MCPErrorType.TOOL_NOT_FOUND:
      case MCPErrorType.VALIDATION_ERROR:
        return ErrorSeverity.LOW;
      default:
        return ErrorSeverity.MEDIUM;
    }
  }

  /**
   * Check if error is retryable
   */
  private isRetryable(errorType: MCPErrorType, error: any): boolean {
    switch (errorType) {
      case MCPErrorType.TIMEOUT_ERROR:
      case MCPErrorType.CONNECTION_ERROR:
      case MCPErrorType.RATE_LIMIT_ERROR:
      case MCPErrorType.SERVER_ERROR:
        return true;
      case MCPErrorType.AUTHENTICATION_ERROR:
      case MCPErrorType.TOOL_NOT_FOUND:
      case MCPErrorType.VALIDATION_ERROR:
        return false;
      default:
        return false;
    }
  }

  /**
   * Generate user-friendly error message
   */
  private generateUserMessage(errorType: MCPErrorType, context: any): string {
    const serverName = context.serverId || 'the service';
    
    switch (errorType) {
      case MCPErrorType.CONNECTION_ERROR:
        return `I'm having trouble connecting to ${serverName}. Please try again in a moment.`;
      case MCPErrorType.TIMEOUT_ERROR:
        return `The request to ${serverName} is taking longer than expected. Please try again.`;
      case MCPErrorType.AUTHENTICATION_ERROR:
        return `There's an authentication issue with ${serverName}. Please contact support.`;
      case MCPErrorType.RATE_LIMIT_ERROR:
        return `I'm being rate limited by ${serverName}. Please wait a moment before trying again.`;
      case MCPErrorType.TOOL_NOT_FOUND:
        return `The requested tool is not available on ${serverName}.`;
      case MCPErrorType.VALIDATION_ERROR:
        return `There's an issue with the request parameters. Please check your input.`;
      case MCPErrorType.SERVER_ERROR:
        return `${serverName} is experiencing issues. I'll try an alternative approach.`;
      default:
        return `I encountered an unexpected issue. Please try again or contact support if the problem persists.`;
    }
  }

  /**
   * Log error
   */
  private logError(error: MCPError): void {
    this.errorLog.push(error);
    
    // Keep only last 1000 errors
    if (this.errorLog.length > 1000) {
      this.errorLog = this.errorLog.slice(-1000);
    }
    
    // Log to console based on severity
    const logLevel = error.severity === ErrorSeverity.CRITICAL ? 'error' :
                    error.severity === ErrorSeverity.HIGH ? 'error' :
                    error.severity === ErrorSeverity.MEDIUM ? 'warn' : 'info';
    
    console[logLevel](`MCP Error [${error.type}]:`, error.message, error.details);
  }

  /**
   * Get or create circuit breaker for server
   */
  private getOrCreateCircuitBreaker(serverId: string): CircuitBreaker {
    if (!this.circuitBreakers.has(serverId)) {
      const breaker = new CircuitBreaker(serverId);
      breaker.on('state-changed', (data) => {
        this.emit('circuit-breaker-state-changed', data);
      });
      this.circuitBreakers.set(serverId, breaker);
    }
    
    return this.circuitBreakers.get(serverId)!;
  }

  /**
   * Update circuit breaker state
   */
  private updateCircuitBreaker(serverId: string, success: boolean): void {
    // Circuit breaker updates are handled internally by the executeWithProtection method
  }

  /**
   * Attempt recovery strategies
   */
  private async attemptRecovery(error: MCPError): Promise<void> {
    // Implement recovery strategies based on error type
    switch (error.type) {
      case MCPErrorType.RATE_LIMIT_ERROR:
        // Add to retry queue with delay
        if (error.serverId) {
          this.addToRetryQueue(error.serverId, async () => {
            // Retry logic would go here
          });
        }
        break;
      case MCPErrorType.CONNECTION_ERROR:
        // Attempt to reconnect or use alternative server
        break;
    }
  }

  /**
   * Try degradation strategy
   */
  private async tryDegradation(context: { serverId: string; [key: string]: any }): Promise<any> {
    const strategy = this.degradationStrategies.get(context.serverId);
    if (strategy) {
      try {
        console.log(`Attempting degradation strategy for server: ${context.serverId}`);
        return await strategy();
      } catch (error) {
        console.error('Degradation strategy failed:', error);
      }
    }
    return null;
  }

  /**
   * Add operation to retry queue
   */
  private addToRetryQueue(serverId: string, operation: () => Promise<any>): void {
    if (!this.retryQueues.has(serverId)) {
      this.retryQueues.set(serverId, []);
    }
    this.retryQueues.get(serverId)!.push(operation);
  }

  /**
   * Calculate retry delay with exponential backoff
   */
  private calculateRetryDelay(attempt: number): number {
    return Math.min(1000 * Math.pow(2, attempt), 30000); // Max 30 seconds
  }

  /**
   * Add timeout to a promise
   */
  private withTimeout<T>(promise: Promise<T>, timeoutMs: number): Promise<T> {
    return Promise.race([
      promise,
      new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error(`Operation timed out after ${timeoutMs}ms`)), timeoutMs);
      })
    ]);
  }

  /**
   * Sleep utility
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
