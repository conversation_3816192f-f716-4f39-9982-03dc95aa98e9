"use client";

import React from "react";
import { CopilotProvider } from "../lib/copilot/CopilotProvider";
import { CopilotChat } from "../components/CopilotChat";
import "../styles/globals.css";

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <head>
        <title>Phoenix Roofing and Repair</title>
        <meta name="description" content="Phoenix Roofing and Repair - Quality roofing services in Phoenix, AZ" />
      </head>
      <body>
        <CopilotProvider>
          {children}
          <CopilotChat variant="popup" />
        </CopilotProvider>
      </body>
    </html>
  );
}
