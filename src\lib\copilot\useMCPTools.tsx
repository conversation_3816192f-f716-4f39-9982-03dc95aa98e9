"use client";

import { useEffect, useState } from "react";
import { useCopilotTools } from "@copilotkit/react-core";
import { MCPToolAdapter } from "./MCPToolAdapter";

interface UseMCPToolsOptions {
  serverIds?: string[];
  enabled?: boolean;
}

/**
 * Custom hook to use MCP tools with CopilotKit
 */
export function useMCPTools({ 
  serverIds = [], 
  enabled = true 
}: UseMCPToolsOptions = {}) {
  const [isInitialized, setIsInitialized] = useState(false);
  const [adapter] = useState(() => new MCPToolAdapter());
  const [tools, setTools] = useState<any[]>([]);
  
  // Initialize the MCP tool adapter
  useEffect(() => {
    if (!enabled) return;
    
    const initializeAdapter = async () => {
      try {
        await adapter.initialize(serverIds);
        const toolDefinitions = adapter.getToolDefinitions();
        setTools(toolDefinitions);
        setIsInitialized(true);
      } catch (error) {
        console.error("Error initializing MCP tool adapter:", error);
      }
    };
    
    initializeAdapter();
  }, [adapter, serverIds, enabled]);
  
  // Register the tools with CopilotKit
  useCopilotTools(tools, [isInitialized, tools]);
  
  return {
    isInitialized,
    tools,
    adapter,
  };
}
