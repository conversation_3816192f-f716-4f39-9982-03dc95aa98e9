import os
from typing import Annotated, Dict, List, Any
from typing_extensions import TypedDict
from operator import add

import openai
from langchain_core.messages import HumanMessage, AIMessage
from langgraph.graph import StateGraph, START, END
from langgraph.graph.message import add_messages
from langgraph.checkpoint.memory import MemorySaver

# Define the state for our agent
class AgentState(TypedDict):
    messages: Annotated[list, add_messages]
    model: str
    context: Dict[str, Any]

# Initialize OpenRouter client
def init_openrouter_client(api_key=None, model=None):
    """Initialize the OpenRouter client with the specified API key and model."""
    if api_key is None:
        api_key = os.environ.get("OPENROUTER_API_KEY")
    
    if not api_key:
        raise ValueError("OpenRouter API key is required")
    
    client = openai.OpenAI(
        api_key=api_key,
        base_url="https://openrouter.ai/api/v1",
        default_headers={
            "HTTP-Referer": "http://localhost:5173",  # Your website URL
            "X-Title": "AG3NT",  # Optional, shows in rankings
        }
    )
    
    return client

# Define the model node
def model_node(state: AgentState):
    """Process the messages with the specified model and return the response."""
    client = init_openrouter_client()
    
    # Get the current model from the state
    model = state.get("model", "anthropic/claude-3.7-sonnet")
    
    # Get the messages from the state
    messages = state["messages"]
    
    # Convert messages to the format expected by OpenRouter
    openrouter_messages = []
    for message in messages:
        if isinstance(message, HumanMessage):
            openrouter_messages.append({"role": "user", "content": message.content})
        elif isinstance(message, AIMessage):
            openrouter_messages.append({"role": "assistant", "content": message.content})
        else:
            # Handle other message types if needed
            pass
    
    # Call the OpenRouter API
    response = client.chat.completions.create(
        model=model,
        messages=openrouter_messages,
        temperature=0.7,
        max_tokens=2048,
    )
    
    # Extract the response content
    content = response.choices[0].message.content
    
    # Return the response as an AIMessage
    return {"messages": [AIMessage(content=content)]}

# Build the graph
def build_agent_graph():
    """Build the LangGraph agent graph."""
    # Create the graph
    graph_builder = StateGraph(AgentState)
    
    # Add the model node
    graph_builder.add_node("model", model_node)
    
    # Set the entry point
    graph_builder.add_edge(START, "model")
    
    # Set the exit point
    graph_builder.add_edge("model", END)
    
    # Create a memory saver for persistence
    memory = MemorySaver()
    
    # Compile the graph
    return graph_builder.compile(checkpointer=memory)

# Function to invoke the agent
def invoke_agent(graph, user_message, model="anthropic/claude-3.7-sonnet", thread_id="default"):
    """Invoke the agent with a user message."""
    # Create the input state
    input_state = {
        "messages": [HumanMessage(content=user_message)],
        "model": model,
        "context": {}
    }
    
    # Create the config with the thread ID
    config = {"configurable": {"thread_id": thread_id}}
    
    # Invoke the graph
    return graph.invoke(input_state, config)

# Function to stream the agent's response
def stream_agent(graph, user_message, model="anthropic/claude-3.7-sonnet", thread_id="default"):
    """Stream the agent's response."""
    # Create the input state
    input_state = {
        "messages": [HumanMessage(content=user_message)],
        "model": model,
        "context": {}
    }
    
    # Create the config with the thread ID
    config = {"configurable": {"thread_id": thread_id}}
    
    # Stream the graph's execution
    return graph.stream(input_state, config, stream_mode="values")

# Main function to create and use the agent
def create_agent():
    """Create the agent graph."""
    return build_agent_graph()
