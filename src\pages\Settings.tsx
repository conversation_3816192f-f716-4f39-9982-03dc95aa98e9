import React, { useState, useEffect } from 'react';
import { ArrowLeft, Save, AlertCircle, Sliders, Database, Upload, Globe, Bot, ToggleLeft, ToggleRight, Loader, CheckCircle, XCircle, Activity } from 'lucide-react';
import { Link, useNavigate } from 'react-router-dom';
import { Settings as SettingsType } from '../types';
import { OPENROUTER_MODELS } from '../lib/openrouter';
import { RAGAgent } from '../lib/agents/rag-agent';
import { PineconeService } from '../lib/knowledge/pinecone-service';
import { ToolUsageDashboard } from '../components/ToolUsageDashboard';

interface SettingsProps {
  settings: SettingsType;
  onSaveSettings: (settings: SettingsType) => void;
}

type SettingsTab = 'general' | 'knowledgebase' | 'agents' | 'tools';

interface AgentCard {
  id: string;
  name: string;
  description: string;
  icon: string;
  enabled: boolean;
}

export function Settings({ settings, onSaveSettings }: SettingsProps) {
  const [localSettings, setLocalSettings] = useState<SettingsType>(settings);
  const [showApiKeyWarning, setShowApiKeyWarning] = useState(false);
  const [activeTab, setActiveTab] = useState<SettingsTab>('general');
  // Website scraping state
  const [websiteUrl, setWebsiteUrl] = useState('');
  const [followLinks, setFollowLinks] = useState(true);
  const [extractPdfText, setExtractPdfText] = useState(true);
  const [includeImages, setIncludeImages] = useState(true);
  const [isScraping, setIsScraping] = useState(false);
  const [scrapingResults, setScrapingResults] = useState<{
    url: string;
    pageCount: number;
    status: 'success' | 'error';
    message?: string;
  } | null>(null);

  // Pinecone state
  const [pineconeApiKey, setPineconeApiKey] = useState(
    settings.apiKeys.pinecone || import.meta.env.VITE_PINECONE_API_KEY || ''
  );
  const [pineconeEnvironment, setPineconeEnvironment] = useState(
    settings.knowledgeBase?.pineconeEnvironment || import.meta.env.VITE_PINECONE_ENVIRONMENT || ''
  );
  const [pineconeIndex, setPineconeIndex] = useState(
    settings.knowledgeBase?.pineconeIndex || import.meta.env.VITE_PINECONE_INDEX || ''
  );
  const [pineconeHost, setPineconeHost] = useState(
    settings.knowledgeBase?.pineconeHost || import.meta.env.VITE_PINECONE_HOST || ''
  );
  const [pineconeMetric, setPineconeMetric] = useState(
    settings.knowledgeBase?.pineconeMetric || import.meta.env.VITE_PINECONE_METRIC || 'cosine'
  );
  const [pineconeDimensions, setPineconeDimensions] = useState(
    settings.knowledgeBase?.pineconeDimensions ||
    (import.meta.env.VITE_PINECONE_DIMENSIONS ? Number(import.meta.env.VITE_PINECONE_DIMENSIONS) : 1536)
  );
  const [embeddingModel, setEmbeddingModel] = useState(
    settings.knowledgeBase?.embeddingModel || import.meta.env.VITE_EMBEDDING_MODEL || 'text-embedding-ada-002'
  );

  // Connection status
  const [connectionStatus, setConnectionStatus] = useState<{
    connected: boolean;
    indexExists: boolean;
    config: Record<string, any>;
    loading: boolean;
    message?: string;
  }>({
    connected: false,
    indexExists: false,
    config: {},
    loading: false
  });

  // Check Pinecone connection when component mounts or settings change
  useEffect(() => {
    if (pineconeApiKey && pineconeEnvironment && pineconeIndex) {
      checkPineconeConnection();
    }
  }, [pineconeApiKey, pineconeEnvironment, pineconeIndex]);

  const [agents, setAgents] = useState<AgentCard[]>([
    {
      id: 'general-agent',
      name: 'General Agent',
      description: 'Versatile agent capable of handling a wide range of tasks and queries with balanced capabilities.',
      icon: 'bot',
      enabled: true
    },
    {
      id: 'rag-agent',
      name: 'RAG Agent',
      description: 'Retrieval-Augmented Generation agent that uses your knowledge base to provide more accurate responses.',
      icon: 'database',
      enabled: true
    },
    {
      id: 'ad-creative-agent',
      name: 'Ad Creative Agent',
      description: 'Specialized in creating marketing content, ad concepts, and promotional materials using GPT-4.1.',
      icon: 'pen-tool',
      enabled: true
    },
    {
      id: 'code-agent',
      name: 'Code Agent',
      description: 'Specialized in code generation, analysis, and debugging across multiple programming languages.',
      icon: 'code',
      enabled: false
    },
    {
      id: 'research-agent',
      name: 'Research Agent',
      description: 'Performs web searches and analyzes information to provide comprehensive research on any topic.',
      icon: 'search',
      enabled: true
    },
    {
      id: 'data-agent',
      name: 'Data Analysis Agent',
      description: 'Processes and analyzes data from various formats including CSV, Excel, and databases.',
      icon: 'bar-chart',
      enabled: false
    },
    {
      id: 'creative-agent',
      name: 'Creative Agent',
      description: 'Specialized in creative writing, storytelling, and content generation with a focus on originality.',
      icon: 'pen-tool',
      enabled: false
    }
  ]);
  const navigate = useNavigate();

  const toggleAgent = (agentId: string) => {
    setAgents(prevAgents =>
      prevAgents.map(agent =>
        agent.id === agentId ? { ...agent, enabled: !agent.enabled } : agent
      )
    );
  };

  const handleChange = (field: keyof SettingsType, value: any) => {
    setLocalSettings(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleApiKeyChange = (value: string) => {
    setLocalSettings(prev => ({
      ...prev,
      apiKeys: {
        ...prev.apiKeys,
        openRouter: value
      }
    }));

    // Show warning if API key is being cleared
    if (value === '' && settings.apiKeys.openRouter) {
      setShowApiKeyWarning(true);
    } else {
      setShowApiKeyWarning(false);
    }
  };

  const handleSave = () => {
    // Update Pinecone settings
    const updatedSettings = {
      ...localSettings,
      apiKeys: {
        ...localSettings.apiKeys,
        pinecone: pineconeApiKey
      },
      knowledgeBase: {
        ...localSettings.knowledgeBase,
        pineconeEnvironment,
        pineconeIndex,
        pineconeHost,
        pineconeMetric,
        pineconeDimensions,
        embeddingModel
      }
    };

    onSaveSettings(updatedSettings);
    navigate('/');
  };

  const createPineconeService = () => {
    console.log('Creating Pinecone service with OpenRouter API key:', localSettings.apiKeys.openRouter);
    return new PineconeService({
      apiKey: pineconeApiKey,
      environment: pineconeEnvironment,
      indexName: pineconeIndex,
      host: pineconeHost || `https://${pineconeIndex}-${pineconeEnvironment}.svc.pinecone.io`,
      metric: pineconeMetric,
      dimensions: pineconeDimensions,
      embeddingModel: embeddingModel,
      openAIApiKey: localSettings.apiKeys.openRouter // Use OpenRouter API key for embeddings
    });
  };

  const checkPineconeConnection = async () => {
    if (!pineconeApiKey || !pineconeEnvironment || !pineconeIndex) {
      setConnectionStatus({
        connected: false,
        indexExists: false,
        config: {},
        loading: false,
        message: 'Please enter Pinecone API key, environment, and index name.'
      });
      return false;
    }

    setConnectionStatus(prev => ({ ...prev, loading: true }));

    try {
      const pineconeService = createPineconeService();
      const status = await pineconeService.getConnectionStatus();

      setConnectionStatus({
        ...status,
        loading: false
      });

      return status.connected;
    } catch (error) {
      console.error('Error checking Pinecone connection:', error);
      setConnectionStatus({
        connected: false,
        indexExists: false,
        config: {},
        loading: false,
        message: error instanceof Error ? error.message : 'Unknown error'
      });
      return false;
    }
  };

  const initializePineconeIndex = async () => {
    if (!pineconeApiKey || !pineconeEnvironment || !pineconeIndex) {
      alert('Please enter Pinecone API key, environment, and index name.');
      return;
    }

    setConnectionStatus(prev => ({ ...prev, loading: true }));

    try {
      const pineconeService = createPineconeService();
      await pineconeService.createIndexIfNotExists();

      // Check connection status again
      await checkPineconeConnection();
    } catch (error) {
      console.error('Error initializing Pinecone index:', error);
      setConnectionStatus({
        connected: false,
        indexExists: false,
        config: {},
        loading: false,
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };

  const handleScrapeWebsite = async () => {
    if (!websiteUrl || !pineconeApiKey || !pineconeEnvironment || !pineconeIndex) {
      alert('Please enter a website URL and configure Pinecone settings first.');
      return;
    }

    setIsScraping(true);
    setScrapingResults(null);

    try {
      // Create Pinecone service with OpenRouter API key for embeddings
      const pineconeService = new PineconeService({
        apiKey: pineconeApiKey,
        environment: pineconeEnvironment,
        indexName: pineconeIndex,
        host: pineconeHost,
        metric: pineconeMetric,
        dimensions: pineconeDimensions,
        embeddingModel: embeddingModel,
        openAIApiKey: localSettings.apiKeys.openRouter // Use OpenRouter API key for embeddings
      });

      // Create RAG agent
      const ragAgent = new RAGAgent(
        {
          id: 'rag-agent',
          name: 'RAG Agent',
          description: 'Retrieval-Augmented Generation agent',
          systemPrompt: 'You are a helpful assistant with access to a knowledge base.',
          model: localSettings.defaultModel,
          enabled: true
        },
        pineconeService
      );

      // Initialize RAG agent
      await ragAgent.initialize();

      // Scrape website
      const pageCount = await ragAgent.scrapeWebsite(websiteUrl, {
        followLinks,
        extractPdfText,
        includeImages,
        maxPages: 10
      });

      // Set results
      setScrapingResults({
        url: websiteUrl,
        pageCount,
        status: 'success'
      });

    } catch (error) {
      console.error('Error scraping website:', error);
      setScrapingResults({
        url: websiteUrl,
        pageCount: 0,
        status: 'error',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    } finally {
      setIsScraping(false);
    }
  };

  return (
    <div className="h-full bg-base-black flex flex-col">
      {/* Header */}
      <header className="border-b border-white/10 backdrop-blur-md bg-base-black/50 p-4">
        <div className="flex items-center justify-between max-w-5xl mx-auto">
          <div className="flex items-center gap-3">
            <Link to="/" className="p-2 rounded-full hover:bg-white/5 transition-colors">
              <ArrowLeft className="w-5 h-5 text-gray-400" />
            </Link>
            <h1 className="text-xl font-bold text-white">Settings</h1>
          </div>
          <button
            onClick={handleSave}
            className="btn-primary flex items-center gap-2 px-4 py-2"
          >
            <Save className="w-4 h-4" />
            Save Changes
          </button>
        </div>
      </header>

      {/* Tab Navigation */}
      <div className="border-b border-white/10">
        <div className="max-w-5xl mx-auto px-6">
          <div className="flex space-x-8">
            <button
              onClick={() => setActiveTab('general')}
              className={`py-4 px-4 relative font-medium text-sm transition-colors ${
                activeTab === 'general'
                  ? 'text-neon-green'
                  : 'text-gray-400 hover:text-gray-200'
              }`}
            >
              <span className="flex items-center gap-2">
                <Sliders className="w-4 h-4" />
                General Settings
              </span>
              {activeTab === 'general' && (
                <span className="absolute bottom-0 left-0 w-full h-0.5 bg-neon-green"></span>
              )}
            </button>

            <button
              onClick={() => setActiveTab('knowledgebase')}
              className={`py-4 px-4 relative font-medium text-sm transition-colors ${
                activeTab === 'knowledgebase'
                  ? 'text-neon-green'
                  : 'text-gray-400 hover:text-gray-200'
              }`}
            >
              <span className="flex items-center gap-2">
                <Database className="w-4 h-4" />
                Knowledge Base
              </span>
              {activeTab === 'knowledgebase' && (
                <span className="absolute bottom-0 left-0 w-full h-0.5 bg-neon-green"></span>
              )}
            </button>

            <button
              onClick={() => setActiveTab('agents')}
              className={`py-4 px-4 relative font-medium text-sm transition-colors ${
                activeTab === 'agents'
                  ? 'text-neon-green'
                  : 'text-gray-400 hover:text-gray-200'
              }`}
            >
              <span className="flex items-center gap-2">
                <Bot className="w-4 h-4" />
                Agents
              </span>
              {activeTab === 'agents' && (
                <span className="absolute bottom-0 left-0 w-full h-0.5 bg-neon-green"></span>
              )}
            </button>

            <button
              onClick={() => setActiveTab('tools')}
              className={`py-4 px-4 relative font-medium text-sm transition-colors ${
                activeTab === 'tools'
                  ? 'text-neon-green'
                  : 'text-gray-400 hover:text-gray-200'
              }`}
            >
              <span className="flex items-center gap-2">
                <Activity className="w-4 h-4" />
                Tool Usage
              </span>
              {activeTab === 'tools' && (
                <span className="absolute bottom-0 left-0 w-full h-0.5 bg-neon-green"></span>
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Settings Content */}
      <div className="flex-1 overflow-y-auto p-6 custom-scrollbar">
        <div className="max-w-5xl mx-auto space-y-12">
          {activeTab === 'general' && (
            <>
              {/* Model Settings */}
              <section className="space-y-6">
                <h2 className="text-2xl font-bold text-white border-b border-white/10 pb-2">Model Settings</h2>

                <div className="space-y-6 max-w-2xl">
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-300">
                      Default Model
                    </label>
                    <select
                      value={localSettings.defaultModel}
                      onChange={(e) => handleChange('defaultModel', e.target.value)}
                      className="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2 text-white [&>option]:text-black"
                    >
                      {Object.entries(OPENROUTER_MODELS).map(([id, name]) => (
                        <option key={id} value={id}>
                          {name}
                        </option>
                      ))}
                    </select>
                    <p className="text-sm text-gray-400">
                      This model will be used by default for new conversations.
                    </p>
                  </div>

                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-300">
                      Temperature
                    </label>
                    <div className="flex items-center gap-4">
                      <input
                        type="range"
                        min="0"
                        max="1"
                        step="0.1"
                        value={localSettings.temperature}
                        onChange={(e) => handleChange('temperature', parseFloat(e.target.value))}
                        className="flex-1"
                      />
                      <span className="text-white w-10 text-center">
                        {localSettings.temperature.toFixed(1)}
                      </span>
                    </div>
                    <p className="text-sm text-gray-400">
                      Lower values produce more focused and deterministic responses. Higher values produce more creative and varied responses.
                    </p>
                  </div>

                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-300">
                      Max Tokens
                    </label>
                    <input
                      type="number"
                      min="100"
                      max="4000"
                      step="100"
                      value={localSettings.maxTokens}
                      onChange={(e) => handleChange('maxTokens', parseInt(e.target.value))}
                      className="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2 text-white"
                    />
                    <p className="text-sm text-gray-400">
                      Maximum number of tokens to generate in the response.
                    </p>
                  </div>
                </div>
              </section>

              {/* API Keys */}
              <section className="space-y-6">
                <h2 className="text-2xl font-bold text-white border-b border-white/10 pb-2">API Keys</h2>

                <div className="space-y-6 max-w-2xl">
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-300">
                      OpenRouter API Key
                    </label>
                    <input
                      type="password"
                      value={localSettings.apiKeys.openRouter || ''}
                      onChange={(e) => handleApiKeyChange(e.target.value)}
                      placeholder="Enter your OpenRouter API key"
                      className="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2 text-white"
                    />
                    <p className="text-sm text-gray-400">
                      Required to access language models through OpenRouter.
                    </p>
                  </div>

                  {showApiKeyWarning && (
                    <div className="flex items-start gap-3 p-4 bg-warning/10 border border-warning/30 rounded-lg">
                      <AlertCircle className="w-5 h-5 text-warning shrink-0 mt-0.5" />
                      <p className="text-sm text-gray-300">
                        Removing your API key will prevent the application from accessing language models. Make sure you have saved your API key somewhere secure before removing it.
                      </p>
                    </div>
                  )}
                </div>
              </section>

              {/* Appearance */}
              <section className="space-y-6">
                <h2 className="text-2xl font-bold text-white border-b border-white/10 pb-2">Appearance</h2>

                <div className="space-y-6 max-w-2xl">
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-300">
                      Theme
                    </label>
                    <div className="grid grid-cols-2 gap-4">
                      <label className={`flex items-center gap-3 p-4 rounded-lg border cursor-pointer transition-colors ${
                        localSettings.theme === 'dark'
                          ? 'border-neon-green bg-neon-green/10'
                          : 'border-white/10 bg-white/5 hover:bg-white/10'
                      }`}>
                        <input
                          type="radio"
                          name="theme"
                          value="dark"
                          checked={localSettings.theme === 'dark'}
                          onChange={() => handleChange('theme', 'dark')}
                          className="sr-only"
                        />
                        <span className="text-white">Dark</span>
                      </label>

                      <label className={`flex items-center gap-3 p-4 rounded-lg border cursor-pointer transition-colors ${
                        localSettings.theme === 'light'
                          ? 'border-neon-green bg-neon-green/10'
                          : 'border-white/10 bg-white/5 hover:bg-white/10'
                      }`}>
                        <input
                          type="radio"
                          name="theme"
                          value="light"
                          checked={localSettings.theme === 'light'}
                          onChange={() => handleChange('theme', 'light')}
                          className="sr-only"
                        />
                        <span className="text-white">Light</span>
                      </label>
                    </div>
                  </div>
                </div>
              </section>
            </>
          )}

          {activeTab === 'knowledgebase' && (
            <>
              {/* Company Information */}
              <section className="space-y-6">
                <h2 className="text-2xl font-bold text-white border-b border-white/10 pb-2">Company Information</h2>

                <div className="space-y-8 max-w-4xl">
                  <p className="text-gray-300">
                    Create a comprehensive company profile to help the AI better understand your business and provide more accurate responses to company-related questions.
                  </p>

                  {/* Basic Information */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-white">Basic Information</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <label className="block text-sm font-medium text-gray-300">
                          Company Name
                        </label>
                        <input
                          type="text"
                          placeholder="Enter your company name"
                          className="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2 text-white"
                        />
                      </div>

                      <div className="space-y-2">
                        <label className="block text-sm font-medium text-gray-300">
                          Founded Year
                        </label>
                        <input
                          type="number"
                          placeholder="e.g., 2010"
                          className="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2 text-white"
                        />
                      </div>

                      <div className="space-y-2 md:col-span-2">
                        <label className="block text-sm font-medium text-gray-300">
                          Company Description
                        </label>
                        <textarea
                          rows={4}
                          placeholder="Enter a detailed description of your company, including its mission, vision, and core values"
                          className="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2 text-white"
                        />
                      </div>

                      <div className="space-y-2">
                        <label className="block text-sm font-medium text-gray-300">
                          Industry
                        </label>
                        <input
                          type="text"
                          placeholder="e.g., Technology, Healthcare, Finance"
                          className="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2 text-white"
                        />
                      </div>

                      <div className="space-y-2">
                        <label className="block text-sm font-medium text-gray-300">
                          Company Size
                        </label>
                        <select
                          className="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2 text-white [&>option]:text-black"
                        >
                          <option value="">Select company size</option>
                          <option value="1-10">1-10 employees</option>
                          <option value="11-50">11-50 employees</option>
                          <option value="51-200">51-200 employees</option>
                          <option value="201-500">201-500 employees</option>
                          <option value="501-1000">501-1000 employees</option>
                          <option value="1001+">1001+ employees</option>
                        </select>
                      </div>
                    </div>
                  </div>

                  {/* Contact Information */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-white">Contact Information</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <label className="block text-sm font-medium text-gray-300">
                          Website
                        </label>
                        <input
                          type="url"
                          placeholder="https://example.com"
                          className="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2 text-white"
                        />
                      </div>

                      <div className="space-y-2">
                        <label className="block text-sm font-medium text-gray-300">
                          Email
                        </label>
                        <input
                          type="email"
                          placeholder="<EMAIL>"
                          className="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2 text-white"
                        />
                      </div>

                      <div className="space-y-2">
                        <label className="block text-sm font-medium text-gray-300">
                          Phone
                        </label>
                        <input
                          type="tel"
                          placeholder="+****************"
                          className="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2 text-white"
                        />
                      </div>

                      <div className="space-y-2 md:col-span-2">
                        <label className="block text-sm font-medium text-gray-300">
                          Headquarters Address
                        </label>
                        <textarea
                          rows={2}
                          placeholder="Enter your company's headquarters address"
                          className="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2 text-white"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Products and Services */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-white">Products and Services</h3>
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <label className="block text-sm font-medium text-gray-300">
                          Main Products/Services
                        </label>
                        <textarea
                          rows={4}
                          placeholder="Describe your company's main products or services"
                          className="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2 text-white"
                        />
                      </div>

                      <div className="space-y-2">
                        <label className="block text-sm font-medium text-gray-300">
                          Target Audience
                        </label>
                        <input
                          type="text"
                          placeholder="e.g., Small businesses, Enterprise companies, Consumers"
                          className="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2 text-white"
                        />
                      </div>

                      <div className="space-y-2">
                        <label className="block text-sm font-medium text-gray-300">
                          Unique Selling Proposition
                        </label>
                        <textarea
                          rows={3}
                          placeholder="What makes your company's offerings unique in the market?"
                          className="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2 text-white"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Company Culture */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-white">Company Culture</h3>
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <label className="block text-sm font-medium text-gray-300">
                          Core Values
                        </label>
                        <textarea
                          rows={3}
                          placeholder="List and describe your company's core values"
                          className="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2 text-white"
                        />
                      </div>

                      <div className="space-y-2">
                        <label className="block text-sm font-medium text-gray-300">
                          Mission Statement
                        </label>
                        <textarea
                          rows={2}
                          placeholder="Your company's mission statement"
                          className="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2 text-white"
                        />
                      </div>

                      <div className="space-y-2">
                        <label className="block text-sm font-medium text-gray-300">
                          Vision Statement
                        </label>
                        <textarea
                          rows={2}
                          placeholder="Your company's vision for the future"
                          className="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2 text-white"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Additional Information */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-white">Additional Information</h3>
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <label className="block text-sm font-medium text-gray-300">
                          Social Media Profiles
                        </label>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <input
                            type="url"
                            placeholder="LinkedIn URL"
                            className="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2 text-white"
                          />
                          <input
                            type="url"
                            placeholder="Twitter URL"
                            className="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2 text-white"
                          />
                        </div>
                      </div>

                      <div className="space-y-2">
                        <label className="block text-sm font-medium text-gray-300">
                          Key Achievements
                        </label>
                        <textarea
                          rows={3}
                          placeholder="Notable achievements, awards, or milestones"
                          className="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2 text-white"
                        />
                      </div>

                      <div className="space-y-2">
                        <label className="block text-sm font-medium text-gray-300">
                          FAQ
                        </label>
                        <textarea
                          rows={5}
                          placeholder="Common questions and answers about your company"
                          className="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2 text-white"
                        />
                        <p className="text-sm text-gray-400">
                          Format as "Q: Question? A: Answer." for each FAQ item.
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="pt-4">
                    <p className="text-sm text-gray-400">
                      This comprehensive company profile will be used to provide context to the AI when answering questions about your company. The more detailed information you provide, the more accurate and helpful the responses will be.
                    </p>
                  </div>
                </div>
              </section>

              {/* Document Upload */}
              <section className="space-y-6">
                <h2 className="text-2xl font-bold text-white border-b border-white/10 pb-2">Document Upload</h2>

                <div className="space-y-6 max-w-2xl">
                  <div className="space-y-4">
                    <div className="border-2 border-dashed border-white/20 rounded-lg p-8 text-center">
                      <div className="flex flex-col items-center">
                        <Upload className="w-12 h-12 text-gray-400 mb-4" />
                        <p className="text-gray-300 mb-2">Drag and drop files here, or click to select files</p>
                        <p className="text-sm text-gray-400 mb-4">Supported formats: PDF, DOCX, TXT, CSV, MD</p>
                        <button className="btn-secondary py-2 px-4 text-sm">
                          Select Files
                        </button>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <h3 className="text-lg font-medium text-white">Uploaded Documents</h3>
                      <div className="bg-white/5 border border-white/10 rounded-lg overflow-hidden">
                        <div className="p-4 text-center text-gray-400">
                          No documents uploaded yet
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </section>

              {/* Website Scraping */}
              <section className="space-y-6">
                <h2 className="text-2xl font-bold text-white border-b border-white/10 pb-2">Website Scraping</h2>

                <div className="space-y-6 max-w-2xl">
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-300">
                      Website URL
                    </label>
                    <div className="flex gap-2">
                      <div className="flex-1 flex items-center bg-white/5 border border-white/10 rounded-lg px-4">
                        <Globe className="w-5 h-5 text-gray-400 mr-2" />
                        <input
                          type="url"
                          value={websiteUrl}
                          onChange={(e) => setWebsiteUrl(e.target.value)}
                          placeholder="https://example.com"
                          className="flex-1 bg-transparent py-2 text-white focus:outline-none"
                        />
                      </div>
                      <button
                        className="btn-primary py-2 px-4 flex items-center gap-2"
                        onClick={handleScrapeWebsite}
                        disabled={isScraping || !websiteUrl || !pineconeApiKey || !pineconeEnvironment || !pineconeIndex}
                      >
                        {isScraping ? (
                          <>
                            <Loader className="w-4 h-4 animate-spin" />
                            Scraping...
                          </>
                        ) : (
                          'Scrape'
                        )}
                      </button>
                    </div>
                    <p className="text-sm text-gray-400">
                      Enter the URL of a website to scrape content for your knowledge base.
                    </p>
                  </div>

                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-300">
                      Scraping Options
                    </label>
                    <div className="space-y-2">
                      <label className="flex items-center gap-2">
                        <input
                          type="checkbox"
                          checked={followLinks}
                          onChange={(e) => setFollowLinks(e.target.checked)}
                          className="rounded text-neon-green bg-white/5 border-white/20"
                        />
                        <span className="text-gray-300">Follow links within the same domain</span>
                      </label>
                      <label className="flex items-center gap-2">
                        <input
                          type="checkbox"
                          checked={extractPdfText}
                          onChange={(e) => setExtractPdfText(e.target.checked)}
                          className="rounded text-neon-green bg-white/5 border-white/20"
                        />
                        <span className="text-gray-300">Extract text from PDFs</span>
                      </label>
                      <label className="flex items-center gap-2">
                        <input
                          type="checkbox"
                          checked={includeImages}
                          onChange={(e) => setIncludeImages(e.target.checked)}
                          className="rounded text-neon-green bg-white/5 border-white/20"
                        />
                        <span className="text-gray-300">Include images (with alt text)</span>
                      </label>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <h3 className="text-lg font-medium text-white">Scraped Content</h3>
                    <div className="bg-white/5 border border-white/10 rounded-lg overflow-hidden">
                      {isScraping ? (
                        <div className="p-8 text-center">
                          <Loader className="w-8 h-8 text-neon-green animate-spin mx-auto mb-4" />
                          <p className="text-gray-300">Scraping {websiteUrl}...</p>
                          <p className="text-sm text-gray-400 mt-2">This may take a few minutes depending on the size of the website.</p>
                        </div>
                      ) : scrapingResults ? (
                        <div className="p-4">
                          <div className={`p-4 rounded-lg ${
                            scrapingResults.status === 'success'
                              ? 'bg-neon-green/10 border border-neon-green/30'
                              : 'bg-red-500/10 border border-red-500/30'
                          }`}>
                            <div className="flex items-start gap-3">
                              {scrapingResults.status === 'success' ? (
                                <>
                                  <div className="w-8 h-8 rounded-full bg-neon-green/20 flex items-center justify-center flex-shrink-0">
                                    <span className="text-neon-green">✓</span>
                                  </div>
                                  <div>
                                    <h4 className="font-medium text-white">Scraping Completed</h4>
                                    <p className="text-gray-300 mt-1">
                                      Successfully scraped {scrapingResults.pageCount} pages from {scrapingResults.url}
                                    </p>
                                    <p className="text-sm text-gray-400 mt-2">
                                      The content has been processed and added to your knowledge base.
                                    </p>
                                  </div>
                                </>
                              ) : (
                                <>
                                  <div className="w-8 h-8 rounded-full bg-red-500/20 flex items-center justify-center flex-shrink-0">
                                    <span className="text-red-500">✗</span>
                                  </div>
                                  <div>
                                    <h4 className="font-medium text-white">Scraping Failed</h4>
                                    <p className="text-gray-300 mt-1">
                                      Failed to scrape {scrapingResults.url}
                                    </p>
                                    {scrapingResults.message && (
                                      <p className="text-sm text-gray-400 mt-2">
                                        Error: {scrapingResults.message}
                                      </p>
                                    )}
                                  </div>
                                </>
                              )}
                            </div>
                          </div>
                        </div>
                      ) : (
                        <div className="p-4 text-center text-gray-400">
                          No content scraped yet
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </section>

              {/* Pinecone DB Settings */}
              <section className="space-y-6">
                <h2 className="text-2xl font-bold text-white border-b border-white/10 pb-2">Pinecone DB Settings</h2>

                <div className="space-y-6 max-w-2xl">
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-300">
                      Pinecone API Key
                    </label>
                    <input
                      type="password"
                      value={pineconeApiKey}
                      onChange={(e) => setPineconeApiKey(e.target.value)}
                      placeholder="Enter your Pinecone API key"
                      className="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2 text-white"
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <label className="block text-sm font-medium text-gray-300">
                        Pinecone Environment
                      </label>
                      <input
                        type="text"
                        value={pineconeEnvironment}
                        onChange={(e) => setPineconeEnvironment(e.target.value)}
                        placeholder="e.g., us-east-1"
                        className="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2 text-white"
                      />
                    </div>

                    <div className="space-y-2">
                      <label className="block text-sm font-medium text-gray-300">
                        Index Name
                      </label>
                      <input
                        type="text"
                        value={pineconeIndex}
                        onChange={(e) => setPineconeIndex(e.target.value)}
                        placeholder="e.g., roofermax-ciouxjl"
                        className="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2 text-white"
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-300">
                      Pinecone Host
                    </label>
                    <input
                      type="text"
                      value={pineconeHost}
                      onChange={(e) => setPineconeHost(e.target.value)}
                      placeholder="e.g., https://roofermax-ciouxjl.svc.aped-4627-b74a.pinecone.io"
                      className="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2 text-white"
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <label className="block text-sm font-medium text-gray-300">
                        Metric
                      </label>
                      <select
                        value={pineconeMetric}
                        onChange={(e) => setPineconeMetric(e.target.value)}
                        className="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2 text-white [&>option]:text-black"
                      >
                        <option value="cosine">Cosine</option>
                        <option value="dotproduct">Dot Product</option>
                        <option value="euclidean">Euclidean</option>
                      </select>
                    </div>

                    <div className="space-y-2">
                      <label className="block text-sm font-medium text-gray-300">
                        Dimensions
                      </label>
                      <input
                        type="number"
                        value={pineconeDimensions}
                        onChange={(e) => setPineconeDimensions(Number(e.target.value))}
                        placeholder="e.g., 3072"
                        className="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2 text-white"
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-300">
                      Embedding Model
                    </label>
                    <select
                      value={embeddingModel}
                      onChange={(e) => setEmbeddingModel(e.target.value)}
                      className="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2 text-white [&>option]:text-black"
                    >
                      <option value="text-embedding-3-large">text-embedding-3-large</option>
                      <option value="text-embedding-3-small">text-embedding-3-small</option>
                      <option value="text-embedding-ada-002">text-embedding-ada-002</option>
                    </select>
                  </div>

                  <div className="mt-6 flex gap-4">
                    <button
                      className="btn-primary py-2 px-4 flex items-center gap-2"
                      onClick={checkPineconeConnection}
                      disabled={!pineconeApiKey || !pineconeEnvironment || !pineconeIndex || connectionStatus.loading}
                    >
                      {connectionStatus.loading ? (
                        <>
                          <Loader className="w-4 h-4 animate-spin" />
                          Checking...
                        </>
                      ) : (
                        'Test Connection'
                      )}
                    </button>

                    <button
                      className="btn-secondary py-2 px-4 flex items-center gap-2"
                      onClick={initializePineconeIndex}
                      disabled={!pineconeApiKey || !pineconeEnvironment || !pineconeIndex || connectionStatus.loading}
                    >
                      {connectionStatus.loading ? (
                        <>
                          <Loader className="w-4 h-4 animate-spin" />
                          Initializing...
                        </>
                      ) : (
                        'Initialize Index'
                      )}
                    </button>
                  </div>

                  {/* Connection Status */}
                  <div className="mt-4">
                    {connectionStatus.connected ? (
                      <div className="p-3 bg-neon-green/10 border border-neon-green/30 rounded-lg flex items-center gap-3">
                        <CheckCircle className="w-5 h-5 text-neon-green" />
                        <div>
                          <p className="text-white font-medium">Connected to Pinecone</p>
                          <p className="text-sm text-gray-300">
                            Index: {connectionStatus.indexExists ? 'Exists' : 'Not Found'} •
                            Environment: {connectionStatus.config.environment || pineconeEnvironment} •
                            Dimensions: {connectionStatus.config.dimensions || pineconeDimensions}
                          </p>
                        </div>
                      </div>
                    ) : (
                      <div className="p-3 bg-red-500/10 border border-red-500/30 rounded-lg flex items-center gap-3">
                        <XCircle className="w-5 h-5 text-red-500" />
                        <div>
                          <p className="text-white font-medium">Not Connected to Pinecone</p>
                          {connectionStatus.message && (
                            <p className="text-sm text-gray-300">{connectionStatus.message}</p>
                          )}
                        </div>
                      </div>
                    )}
                  </div>

                  <div className="mt-4">
                    <p className="text-sm text-gray-400">
                      The Pinecone vector database is used to store and retrieve knowledge base embeddings.
                      You'll need to create an account at <a href="https://www.pinecone.io/" target="_blank" rel="noopener noreferrer" className="text-neon-green hover:underline">pinecone.io</a> to get your API key.
                    </p>
                  </div>
                </div>
              </section>
            </>
          )}

          {activeTab === 'agents' && (
            <>
              <section className="space-y-6">
                <h2 className="text-2xl font-bold text-white border-b border-white/10 pb-2">Agent Management</h2>

                <div className="space-y-4">
                  <p className="text-gray-300">
                    Enable or disable agents to customize your AI assistant's capabilities. Each agent specializes in different tasks and can be activated based on your needs.
                  </p>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                    {agents.map(agent => (
                      <div
                        key={agent.id}
                        className={`rounded-xl border p-6 transition-all duration-300 ${
                          agent.enabled
                            ? 'border-neon-green bg-neon-green/5 shadow-[0_0_15px_rgba(0,255,148,0.15)]'
                            : 'border-white/10 bg-white/5 hover:bg-white/10'
                        }`}
                      >
                        <div className="flex justify-between items-start mb-4">
                          <div className="flex items-center gap-3">
                            <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${
                              agent.enabled ? 'bg-neon-green/20' : 'bg-white/10'
                            }`}>
                              <Bot className={`w-6 h-6 ${agent.enabled ? 'text-neon-green' : 'text-gray-400'}`} />
                            </div>
                            <h3 className="text-lg font-semibold text-white">{agent.name}</h3>
                          </div>
                          <button
                            onClick={() => toggleAgent(agent.id)}
                            className="text-gray-300 hover:text-neon-green transition-colors"
                            aria-label={agent.enabled ? "Disable agent" : "Enable agent"}
                          >
                            {agent.enabled ? (
                              <ToggleRight className="w-10 h-6 text-neon-green" />
                            ) : (
                              <ToggleLeft className="w-10 h-6" />
                            )}
                          </button>
                        </div>
                        <p className="text-gray-300 text-sm mb-4">{agent.description}</p>
                        <div className="flex justify-between items-center">
                          <span className={`text-xs px-2 py-1 rounded-full ${
                            agent.enabled
                              ? 'bg-neon-green/20 text-neon-green'
                              : 'bg-white/10 text-gray-400'
                          }`}>
                            {agent.enabled ? 'Active' : 'Inactive'}
                          </span>
                          <button className="text-xs text-gray-400 hover:text-white underline">
                            Configure
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </section>

              <section className="space-y-6 mt-12">
                <h2 className="text-2xl font-bold text-white border-b border-white/10 pb-2">Agent Orchestration</h2>

                <div className="space-y-6 max-w-2xl">
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-300">
                      Default Agent Selection
                    </label>
                    <select
                      className="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2 text-white [&>option]:text-black"
                      defaultValue="general-agent"
                      onChange={(e) => {
                        // In a real implementation, this would update a setting
                        console.log("Default agent selection:", e.target.value);
                      }}
                    >
                      <option value="auto">Automatic (Based on query)</option>
                      <option value="general-agent">General Agent</option>
                      <option value="rag-agent">RAG Agent</option>
                      <option value="code-agent">Code Agent</option>
                      <option value="research-agent">Research Agent</option>
                      <option value="data-agent">Data Analysis Agent</option>
                      <option value="creative-agent">Creative Agent</option>
                      <option value="ad-creative-agent">Ad Creative Agent</option>
                    </select>
                    <p className="text-sm text-gray-400">
                      Choose how agents are selected when processing queries. "Automatic" uses a router to select the most appropriate agent.
                    </p>
                  </div>

                  <div className="space-y-2">
                    <label className="flex items-center gap-2">
                      <input
                        type="checkbox"
                        className="rounded text-neon-green bg-white/5 border-white/20"
                        defaultChecked={true}
                        onChange={(e) => {
                          // In a real implementation, this would update a setting
                          console.log("Agent collaboration:", e.target.checked);
                        }}
                      />
                      <span className="text-gray-300">Allow agent collaboration (multiple agents can work together)</span>
                    </label>
                  </div>

                  <div className="space-y-2">
                    <label className="flex items-center gap-2">
                      <input
                        type="checkbox"
                        className="rounded text-neon-green bg-white/5 border-white/20"
                        defaultChecked={true}
                        onChange={(e) => {
                          // In a real implementation, this would update a setting
                          console.log("Show agent reasoning:", e.target.checked);
                        }}
                      />
                      <span className="text-gray-300">Show agent reasoning in responses</span>
                    </label>
                  </div>
                </div>
              </section>
            </>
          )}

          {activeTab === 'tools' && (
            <>
              <section className="space-y-6">
                <h2 className="text-2xl font-bold text-white border-b border-white/10 pb-2">Tool Usage Dashboard</h2>

                <div className="space-y-4">
                  <p className="text-gray-300">
                    Monitor how agents are using MCP tools across your system. This dashboard shows real-time tool usage statistics and events.
                  </p>

                  <ToolUsageDashboard showEvents={true} showStats={true} maxEvents={20} />
                </div>
              </section>

              <section className="space-y-6 mt-12">
                <h2 className="text-2xl font-bold text-white border-b border-white/10 pb-2">MCP Server Status</h2>

                <div className="space-y-4">
                  <p className="text-gray-300">
                    View the status of your MCP servers and the tools they provide.
                  </p>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                    <div className="rounded-xl border border-neon-green/30 bg-neon-green/5 p-6 transition-all duration-300 hover:shadow-[0_0_15px_rgba(0,255,148,0.15)]">
                      <div className="flex items-center gap-3 mb-4">
                        <div className="w-10 h-10 rounded-lg bg-neon-green/20 flex items-center justify-center">
                          <Activity className="w-6 h-6 text-neon-green" />
                        </div>
                        <div>
                          <h3 className="text-lg font-semibold text-white">Context7</h3>
                          <p className="text-xs text-neon-green">Running</p>
                        </div>
                      </div>
                      <p className="text-gray-300 text-sm mb-4">Provides code context and documentation tools.</p>
                      <div className="text-xs text-gray-400">
                        <p>Server ID: context7</p>
                        <p>Tools: 5 available</p>
                      </div>
                    </div>

                    <div className="rounded-xl border border-neon-green/30 bg-neon-green/5 p-6 transition-all duration-300 hover:shadow-[0_0_15px_rgba(0,255,148,0.15)]">
                      <div className="flex items-center gap-3 mb-4">
                        <div className="w-10 h-10 rounded-lg bg-neon-green/20 flex items-center justify-center">
                          <Activity className="w-6 h-6 text-neon-green" />
                        </div>
                        <div>
                          <h3 className="text-lg font-semibold text-white">GitHub</h3>
                          <p className="text-xs text-neon-green">Running</p>
                        </div>
                      </div>
                      <p className="text-gray-300 text-sm mb-4">Provides repository access and management tools.</p>
                      <div className="text-xs text-gray-400">
                        <p>Server ID: github</p>
                        <p>Tools: 8 available</p>
                      </div>
                    </div>

                    <div className="rounded-xl border border-neon-green/30 bg-neon-green/5 p-6 transition-all duration-300 hover:shadow-[0_0_15px_rgba(0,255,148,0.15)]">
                      <div className="flex items-center gap-3 mb-4">
                        <div className="w-10 h-10 rounded-lg bg-neon-green/20 flex items-center justify-center">
                          <Activity className="w-6 h-6 text-neon-green" />
                        </div>
                        <div>
                          <h3 className="text-lg font-semibold text-white">Supabase</h3>
                          <p className="text-xs text-neon-green">Running</p>
                        </div>
                      </div>
                      <p className="text-gray-300 text-sm mb-4">Provides database access and management tools.</p>
                      <div className="text-xs text-gray-400">
                        <p>Server ID: supabase</p>
                        <p>Tools: 6 available</p>
                      </div>
                    </div>

                    <div className="rounded-xl border border-neon-green/30 bg-neon-green/5 p-6 transition-all duration-300 hover:shadow-[0_0_15px_rgba(0,255,148,0.15)]">
                      <div className="flex items-center gap-3 mb-4">
                        <div className="w-10 h-10 rounded-lg bg-neon-green/20 flex items-center justify-center">
                          <Activity className="w-6 h-6 text-neon-green" />
                        </div>
                        <div>
                          <h3 className="text-lg font-semibold text-white">VAPI</h3>
                          <p className="text-xs text-neon-green">Running</p>
                        </div>
                      </div>
                      <p className="text-gray-300 text-sm mb-4">Provides voice and audio processing tools.</p>
                      <div className="text-xs text-gray-400">
                        <p>Server ID: vapi</p>
                        <p>Tools: 3 available</p>
                      </div>
                    </div>

                    <div className="rounded-xl border border-white/10 bg-white/5 p-6 transition-all duration-300 hover:bg-white/10">
                      <div className="flex items-center gap-3 mb-4">
                        <div className="w-10 h-10 rounded-lg bg-white/10 flex items-center justify-center">
                          <Activity className="w-6 h-6 text-gray-400" />
                        </div>
                        <div>
                          <h3 className="text-lg font-semibold text-white">Telnyx</h3>
                          <p className="text-xs text-gray-400">Stopped</p>
                        </div>
                      </div>
                      <p className="text-gray-300 text-sm mb-4">Provides telephony and messaging tools.</p>
                      <div className="text-xs text-gray-400">
                        <p>Server ID: telnyx</p>
                        <p>Tools: 4 available</p>
                      </div>
                    </div>
                  </div>
                </div>
              </section>
            </>
          )}
        </div>
      </div>
    </div>
  );
}
