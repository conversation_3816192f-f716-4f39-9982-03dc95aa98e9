/**
 * MCP Server Manager Component
 * 
 * This component provides a UI for managing MCP servers, including
 * adding, removing, configuring, and monitoring servers.
 */

import React, { useState, useEffect } from 'react';
import { 
  addMCPServer, 
  removeMCPServer, 
  getSystemStatus, 
  isEnhancedModeActive 
} from '../lib/agents/agent-service';

interface MCPServer {
  id: string;
  name: string;
  type: string;
  enabled: boolean;
  priority: number;
  endpoint?: string;
  apiKey?: string;
  status?: 'online' | 'offline' | 'degraded' | 'error';
  toolCount?: number;
  lastActivity?: number;
}

interface MCPServerManagerProps {
  onClose: () => void;
}

export const MCPServerManager: React.FC<MCPServerManagerProps> = ({ onClose }) => {
  const [servers, setServers] = useState<MCPServer[]>([]);
  const [showAddForm, setShowAddForm] = useState(false);
  const [systemStatus, setSystemStatus] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Form state for adding new server
  const [newServer, setNewServer] = useState({
    id: '',
    name: '',
    type: 'context7',
    enabled: true,
    priority: 1,
    endpoint: '',
    apiKey: ''
  });

  useEffect(() => {
    loadSystemStatus();
    const interval = setInterval(loadSystemStatus, 5000); // Refresh every 5 seconds
    return () => clearInterval(interval);
  }, []);

  const loadSystemStatus = async () => {
    try {
      if (!isEnhancedModeActive()) {
        setError('Enhanced mode is not active. MCP server management is not available.');
        setLoading(false);
        return;
      }

      const status = getSystemStatus();
      setSystemStatus(status);
      
      if (status.servers) {
        setServers(status.servers.map((server: any) => ({
          id: server.id,
          name: server.name || server.id,
          type: server.type || 'unknown',
          enabled: server.enabled !== false,
          priority: server.priority || 1,
          endpoint: server.endpoint,
          apiKey: server.apiKey ? '***' : undefined,
          status: server.status?.status || 'unknown',
          toolCount: server.toolCount || 0,
          lastActivity: server.lastActivity
        })));
      }
      
      setLoading(false);
      setError(null);
    } catch (err) {
      console.error('Error loading system status:', err);
      setError(err instanceof Error ? err.message : 'Failed to load system status');
      setLoading(false);
    }
  };

  const handleAddServer = async () => {
    try {
      if (!newServer.id || !newServer.name) {
        setError('Server ID and name are required');
        return;
      }

      const serverConfig = {
        id: newServer.id,
        name: newServer.name,
        type: newServer.type,
        enabled: newServer.enabled,
        priority: newServer.priority,
        endpoint: newServer.endpoint || undefined,
        apiKey: newServer.apiKey || undefined,
        timeout: 30000,
        customConfig: {}
      };

      await addMCPServer(serverConfig);
      
      // Reset form
      setNewServer({
        id: '',
        name: '',
        type: 'context7',
        enabled: true,
        priority: 1,
        endpoint: '',
        apiKey: ''
      });
      
      setShowAddForm(false);
      await loadSystemStatus();
      setError(null);
    } catch (err) {
      console.error('Error adding server:', err);
      setError(err instanceof Error ? err.message : 'Failed to add server');
    }
  };

  const handleRemoveServer = async (serverId: string) => {
    try {
      await removeMCPServer(serverId);
      await loadSystemStatus();
      setError(null);
    } catch (err) {
      console.error('Error removing server:', err);
      setError(err instanceof Error ? err.message : 'Failed to remove server');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online': return 'text-green-600';
      case 'degraded': return 'text-yellow-600';
      case 'offline': return 'text-gray-600';
      case 'error': return 'text-red-600';
      default: return 'text-gray-400';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'online': return '🟢';
      case 'degraded': return '🟡';
      case 'offline': return '⚫';
      case 'error': return '🔴';
      default: return '⚪';
    }
  };

  if (loading) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="ml-2">Loading MCP servers...</span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="bg-blue-600 text-white px-6 py-4 flex justify-between items-center">
          <h2 className="text-xl font-semibold">MCP Server Manager</h2>
          <button
            onClick={onClose}
            className="text-white hover:text-gray-200 text-2xl"
          >
            ×
          </button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-80px)]">
          {error && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
              {error}
            </div>
          )}

          {/* System Status */}
          {systemStatus && (
            <div className="bg-gray-50 rounded-lg p-4 mb-6">
              <h3 className="text-lg font-semibold mb-2">System Status</h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <span className="text-gray-600">Mode:</span>
                  <span className="ml-2 font-medium">
                    {systemStatus.status?.initialized ? 'Enhanced' : 'Basic'}
                  </span>
                </div>
                <div>
                  <span className="text-gray-600">Servers:</span>
                  <span className="ml-2 font-medium">{systemStatus.status?.serverCount || 0}</span>
                </div>
                <div>
                  <span className="text-gray-600">Tools:</span>
                  <span className="ml-2 font-medium">{systemStatus.status?.toolCount || 0}</span>
                </div>
                <div>
                  <span className="text-gray-600">Agents:</span>
                  <span className="ml-2 font-medium">{systemStatus.status?.agentCount || 0}</span>
                </div>
              </div>
            </div>
          )}

          {/* Add Server Button */}
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold">MCP Servers</h3>
            <button
              onClick={() => setShowAddForm(true)}
              className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
              disabled={!isEnhancedModeActive()}
            >
              Add Server
            </button>
          </div>

          {/* Add Server Form */}
          {showAddForm && (
            <div className="bg-gray-50 rounded-lg p-4 mb-6">
              <h4 className="font-semibold mb-3">Add New MCP Server</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Server ID *
                  </label>
                  <input
                    type="text"
                    value={newServer.id}
                    onChange={(e) => setNewServer({ ...newServer, id: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md"
                    placeholder="e.g., my-server"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Server Name *
                  </label>
                  <input
                    type="text"
                    value={newServer.name}
                    onChange={(e) => setNewServer({ ...newServer, name: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md"
                    placeholder="e.g., My Custom Server"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Server Type
                  </label>
                  <select
                    value={newServer.type}
                    onChange={(e) => setNewServer({ ...newServer, type: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  >
                    <option value="context7">Context7</option>
                    <option value="supabase">Supabase</option>
                    <option value="github">GitHub</option>
                    <option value="custom">Custom</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Priority
                  </label>
                  <input
                    type="number"
                    value={newServer.priority}
                    onChange={(e) => setNewServer({ ...newServer, priority: parseInt(e.target.value) })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md"
                    min="1"
                    max="10"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Endpoint URL
                  </label>
                  <input
                    type="url"
                    value={newServer.endpoint}
                    onChange={(e) => setNewServer({ ...newServer, endpoint: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md"
                    placeholder="https://api.example.com"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    API Key
                  </label>
                  <input
                    type="password"
                    value={newServer.apiKey}
                    onChange={(e) => setNewServer({ ...newServer, apiKey: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md"
                    placeholder="Enter API key"
                  />
                </div>
              </div>
              <div className="flex items-center mt-4">
                <input
                  type="checkbox"
                  id="enabled"
                  checked={newServer.enabled}
                  onChange={(e) => setNewServer({ ...newServer, enabled: e.target.checked })}
                  className="mr-2"
                />
                <label htmlFor="enabled" className="text-sm text-gray-700">
                  Enable server
                </label>
              </div>
              <div className="flex justify-end space-x-2 mt-4">
                <button
                  onClick={() => setShowAddForm(false)}
                  className="px-4 py-2 text-gray-600 border border-gray-300 rounded hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  onClick={handleAddServer}
                  className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
                >
                  Add Server
                </button>
              </div>
            </div>
          )}

          {/* Servers List */}
          <div className="space-y-3">
            {servers.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                No MCP servers configured
              </div>
            ) : (
              servers.map((server) => (
                <div key={server.id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-2">
                        <span className="text-lg">{getStatusIcon(server.status || 'unknown')}</span>
                        <h4 className="font-semibold">{server.name}</h4>
                        <span className="text-sm text-gray-500">({server.id})</span>
                        <span className={`text-sm ${getStatusColor(server.status || 'unknown')}`}>
                          {server.status || 'unknown'}
                        </span>
                      </div>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-600">
                        <div>
                          <span className="font-medium">Type:</span> {server.type}
                        </div>
                        <div>
                          <span className="font-medium">Priority:</span> {server.priority}
                        </div>
                        <div>
                          <span className="font-medium">Tools:</span> {server.toolCount || 0}
                        </div>
                        <div>
                          <span className="font-medium">Enabled:</span> {server.enabled ? 'Yes' : 'No'}
                        </div>
                      </div>
                      {server.endpoint && (
                        <div className="text-sm text-gray-600 mt-1">
                          <span className="font-medium">Endpoint:</span> {server.endpoint}
                        </div>
                      )}
                    </div>
                    <button
                      onClick={() => handleRemoveServer(server.id)}
                      className="text-red-600 hover:text-red-800 ml-4"
                      title="Remove server"
                    >
                      🗑️
                    </button>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
