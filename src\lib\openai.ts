import OpenAI from 'openai';

let openai: OpenAI | null = null;

const getApiKey = () => {
  const key = import.meta.env.VITE_OPENAI_API_KEY;
  if (!key) {
    throw new Error(
      'OpenAI API key not found. Please add your API key to .env file:\n' +
      'VITE_OPENAI_API_KEY=your_api_key_here'
    );
  }
  return key;
};

const initializeOpenAI = () => {
  if (!openai) {
    try {
      openai = new OpenAI({
        apiKey: getApiKey(),
        dangerouslyAllowBrowser: true // Note: In production, API calls should be made through a backend
      });
    } catch (error) {
      console.error('Failed to initialize OpenAI client:', error);
      throw error;
    }
  }
  return openai;
}

export interface Message {
  role: 'user' | 'assistant';
  content: string | MessageContent[];
}

interface MessageContent {
  type: 'text' | 'image_url' | 'latex';
  text?: string;
  image_url?: { url: string };
  latex?: string;
}

export async function getChatCompletion(messages: Message[]) {
  try {
    const client = initializeOpenAI();
    if (!client) {
      throw new Error('OpenAI client not initialized');
    }

    const completion = await openai.chat.completions.create({
      model: "gpt-4-vision-preview",
      messages: messages.map(msg => ({
        role: msg.role,
        content: Array.isArray(msg.content) ? msg.content : [{ type: 'text', text: msg.content }]
      })),
      max_tokens: 2048,
      temperature: 0.7,
      stream: true
    });

    return completion;
  } catch (error) {
    console.error('Error getting chat completion:', error);
    throw error;
  }
}

export async function analyzeImage(imageUrl: string, prompt: string) {
  try {
    const client = initializeOpenAI();
    if (!client) {
      throw new Error('OpenAI client not initialized');
    }

    const response = await openai.chat.completions.create({
      model: "gpt-4-vision-preview",
      messages: [
        {
          role: "user",
          content: [
            { type: "text", text: prompt },
            { type: "image_url", image_url: { url: imageUrl } }
          ]
        }
      ],
      max_tokens: 2048
    });

    return response.choices[0].message.content;
  } catch (error) {
    console.error('Error analyzing image:', error);
    throw error;
  }
}