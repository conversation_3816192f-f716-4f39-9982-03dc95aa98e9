# Comprehensive MCP (Model Context Protocol) Integration Framework

This document describes the comprehensive MCP integration framework that provides a production-ready, scalable system for integrating any MCP server with intelligent routing, error handling, and agent collaboration.

## Overview

The MCP framework consists of several key components that work together to provide a seamless integration experience:

1. **Universal MCP Adapter Interface** - Standardized interface for any MCP server
2. **Dynamic Server Discovery** - Automatic detection and registration of MCP servers
3. **Enhanced Agent System** - Intelligent agent management with memory and collaboration
4. **Advanced Tool Management** - Unified tool registry with conflict resolution
5. **Configuration Management** - Flexible configuration system
6. **Error Handling & Resilience** - Robust error handling with circuit breakers

## Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    Application Layer                        │
├─────────────────────────────────────────────────────────────┤
│                 MCP Integration Service                     │
├─────────────────────────────────────────────────────────────┤
│  Enhanced Agent Manager  │  MCP Tool Registry              │
├─────────────────────────────────────────────────────────────┤
│  Config Manager  │  Error Handler  │  Server Discovery    │
├─────────────────────────────────────────────────────────────┤
│     Context7     │    Supabase     │    GitHub    │ ...    │
│     Adapter      │     Adapter     │    Adapter   │        │
└─────────────────────────────────────────────────────────────┘
```

## Key Features

### 1. Universal MCP Adapter Interface

All MCP servers implement a standardized interface that provides:

- **Consistent Tool Registration** - Uniform way to register and describe tools
- **Parameter Validation** - Automatic validation of tool parameters
- **Error Handling** - Standardized error responses and recovery
- **Health Monitoring** - Built-in health checks and status reporting
- **Configuration Management** - Hot-reload configuration updates

### 2. Dynamic Server Discovery

The framework automatically discovers and manages MCP servers:

- **Environment Variable Detection** - Automatically detect servers from env vars
- **Runtime Configuration** - Add/remove servers without restart
- **Health Monitoring** - Continuous health checks with automatic failover
- **Hot-swapping** - Update server configurations on the fly

### 3. Intelligent Tool Routing

Advanced routing system that selects the best server for each tool call:

- **Priority-based Routing** - Route based on server priorities
- **Load Balancing** - Distribute load across multiple servers
- **Conflict Resolution** - Handle tools available on multiple servers
- **Context-aware Selection** - Route based on query context and agent type

### 4. Agent Memory & Collaboration

Enhanced agent system with advanced capabilities:

- **Persistent Memory** - Maintain conversation context across sessions
- **Agent Collaboration** - Agents can work together on complex tasks
- **Performance Monitoring** - Track agent performance and optimization
- **Intelligent Tool Assignment** - Automatically assign tools based on agent specialization

### 5. Error Handling & Resilience

Comprehensive error handling system:

- **Circuit Breakers** - Prevent cascading failures
- **Automatic Retry** - Intelligent retry with exponential backoff
- **Graceful Degradation** - Fallback strategies when servers are unavailable
- **User-friendly Messages** - Convert technical errors to user-friendly messages

## Getting Started

### 1. Basic Setup

```typescript
import { MCPIntegrationService } from './lib/mcp/integration-service';

// Initialize the MCP framework
const integrationService = MCPIntegrationService.getInstance();
await integrationService.initialize();

// Process a message
const result = await integrationService.processMessage("Search for authentication code", {
  agentId: 'general-agent',
  conversationId: 'conv_123'
});
```

### 2. Adding MCP Servers

#### Via Environment Variables

```bash
# Enable Context7
MCP_CONTEXT7_ENABLED=true
MCP_CONTEXT7_API_KEY=your_api_key
MCP_CONTEXT7_ENDPOINT=https://api.context7.com
MCP_CONTEXT7_PRIORITY=1

# Enable Supabase
MCP_SUPABASE_ENABLED=true
MCP_SUPABASE_API_KEY=your_supabase_key
MCP_SUPABASE_ENDPOINT=https://your-project.supabase.co
MCP_SUPABASE_PRIORITY=2
```

#### Via Runtime Configuration

```typescript
// Add a new server at runtime
await integrationService.addServer({
  id: 'github-server',
  name: 'GitHub Integration',
  type: 'github',
  enabled: true,
  priority: 3,
  apiKey: 'your_github_token',
  endpoint: 'https://api.github.com'
});
```

### 3. Creating Custom Adapters

```typescript
import { BaseMCPAdapter } from './lib/mcp/base-adapter';

export class CustomAdapter extends BaseMCPAdapter {
  constructor() {
    super('custom', 'Custom Server', '1.0.0', {
      supportsStreaming: false,
      supportsAuthentication: true,
      supportsBatching: false,
      maxConcurrentRequests: 10
    });
  }

  protected async doInitialize(): Promise<void> {
    // Initialize your adapter
  }

  protected async loadTools(): Promise<void> {
    // Define your tools
    this.tools = [
      {
        name: 'custom_tool',
        description: 'A custom tool',
        serverId: this.id,
        category: 'custom'
      }
    ];
  }

  protected async doCallTool(toolName: string, params: any): Promise<any> {
    // Implement your tool logic
    switch (toolName) {
      case 'custom_tool':
        return await this.handleCustomTool(params);
      default:
        throw new Error(`Unknown tool: ${toolName}`);
    }
  }
}
```

### 4. Registering Custom Adapters

```typescript
import { MCPServerFactory } from './lib/mcp/server-discovery';
import { CustomAdapter } from './custom-adapter';

// Register your adapter
MCPServerFactory.registerAdapter('custom', CustomAdapter);
```

## Configuration

### Server Configuration

```typescript
interface MCPServerConfig {
  id: string;                    // Unique server identifier
  name: string;                  // Human-readable name
  type: string;                  // Server type (matches adapter)
  enabled: boolean;              // Whether server is active
  priority: number;              // Routing priority (higher = preferred)
  endpoint?: string;             // Server endpoint URL
  apiKey?: string;               // Authentication key
  timeout?: number;              // Request timeout in ms
  healthCheck?: {                // Health check configuration
    enabled: boolean;
    interval: number;            // Check interval in ms
    timeout: number;             // Health check timeout
    retries: number;             // Max retries before marking unhealthy
  };
  customConfig?: Record<string, any>; // Server-specific configuration
}
```

### Routing Rules

```typescript
interface ToolRoutingRule {
  id: string;                    // Unique rule identifier
  name: string;                  // Human-readable name
  description: string;           // Rule description
  conditions: {                  // Conditions for rule activation
    toolNames?: string[];        // Specific tool names
    agentTypes?: string[];       // Agent types
    keywords?: string[];         // Keywords in query
    patterns?: RegExp[];         // Regex patterns
  };
  action: {                      // Action to take when rule matches
    strategy: ToolConflictStrategy;
    preferredServers?: string[]; // Preferred servers
    fallbackServers?: string[];  // Fallback servers
  };
  priority: number;              // Rule priority
  enabled: boolean;              // Whether rule is active
}
```

## Monitoring & Analytics

### System Status

```typescript
// Get comprehensive system status
const status = integrationService.getSystemInfo();
console.log(status);
// {
//   status: { initialized: true, serverCount: 3, toolCount: 15, ... },
//   servers: [...],
//   tools: [...],
//   agents: [...],
//   errors: { totalErrors: 5, errorsByType: {...}, ... },
//   performance: { circuitBreakers: {...}, toolConflicts: [...], ... }
// }
```

### Agent Performance

```typescript
// Get agent performance metrics
const performance = integrationService.getAgentPerformance('general-agent');
console.log(performance);
// {
//   totalQueries: 150,
//   successfulQueries: 145,
//   successRate: 0.967,
//   averageResponseTime: 1250,
//   lastActivity: 1640995200000,
//   activeConversations: 3
// }
```

### Error Analytics

```typescript
// Get error statistics
const errorStats = errorHandler.getErrorStats();
console.log(errorStats);
// {
//   totalErrors: 25,
//   errorsByType: { CONNECTION_ERROR: 10, TIMEOUT_ERROR: 8, ... },
//   errorsBySeverity: { HIGH: 5, MEDIUM: 15, LOW: 5 },
//   errorsByServer: { 'context7': 12, 'supabase': 8, ... },
//   recentErrors: [...]
// }
```

## Best Practices

### 1. Server Configuration

- Set appropriate priorities based on server reliability and performance
- Configure health checks for critical servers
- Use environment variables for sensitive configuration like API keys
- Set reasonable timeouts and rate limits

### 2. Tool Design

- Provide comprehensive parameter validation
- Include detailed examples in tool descriptions
- Use appropriate categories and tags for better routing
- Implement proper error handling

### 3. Agent Specialization

- Assign tools to agents based on their purpose
- Use routing rules to optimize tool selection
- Monitor agent performance and adjust assignments

### 4. Error Handling

- Implement graceful degradation strategies
- Provide user-friendly error messages
- Monitor circuit breaker status
- Set up alerts for critical errors

## Troubleshooting

### Common Issues

1. **Server Not Discovered**
   - Check environment variables
   - Verify server configuration
   - Check adapter registration

2. **Tool Conflicts**
   - Review routing rules
   - Adjust server priorities
   - Implement conflict resolution strategies

3. **Performance Issues**
   - Monitor server loads
   - Check circuit breaker status
   - Optimize tool assignments

4. **Authentication Errors**
   - Verify API keys
   - Check server endpoints
   - Review authentication configuration

### Debug Mode

Enable debug logging for detailed information:

```bash
DEBUG=mcp:* npm start
```

## Contributing

To add a new MCP server adapter:

1. Create a new adapter class extending `BaseMCPAdapter`
2. Implement required methods (`doInitialize`, `loadTools`, `doCallTool`, etc.)
3. Register the adapter with `MCPServerFactory`
4. Add configuration examples and documentation
5. Write tests for the adapter

## License

This MCP framework is part of the Phoenix Roofing and Repair application and follows the same licensing terms.
