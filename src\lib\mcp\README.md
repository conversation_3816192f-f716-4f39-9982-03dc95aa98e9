# MCP (Model Context Protocol) Tool Registry

This directory contains the implementation of a Centralized MCP Tool Registry for managing MCP servers and tools in a multi-agent system.

## Overview

The MCP Tool Registry provides a centralized way to manage MCP servers and tools across multiple agents. It handles:

- Tool discovery and registration
- Server startup and shutdown
- Tool execution routing
- Resource management

## Key Components

### MCPToolRegistry

The `MCPToolRegistry` class is the core of the implementation. It provides:

- A singleton instance accessible throughout the application
- Methods for initializing and shutting down MCP servers
- A registry of all available tools and their source servers
- Methods for calling tools by name, abstracting away the details of which server provides the tool

### Integration with LangGraphAgent

The `LangGraphAgent` class has been updated to use the MCP Tool Registry instead of directly connecting to MCP servers. This provides:

- Access to all tools from all MCP servers, regardless of which server is assigned to the agent
- Simplified agent configuration (no need to specify MCP server URLs)
- More efficient resource usage (servers are only started when needed)

### Integration with AgentManager

The `AgentManager` class has been updated to use the MCP Tool Registry for managing MCP servers. This provides:

- Centralized management of MCP servers
- Simplified agent initialization
- More efficient resource usage

## Benefits

The Centralized MCP Tool Registry approach provides several benefits:

1. **Decoupling**: Agents don't need to know which MCP server provides which tool.
2. **Flexibility**: You can easily move tools between MCP servers without changing agent code.
3. **Resource Efficiency**: MCP servers are only started when their tools are needed.
4. **Simplified Agent Configuration**: No need to specify MCP server IDs for each agent.
5. **Tool Discovery**: Agents can discover all available tools at runtime.
6. **Consistent Interface**: All tools are accessed through the same interface.

## Usage

### Initializing the Registry

```typescript
// Get the registry instance
const toolRegistry = MCPToolRegistry.getInstance();

// Initialize with specific servers
await toolRegistry.initialize(['context7', 'github']);

// Or initialize with all available servers
await toolRegistry.initialize();
```

### Getting Tools

```typescript
// Get all available tools
const allTools = toolRegistry.getAllTools();

// Get LangGraph-compatible tools
const langGraphTools = toolRegistry.getLangGraphTools();
```

### Calling Tools

```typescript
// Call a tool by name
const result = await toolRegistry.callTool('search_code', { query: 'function example' });
```

### Shutting Down

```typescript
// Shutdown the registry (stops all servers)
await toolRegistry.shutdown();
```

## Configuration

MCP server configurations are defined in `mcp-config.ts`. Each server has:

- A unique ID
- A command to start the server
- Arguments for the command
- Optional environment variables

## Best Practices

1. **Logical Tool Organization**: Group related tools on the same MCP server.
2. **Server Specialization**: Each MCP server should have a clear purpose and provide a specific set of tools.
3. **Resource Management**: Use the registry's lazy loading capabilities to minimize resource usage.
4. **Error Handling**: Always handle errors when calling tools, as network or server issues can occur.
5. **Tool Naming**: Use clear, descriptive names for tools to avoid confusion.

## Future Improvements

1. **Tool Categories**: Add support for categorizing tools by function.
2. **Tool Versioning**: Add support for tool versioning to handle API changes.
3. **Tool Dependencies**: Add support for tools that depend on other tools.
4. **Tool Caching**: Add support for caching tool results to improve performance.
5. **Tool Metrics**: Add support for collecting metrics on tool usage and performance.
