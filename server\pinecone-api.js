const express = require('express');
const cors = require('cors');
const { PineconeClient } = require('@pinecone-database/pinecone');
const { OpenAIEmbeddings } = require('@langchain/openai');
const { PineconeStore } = require('@langchain/pinecone');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

const app = express();
app.use(cors());
app.use(express.json());

// Initialize Pinecone client
let pineconeClient = null;
let pineconeIndex = null;

async function initializePinecone() {
  try {
    console.log('Initializing Pinecone client...');
    
    pineconeClient = new PineconeClient();
    await pineconeClient.init({
      apiKey: process.env.PINECONE_API_KEY,
      environment: process.env.PINECONE_ENVIRONMENT,
    });
    
    console.log('Pinecone client initialized successfully');
    
    // Get the index
    pineconeIndex = pineconeClient.Index(process.env.PINECONE_INDEX);
    
    console.log(`Connected to Pinecone index: ${process.env.PINECONE_INDEX}`);
    return true;
  } catch (error) {
    console.error('Error initializing Pinecone:', error);
    return false;
  }
}

// Initialize OpenAI embeddings
function getEmbeddings() {
  return new OpenAIEmbeddings({
    openAIApiKey: process.env.OPENROUTER_API_KEY,
    modelName: process.env.PINECONE_EMBEDDING_MODEL || 'text-embedding-3-large',
  });
}

// API endpoints
app.get('/api/pinecone/status', async (req, res) => {
  try {
    if (!pineconeClient) {
      const initialized = await initializePinecone();
      if (!initialized) {
        return res.status(500).json({ success: false, message: 'Failed to initialize Pinecone' });
      }
    }
    
    // Check if index exists
    const indexes = await pineconeClient.listIndexes();
    const indexExists = indexes.includes(process.env.PINECONE_INDEX);
    
    res.json({
      success: true,
      connected: true,
      indexExists,
      config: {
        environment: process.env.PINECONE_ENVIRONMENT,
        index: process.env.PINECONE_INDEX,
        host: process.env.PINECONE_HOST,
        dimensions: process.env.PINECONE_DIMENSIONS,
        metric: process.env.PINECONE_METRIC,
        embeddingModel: process.env.PINECONE_EMBEDDING_MODEL
      }
    });
  } catch (error) {
    console.error('Error checking Pinecone status:', error);
    res.status(500).json({ success: false, message: error.message });
  }
});

app.post('/api/pinecone/upsert', async (req, res) => {
  try {
    if (!pineconeClient) {
      await initializePinecone();
    }
    
    const { documents, namespace } = req.body;
    
    if (!documents || !Array.isArray(documents)) {
      return res.status(400).json({ success: false, message: 'Invalid documents format' });
    }
    
    // Convert to LangChain documents
    const docs = documents.map(doc => ({
      pageContent: doc.pageContent,
      metadata: doc.metadata || {}
    }));
    
    // Create vector store
    const embeddings = getEmbeddings();
    const vectorStore = await PineconeStore.fromExistingIndex(
      embeddings,
      {
        pineconeIndex,
        namespace: namespace || 'default',
      }
    );
    
    // Add documents to vector store
    await vectorStore.addDocuments(docs);
    
    res.json({ success: true, count: documents.length });
  } catch (error) {
    console.error('Error upserting documents to Pinecone:', error);
    res.status(500).json({ success: false, message: error.message });
  }
});

app.post('/api/pinecone/search', async (req, res) => {
  try {
    if (!pineconeClient) {
      await initializePinecone();
    }
    
    const { query, k = 5, namespace } = req.body;
    
    if (!query) {
      return res.status(400).json({ success: false, message: 'Query is required' });
    }
    
    // Create vector store
    const embeddings = getEmbeddings();
    const vectorStore = await PineconeStore.fromExistingIndex(
      embeddings,
      {
        pineconeIndex,
        namespace: namespace || 'default',
      }
    );
    
    // Search for similar documents
    const results = await vectorStore.similaritySearch(query, k);
    
    res.json({ success: true, results });
  } catch (error) {
    console.error('Error searching Pinecone:', error);
    res.status(500).json({ success: false, message: error.message });
  }
});

app.delete('/api/pinecone/namespace', async (req, res) => {
  try {
    if (!pineconeClient) {
      await initializePinecone();
    }
    
    const { namespace } = req.body;
    
    // Delete all vectors in the namespace
    await pineconeIndex.delete1({
      deleteAll: true,
      namespace: namespace || 'default'
    });
    
    res.json({ success: true });
  } catch (error) {
    console.error('Error deleting namespace in Pinecone:', error);
    res.status(500).json({ success: false, message: error.message });
  }
});

app.post('/api/pinecone/create-index', async (req, res) => {
  try {
    if (!pineconeClient) {
      await initializePinecone();
    }
    
    const { dimensions } = req.body;
    const actualDimensions = dimensions || process.env.PINECONE_DIMENSIONS || 1536;
    
    // Check if index exists
    const indexes = await pineconeClient.listIndexes();
    const indexExists = indexes.includes(process.env.PINECONE_INDEX);
    
    if (!indexExists) {
      // Create the index
      await pineconeClient.createIndex({
        name: process.env.PINECONE_INDEX,
        dimension: parseInt(actualDimensions),
        metric: process.env.PINECONE_METRIC || 'cosine'
      });
      
      res.json({ success: true, created: true });
    } else {
      res.json({ success: true, created: false, message: 'Index already exists' });
    }
  } catch (error) {
    console.error('Error creating Pinecone index:', error);
    res.status(500).json({ success: false, message: error.message });
  }
});

// Start the server
const PORT = process.env.PORT || 3001;
app.listen(PORT, async () => {
  console.log(`Server running on port ${PORT}`);
  await initializePinecone();
});
