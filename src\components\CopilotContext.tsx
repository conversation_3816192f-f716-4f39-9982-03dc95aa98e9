"use client";

import React, { ReactNode, useEffect } from "react";
import { useCopilotReadable } from "@copilotkit/react-core";
import { useMCPTools } from "../lib/copilot/useMCPTools";

interface CopilotContextProps {
  children: ReactNode;
  companyInfo?: any;
  services?: any[];
  currentUser?: any;
}

/**
 * CopilotContext component that makes application data available to the AI assistant
 */
export function CopilotContext({
  children,
  companyInfo,
  services,
  currentUser,
}: CopilotContextProps) {
  // Initialize MCP tools
  const { isInitialized } = useMCPTools({
    serverIds: ["context7", "supabase", "github", "vapi", "telnyx"],
    enabled: true,
  });

  // Make company information available to the AI assistant
  useCopilotReadable({
    description: "Company information for Phoenix Roofing and Repair",
    value: companyInfo || {
      name: "Phoenix Roofing and Repair",
      address: "532 E. Maryland Suite F, Phoenix, AZ 85012",
      phone: "(602) 837-ROOF (7663)",
      email: "<EMAIL>",
      hours: {
        monday: "8:00 AM - 5:00 PM",
        tuesday: "8:00 AM - 5:00 PM",
        wednesday: "8:00 AM - 5:00 PM",
        thursday: "8:00 AM - 5:00 PM",
        friday: "8:00 AM - 5:00 PM",
        saturday: "9:00 AM - 2:00 PM",
        sunday: "Closed",
      },
    },
  });

  // Make services available to the AI assistant
  useCopilotReadable({
    description: "Services offered by Phoenix Roofing and Repair",
    value: services || [
      {
        id: "roof-repair",
        name: "Roof Repair",
        description: "Professional roof repair services for all types of roofs.",
        priceRange: "$500 - $2,000",
      },
      {
        id: "roof-replacement",
        name: "Roof Replacement",
        description: "Complete roof replacement services with quality materials.",
        priceRange: "$8,000 - $20,000",
      },
      {
        id: "roof-inspection",
        name: "Roof Inspection",
        description: "Thorough roof inspection to identify issues and provide recommendations.",
        priceRange: "FREE Estimates",
      },
      {
        id: "emergency-repairs",
        name: "Emergency Repairs",
        description: "24/7 emergency roof repair services for urgent situations.",
        priceRange: "Varies",
      },
    ],
  });

  // Make current user information available to the AI assistant if logged in
  if (currentUser) {
    useCopilotReadable({
      description: "Current user information",
      value: currentUser,
    });
  }

  return <>{children}</>;
}
