"use client";

import React from "react";
import { CopilotChatbox, CopilotPopup } from "@copilotkit/react-ui";
import "@copilotkit/react-ui/styles.css";

interface CopilotChatProps {
  variant?: "popup" | "chatbox";
  className?: string;
}

/**
 * CopilotChat component that provides a chat interface for the AI assistant
 */
export function CopilotChat({ variant = "popup", className = "" }: CopilotChatProps) {
  // Common props for both variants
  const commonProps = {
    className,
    instructions: `You are a helpful AI assistant for Phoenix Roofing and Repair.
    
Phoenix Roofing and Repair is located at 532 E. Maryland Suite F, Phoenix, AZ 85012.
Our phone number is (602) 837-ROOF (7663).
We offer FREE estimates for all roofing services.

Help users with their roofing needs, answer questions, and assist with scheduling appointments.
Use the available tools to access information and perform actions when needed.`,
  };

  // Render the appropriate variant
  if (variant === "popup") {
    return (
      <CopilotPopup
        {...commonProps}
        labels={{
          title: "Phoenix Roofing Assistant",
          initial: "Need help with your roof?",
          placeholder: "Ask about our services...",
        }}
        position="bottom-right"
        showIntroduction={true}
      />
    );
  } else {
    return (
      <CopilotChatbox
        {...commonProps}
        labels={{
          title: "Phoenix Roofing Assistant",
          placeholder: "Ask about our services...",
        }}
      />
    );
  }
}
