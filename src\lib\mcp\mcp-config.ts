/**
 * MCP Configuration
 * 
 * This module provides configuration for MCP servers.
 * This is a placeholder implementation until the full MCP implementation is complete.
 */

/**
 * MCP server configuration
 */
export const mcpServers: Record<string, any> = {
  'context7': {
    name: 'Context7',
    description: 'Context7 MCP server for code context and documentation',
    command: 'node',
    args: ['server/context7-server.js']
  },
  'supabase': {
    name: 'Supabase',
    description: 'Supabase MCP server for database access',
    command: 'node',
    args: ['server/supabase-server.js']
  },
  'github': {
    name: 'GitHub',
    description: 'GitHub MCP server for repository access',
    command: 'node',
    args: ['server/github-server.js']
  },
  'vapi': {
    name: 'VAPI',
    description: 'VAPI MCP server for voice and audio processing',
    command: 'node',
    args: ['server/vapi-server.js']
  },
  'telnyx': {
    name: 'Telnyx',
    description: 'Telnyx MCP server for telephony and messaging',
    command: 'node',
    args: ['server/telnyx-server.js']
  }
};

/**
 * MCP server ports
 */
export const mcpServerPorts: Record<string, number> = {
  'context7': 3101,
  'supabase': 3102,
  'github': 3103,
  'vapi': 3104,
  'telnyx': 3105
};

/**
 * Get available MCP servers
 */
export function getAvailableMCPServers(): string[] {
  return Object.keys(mcpServers);
}

/**
 * Get MCP server URL
 */
export function getMCPServerUrl(serverId: string): string {
  const port = mcpServerPorts[serverId] || 3100;
  return `http://localhost:${port}`;
}
