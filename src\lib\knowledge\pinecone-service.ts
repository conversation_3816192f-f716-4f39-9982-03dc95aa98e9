/**
 * Pinecone Service
 *
 * This module provides functionality to interact with Pinecone for vector storage and retrieval.
 * It uses the Pinecone HTTP API directly.
 */

// Define a simple Document interface to avoid dependencies
interface Document {
  pageContent: string;
  metadata: Record<string, any>;
}

// OpenAI API for embeddings
const OPENAI_API_URL = "https://api.openai.com/v1/embeddings";

/**
 * Document with metadata for storage
 */
export interface DocumentWithMetadata {
  pageContent: string;
  metadata: {
    source: string;
    title?: string;
    url?: string;
    timestamp?: string;
    type?: string;
  };
}

/**
 * Options for Pinecone service
 */
export interface PineconeServiceOptions {
  apiKey: string;
  environment: string;
  indexName: string;
  namespace?: string;
  openAIApiKey?: string;
  host?: string;
  metric?: string;
  dimensions?: number;
  embeddingModel?: string;
}

/**
 * Pinecone Service class
 */
export class PineconeService {
  private options: PineconeServiceOptions;
  private apiBaseUrl: string = 'http://localhost:3001/api/pinecone';
  private connected: boolean = false;

  /**
   * Create a new PineconeService
   */
  constructor(options: PineconeServiceOptions) {
    this.options = options;

    // Apply defaults from environment variables if not provided
    if (!options.host && import.meta.env.VITE_PINECONE_HOST) {
      this.options.host = import.meta.env.VITE_PINECONE_HOST;
    }

    if (!options.metric && import.meta.env.VITE_PINECONE_METRIC) {
      this.options.metric = import.meta.env.VITE_PINECONE_METRIC;
    }

    if (!options.dimensions && import.meta.env.VITE_PINECONE_DIMENSIONS) {
      this.options.dimensions = Number(import.meta.env.VITE_PINECONE_DIMENSIONS);
    }

    if (!options.embeddingModel && import.meta.env.VITE_EMBEDDING_MODEL) {
      this.options.embeddingModel = import.meta.env.VITE_EMBEDDING_MODEL;
    }

    console.log('Initialized Pinecone service with options:', {
      ...this.options,
      apiKey: this.options.apiKey ? '***' : undefined, // Mask API key for security
      openAIApiKey: this.options.openAIApiKey ? '***' : undefined // Mask API key for security
    });

    // Check connection status
    this.checkConnection();
  }

  /**
   * Check connection to Pinecone
   */
  private async checkConnection(): Promise<boolean> {
    try {
      // Check if we have the required settings
      const hasRequiredSettings =
        !!this.options.apiKey &&
        !!this.options.environment &&
        !!this.options.indexName;

      if (!hasRequiredSettings) {
        this.connected = false;
        console.error('Failed to connect to Pinecone: Missing required settings');
        return false;
      }

      // Try to describe the index to check connection
      const host = this.options.host || `https://${this.options.indexName}-${this.options.environment}.svc.pinecone.io`;
      console.log(`Checking connection to Pinecone at ${host}`);

      try {
        const response = await fetch(`${host}/describe_index_stats`, {
          method: 'GET',
          headers: {
            'Api-Key': this.options.apiKey,
            'Content-Type': 'application/json',
          }
        });

        if (response.ok) {
          const data = await response.json();
          console.log('Pinecone connection successful. Index stats:', data);
          this.connected = true;
          return true;
        } else {
          const errorText = await response.text();
          console.error(`Failed to connect to Pinecone: ${response.status} ${response.statusText}`, errorText);

          // If the error is 404, the index might not exist yet
          if (response.status === 404) {
            console.log('Index not found. It may need to be created.');
            // Check if the index exists using the management API
            const exists = await this.indexExists();
            if (!exists) {
              console.log('Index does not exist. It needs to be created.');
            }
          }

          this.connected = false;
          return false;
        }
      } catch (error) {
        console.error('Error connecting to Pinecone:', error);
        this.connected = false;
        return false;
      }
    } catch (error) {
      console.error('Error checking Pinecone connection:', error);
      this.connected = false;
      return false;
    }
  }

  /**
   * Generate embeddings (mock implementation due to CORS limitations)
   *
   * In a production environment, you would:
   * 1. Set up a proxy server to handle API calls
   * 2. Use a serverless function
   * 3. Contact OpenRouter to add your domain to their CORS allowlist
   */
  private async generateEmbeddings(texts: string[]): Promise<number[][]> {
    if (!texts.length) return [];

    try {
      // Generate mock embeddings with the correct dimensions
      const dimensions = this.options.dimensions || 3072; // Use the dimensions from options
      console.log(`Generating mock embeddings for ${texts.length} texts with ${dimensions} dimensions`);

      // Create deterministic embeddings based on text content
      // This ensures similar texts get similar embeddings
      const mockEmbeddings: number[][] = [];

      for (let i = 0; i < texts.length; i++) {
        const text = texts[i];
        const embedding = new Array(dimensions).fill(0);

        // Generate a deterministic embedding based on the text content
        // This is a simple hash function that converts text to numbers
        for (let j = 0; j < text.length; j++) {
          const charCode = text.charCodeAt(j);
          const position = j % dimensions;
          embedding[position] += charCode / 1000;
        }

        // Normalize the embedding to unit length
        const magnitude = Math.sqrt(embedding.reduce((sum, val) => sum + val * val, 0));
        const normalizedEmbedding = embedding.map(val => val / (magnitude || 1));

        mockEmbeddings.push(normalizedEmbedding);
      }

      console.log(`Successfully generated ${mockEmbeddings.length} mock embeddings`);
      return mockEmbeddings;
    } catch (error) {
      console.error('Error generating embeddings:', error);
      throw error;
    }
  }

  /**
   * Create documents from text content
   */
  createDocuments(
    texts: string[],
    metadatas: Array<Record<string, any>>
  ): Document[] {
    return texts.map((text, i) => {
      return {
        pageContent: text,
        metadata: metadatas[i] || {},
      };
    });
  }

  /**
   * Split text into chunks
   */
  splitTextIntoChunks(text: string, chunkSize: number = 1000, overlap: number = 200): string[] {
    const chunks: string[] = [];

    // Simple text chunking by character count
    for (let i = 0; i < text.length; i += chunkSize - overlap) {
      const chunk = text.slice(i, i + chunkSize);
      chunks.push(chunk);
    }

    return chunks;
  }

  /**
   * Upsert documents to Pinecone
   */
  async upsertDocuments(documents: DocumentWithMetadata[]): Promise<void> {
    try {
      if (!await this.checkConnection()) {
        throw new Error('Not connected to Pinecone');
      }

      console.log(`Upserting ${documents.length} documents to Pinecone`);

      // Process documents in batches to avoid rate limits
      const batchSize = 10;
      const batches = [];

      for (let i = 0; i < documents.length; i += batchSize) {
        batches.push(documents.slice(i, i + batchSize));
      }

      const host = this.options.host || `https://${this.options.indexName}-${this.options.environment}.svc.pinecone.io`;
      const namespace = this.options.namespace || 'default';

      let successCount = 0;

      for (let i = 0; i < batches.length; i++) {
        const batch = batches[i];
        console.log(`Processing batch ${i + 1}/${batches.length} (${batch.length} documents)`);

        // Extract text for embeddings
        const texts = batch.map(doc => doc.pageContent);

        // Generate embeddings
        const embeddings = await this.generateEmbeddings(texts);

        // Prepare vectors for Pinecone
        const vectors = embeddings.map((embedding, idx) => ({
          id: `doc-${Date.now()}-${i}-${idx}`,
          values: embedding,
          metadata: {
            ...batch[idx].metadata,
            text: batch[idx].pageContent.slice(0, 1000) // Limit text size in metadata
          }
        }));

        // Upsert vectors to Pinecone
        const response = await fetch(`${host}/vectors/upsert`, {
          method: 'POST',
          headers: {
            'Api-Key': this.options.apiKey,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            vectors,
            namespace
          }),
        });

        if (!response.ok) {
          const error = await response.json();
          throw new Error(`Pinecone API error: ${JSON.stringify(error)}`);
        }

        const result = await response.json();
        successCount += result.upsertedCount || batch.length;

        // Add a small delay between batches to avoid rate limits
        if (i < batches.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 500));
        }
      }

      console.log(`Successfully upserted ${successCount} documents to Pinecone`);

      // Log some sample data for demonstration
      if (documents.length > 0) {
        console.log('Sample document:', {
          content: documents[0].pageContent.substring(0, 100) + '...',
          metadata: documents[0].metadata
        });
      }
    } catch (error) {
      console.error('Error upserting documents to Pinecone:', error);
      throw error;
    }
  }

  /**
   * Search for similar documents in Pinecone
   */
  async similaritySearch(query: string, k: number = 5): Promise<Document[]> {
    try {
      if (!await this.checkConnection()) {
        throw new Error('Not connected to Pinecone');
      }

      console.log(`Searching Pinecone for: "${query}" with k=${k}`);

      try {
        // Generate embedding for the query
        const [queryEmbedding] = await this.generateEmbeddings([query]);

        // Prepare the query request
        const host = this.options.host || `https://${this.options.indexName}-${this.options.environment}.svc.pinecone.io`;
        const namespace = this.options.namespace || 'default';

        // Query Pinecone
        const response = await fetch(`${host}/query`, {
          method: 'POST',
          headers: {
            'Api-Key': this.options.apiKey,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            vector: queryEmbedding,
            topK: k,
            namespace,
            includeMetadata: true
          }),
        });

        if (!response.ok) {
          console.error('Error querying Pinecone:', await response.text());
          // If the query fails, return fallback results
          return this.generateFallbackResults(query, k);
        }

        const result = await response.json();
        console.log('Pinecone query result:', result);

        // Check if we have matches
        if (!result.matches || result.matches.length === 0) {
          console.log('No matches found in Pinecone');
          return this.generateFallbackResults(query, k);
        }

        // Convert Pinecone matches to documents
        const documents = result.matches.map((match: any) => ({
          pageContent: match.metadata?.text || 'No content available',
          metadata: {
            ...(match.metadata || {}),
            score: match.score
          }
        }));

        return documents;
      } catch (error) {
        console.error('Error in Pinecone query:', error);
        // If there's an error, return fallback results
        return this.generateFallbackResults(query, k);
      }
    } catch (error) {
      console.error('Error searching Pinecone:', error);

      // Return fallback results if there's an error
      return this.generateFallbackResults(query, k);
    }
  }

  /**
   * Generate fallback search results when the API is unavailable
   */
  private generateFallbackResults(query: string, k: number): Document[] {
    const results: Document[] = [];

    // Generate some fallback results based on the query
    for (let i = 0; i < k; i++) {
      results.push({
        pageContent: `[FALLBACK RESULT] Unable to connect to Pinecone. This is a fallback result for query: "${query}". Please check your Pinecone connection. This is result #${i + 1}.`,
        metadata: {
          source: 'fallback',
          title: `Connection Error - Result ${i + 1}`,
          url: 'N/A',
          timestamp: new Date().toISOString()
        }
      });
    }

    return results;
  }

  /**
   * Delete all documents in a namespace
   */
  async deleteNamespace(): Promise<void> {
    try {
      if (!await this.checkConnection()) {
        throw new Error('Not connected to Pinecone');
      }

      const namespace = this.options.namespace || 'default';
      console.log(`Deleting all documents in namespace: ${namespace}`);

      const host = this.options.host || `https://${this.options.indexName}-${this.options.environment}.svc.pinecone.io`;

      // Delete all vectors in the namespace
      const response = await fetch(`${host}/vectors/delete`, {
        method: 'POST',
        headers: {
          'Api-Key': this.options.apiKey,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          deleteAll: true,
          namespace
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(`Pinecone API error: ${JSON.stringify(error)}`);
      }

      console.log(`Successfully deleted all vectors in namespace: ${namespace}`);
    } catch (error) {
      console.error('Error deleting namespace in Pinecone:', error);
      throw error;
    }
  }

  /**
   * Check if the Pinecone index exists
   */
  async indexExists(): Promise<boolean> {
    try {
      if (!this.options.apiKey || !this.options.environment) {
        return false;
      }

      // List indexes to check if our index exists
      try {
        const response = await fetch('https://api.pinecone.io/indexes', {
          method: 'GET',
          headers: {
            'Api-Key': this.options.apiKey
          }
        });

        if (!response.ok) {
          console.error('Error listing Pinecone indexes:', await response.text());
          return false;
        }

        const data = await response.json();
        console.log('Pinecone indexes response:', data);

        // Check if data is an array or an object with an indexes property
        if (Array.isArray(data)) {
          return data.some((index: any) => index.name === this.options.indexName);
        } else if (data && typeof data === 'object' && Array.isArray(data.indexes)) {
          return data.indexes.some((index: any) => index.name === this.options.indexName);
        } else {
          // If we can't determine the format, check if the index exists directly
          try {
            const indexResponse = await fetch(`https://api.pinecone.io/indexes/${this.options.indexName}`, {
              method: 'GET',
              headers: {
                'Api-Key': this.options.apiKey
              }
            });

            return indexResponse.ok;
          } catch (error) {
            console.error('Error checking specific index:', error);
            return false;
          }
        }
      } catch (error) {
        console.error('Error checking if index exists:', error);
        return false;
      }
    } catch (error) {
      console.error('Error checking if index exists:', error);
      return false;
    }
  }

  /**
   * Create the Pinecone index if it doesn't exist
   * Note: This requires admin permissions and may not work in all environments
   */
  async createIndexIfNotExists(dimensions?: number): Promise<void> {
    try {
      if (!this.options.apiKey || !this.options.environment) {
        throw new Error('Pinecone API key and environment are required');
      }

      const exists = await this.indexExists();

      if (!exists) {
        console.log(`Creating Pinecone index: ${this.options.indexName}`);

        // Use provided dimensions, or options dimensions, or default to 1536
        const actualDimensions = dimensions || this.options.dimensions || 1536;
        const metric = this.options.metric || 'cosine';

        try {
          const response = await fetch('https://api.pinecone.io/indexes', {
            method: 'POST',
            headers: {
              'Api-Key': this.options.apiKey,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              name: this.options.indexName,
              dimension: actualDimensions,
              metric: metric,
              spec: {
                serverless: {
                  cloud: 'aws',
                  region: this.options.environment
                }
              }
            })
          });

          if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`Failed to create index: ${errorText}`);
          }

          console.log(`Successfully created Pinecone index: ${this.options.indexName}`);
          console.log(`Using dimensions: ${actualDimensions}`);
          console.log(`Using metric: ${metric}`);

          // Wait for the index to be ready (this can take a few minutes)
          console.log('Waiting for index to be ready...');
          let isReady = false;
          let attempts = 0;

          while (!isReady && attempts < 10) {
            await new Promise(resolve => setTimeout(resolve, 5000));
            attempts++;

            try {
              const statusResponse = await fetch(`https://api.pinecone.io/indexes/${this.options.indexName}`, {
                method: 'GET',
                headers: {
                  'Api-Key': this.options.apiKey
                }
              });

              if (statusResponse.ok) {
                const statusData = await statusResponse.json();
                isReady = statusData.status?.ready === true;

                if (isReady) {
                  console.log('Index is ready');
                } else {
                  console.log(`Waiting for index to be ready (attempt ${attempts}/10)...`);
                }
              }
            } catch (error) {
              console.error('Error checking index status:', error);
            }
          }
        } catch (error) {
          console.error('Error creating Pinecone index:', error);
          throw error;
        }
      } else {
        console.log(`Pinecone index ${this.options.indexName} already exists`);
      }

      // Update connection status after creating the index
      await this.checkConnection();
    } catch (error) {
      console.error('Error creating Pinecone index:', error);
      throw error;
    }
  }

  /**
   * Get connection status
   */
  async getConnectionStatus(): Promise<{
    connected: boolean;
    indexExists: boolean;
    config: Record<string, any>;
  }> {
    try {
      // Check connection
      const connected = await this.checkConnection();

      // Check if index exists
      const indexExists = await this.indexExists();

      // Get index stats if connected
      let stats = {};

      if (connected) {
        try {
          const host = this.options.host || `https://${this.options.indexName}-${this.options.environment}.svc.pinecone.io`;

          const response = await fetch(`${host}/describe_index_stats`, {
            method: 'GET',
            headers: {
              'Api-Key': this.options.apiKey,
              'Content-Type': 'application/json',
            }
          });

          if (response.ok) {
            stats = await response.json();
          }
        } catch (error) {
          console.error('Error getting index stats:', error);
        }
      }

      return {
        connected,
        indexExists,
        config: {
          environment: this.options.environment,
          index: this.options.indexName,
          host: this.options.host,
          dimensions: this.options.dimensions,
          metric: this.options.metric,
          embeddingModel: this.options.embeddingModel,
          stats
        }
      };
    } catch (error) {
      console.error('Error getting connection status:', error);
      return {
        connected: false,
        indexExists: false,
        config: {}
      };
    }
  }
}
