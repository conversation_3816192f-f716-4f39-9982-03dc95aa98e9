"use client";

import React, { useEffect, useState } from "react";
import { ToolMonitor } from "../lib/mcp/tool-monitor";

interface CopilotToolMonitorProps {
  maxEvents?: number;
  refreshInterval?: number;
}

/**
 * CopilotToolMonitor component that displays tool usage statistics and events
 */
export function CopilotToolMonitor({
  maxEvents = 20,
  refreshInterval = 5000,
}: CopilotToolMonitorProps) {
  const [events, setEvents] = useState<any[]>([]);
  const [stats, setStats] = useState<any[]>([]);
  const [refreshKey, setRefreshKey] = useState<number>(0);

  // Get tool monitor instance
  const toolMonitor = ToolMonitor.getInstance();

  // Refresh data
  useEffect(() => {
    const fetchData = () => {
      // Get events
      const allEvents = toolMonitor.getEvents();
      setEvents(allEvents.slice(-maxEvents).reverse());

      // Get stats
      const allStats = toolMonitor.getStats();
      const statsArray = Array.from(allStats.entries()).map(([key, value]) => {
        const [agentId, toolName] = key.split(":");
        return {
          agentId,
          toolName,
          ...value,
        };
      });
      setStats(statsArray);
    };

    // Initial fetch
    fetchData();

    // Set up interval for refreshing data
    const intervalId = setInterval(() => {
      fetchData();
    }, refreshInterval);

    // Clean up interval on unmount
    return () => {
      clearInterval(intervalId);
    };
  }, [toolMonitor, maxEvents, refreshInterval, refreshKey]);

  // Add listener for tool usage events
  useEffect(() => {
    const handleToolUsage = () => {
      // Trigger refresh
      setRefreshKey((prev) => prev + 1);
    };

    // Add listener
    toolMonitor.addListener(handleToolUsage);

    // Remove listener on cleanup
    return () => {
      toolMonitor.removeListener(handleToolUsage);
    };
  }, [toolMonitor]);

  // Clear all events and stats
  const handleClear = () => {
    toolMonitor.clear();
    setRefreshKey((prev) => prev + 1);
  };

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold">Tool Usage Dashboard</h2>
        <button
          onClick={handleClear}
          className="px-4 py-2 bg-gray-200 rounded hover:bg-gray-300"
        >
          Clear All
        </button>
      </div>

      <div className="mb-8">
        <h3 className="text-lg font-medium mb-4">Tool Usage Statistics</h3>
        {stats.length === 0 ? (
          <p className="text-gray-500">No tool usage statistics available.</p>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Agent
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Tool
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Total Calls
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Success
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Failure
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Success Rate
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Avg Duration
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {stats.map((stat, index) => {
                  const successRate =
                    stat.totalCalls > 0
                      ? (stat.successfulCalls / stat.totalCalls) * 100
                      : 0;

                  return (
                    <tr key={index}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {stat.agentId}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {stat.toolName}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {stat.totalCalls}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {stat.successfulCalls}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {stat.failedCalls}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {successRate.toFixed(1)}%
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {stat.averageDuration.toFixed(2)}ms
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        )}
      </div>

      <div>
        <h3 className="text-lg font-medium mb-4">Recent Tool Usage Events</h3>
        {events.length === 0 ? (
          <p className="text-gray-500">No tool usage events available.</p>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Time
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Agent
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Tool
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Server
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Duration
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Details
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {events.map((event, index) => (
                  <tr
                    key={index}
                    className={event.success ? "bg-green-50" : "bg-red-50"}
                  >
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {new Date(event.timestamp).toLocaleTimeString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {event.agentId}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {event.toolName}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {event.serverId}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {event.success ? "Success" : "Failure"}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {event.duration.toFixed(2)}ms
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <button
                        onClick={() => {
                          console.log("Tool Usage Event Details:", event);
                          alert("Details logged to console");
                        }}
                        className="px-2 py-1 bg-gray-200 rounded text-xs hover:bg-gray-300"
                      >
                        View
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
}
