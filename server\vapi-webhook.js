/**
 * VAPI Webhook Handler
 * 
 * This module provides an API for handling webhooks from VAPI.
 */

const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');

const app = express();
app.use(cors());
app.use(bodyParser.json());

// Store call data
const callData = new Map();

// API endpoint to handle call started webhook
app.post('/vapi/calls/started', (req, res) => {
  const { call_id, from, to } = req.body;
  
  console.log(`Call started: ${call_id} from ${from} to ${to}`);
  
  // Store call data
  callData.set(call_id, {
    id: call_id,
    from,
    to,
    status: 'started',
    startTime: new Date(),
    events: [{
      type: 'started',
      timestamp: new Date(),
      data: req.body
    }]
  });
  
  res.json({ success: true });
});

// API endpoint to handle call ended webhook
app.post('/vapi/calls/ended', (req, res) => {
  const { call_id, duration, end_reason } = req.body;
  
  console.log(`Call ended: ${call_id}, duration: ${duration}, reason: ${end_reason}`);
  
  // Update call data
  if (callData.has(call_id)) {
    const call = callData.get(call_id);
    call.status = 'ended';
    call.endTime = new Date();
    call.duration = duration;
    call.endReason = end_reason;
    call.events.push({
      type: 'ended',
      timestamp: new Date(),
      data: req.body
    });
    
    // In a real implementation, you would notify the voice agent service
    // For now, we'll just log that the call ended
    console.log(`Call ${call_id} ended. Notifying voice agent service.`);
  }
  
  res.json({ success: true });
});

// API endpoint to handle transcript webhook
app.post('/vapi/calls/transcript', (req, res) => {
  const { call_id, transcript } = req.body;
  
  console.log(`Transcript received for call ${call_id}`);
  
  // Update call data
  if (callData.has(call_id)) {
    const call = callData.get(call_id);
    call.transcript = transcript;
    call.events.push({
      type: 'transcript',
      timestamp: new Date(),
      data: req.body
    });
  }
  
  res.json({ success: true });
});

// API endpoint to get call data
app.get('/vapi/calls/:callId', (req, res) => {
  const { callId } = req.params;
  
  if (callData.has(callId)) {
    res.json(callData.get(callId));
  } else {
    res.status(404).json({ error: 'Call not found' });
  }
});

// API endpoint to get all calls
app.get('/vapi/calls', (req, res) => {
  const calls = Array.from(callData.values());
  res.json(calls);
});

// Start the server
const PORT = process.env.VAPI_WEBHOOK_PORT || 3001;
app.listen(PORT, () => {
  console.log(`VAPI Webhook Handler running on port ${PORT}`);
});
