/**
 * Swarm Orchestrator
 *
 * This module provides orchestration for multiple agents working together in a swarm.
 * It handles agent selection, handoffs, and collaboration without user intervention.
 */

import { AgentSettings } from '../../types';
import { LangGraphAgent } from './langgraph-agent';
import { RAGAgent } from './rag-agent';
import { PineconeService } from '../knowledge/pinecone-service';

/**
 * Task interface for swarm processing
 */
export interface SwarmTask {
  id: string;
  query: string;
  context?: Record<string, any>;
  history: SwarmTaskStep[];
  status: 'pending' | 'in_progress' | 'completed' | 'failed';
}

/**
 * Task step interface for tracking agent interactions
 */
export interface SwarmTaskStep {
  agentId: string;
  input: string;
  output: string;
  timestamp: Date;
  ragContext?: string;
}

/**
 * Swarm Orchestrator class
 */
export class SwarmOrchestrator {
  private agents: Map<string, LangGraphAgent | RAGAgent> = new Map();
  private agentConfigs: AgentSettings[] = [];
  private tasks: Map<string, SwarmTask> = new Map();
  private allowCollaboration: boolean = true;
  private showReasoning: boolean = true;
  private ragAgent: RAGAgent | null = null;
  private pineconeService: PineconeService | null = null;

  /**
   * Initialize the swarm orchestrator with agent configurations
   */
  constructor(
    agentConfigs: AgentSettings[],
    options?: {
      allowCollaboration?: boolean;
      showReasoning?: boolean;
      pineconeApiKey?: string;
      pineconeEnvironment?: string;
      pineconeIndex?: string;
      pineconeHost?: string;
      pineconeMetric?: string;
      pineconeDimensions?: number;
      embeddingModel?: string;
    }
  ) {
    this.agentConfigs = agentConfigs.filter(config => config.enabled);

    if (options) {
      this.allowCollaboration = options.allowCollaboration ?? true;
      this.showReasoning = options.showReasoning ?? true;

      // Initialize Pinecone service if credentials are provided
      if (options.pineconeApiKey && options.pineconeEnvironment && options.pineconeIndex) {
        this.pineconeService = new PineconeService({
          apiKey: options.pineconeApiKey,
          environment: options.pineconeEnvironment,
          indexName: options.pineconeIndex,
          host: options.pineconeHost,
          metric: options.pineconeMetric,
          dimensions: options.pineconeDimensions,
          embeddingModel: options.embeddingModel
        });
      }
    }
  }

  /**
   * Initialize all agents in the swarm
   */
  async initialize(): Promise<void> {
    // Initialize each agent
    for (const config of this.agentConfigs) {
      // Check if this is the RAG agent
      if (config.id === 'rag-agent' && this.pineconeService) {
        // Create and initialize RAG agent
        this.ragAgent = new RAGAgent(config, this.pineconeService);
        await this.ragAgent.initialize();
        this.agents.set(config.id, this.ragAgent);
      } else {
        // Create and initialize regular agent
        const agent = new LangGraphAgent(config);
        await agent.initialize();
        this.agents.set(config.id, agent);
      }
    }

    console.log(`Initialized ${this.agents.size} agents in the swarm`);
  }

  /**
   * Create a new task in the swarm
   */
  createTask(query: string, context?: Record<string, any>): string {
    const taskId = Date.now().toString();

    const task: SwarmTask = {
      id: taskId,
      query,
      context,
      history: [],
      status: 'pending'
    };

    this.tasks.set(taskId, task);
    return taskId;
  }

  /**
   * Process a task through the swarm
   */
  async processTask(taskId: string): Promise<string> {
    const task = this.tasks.get(taskId);
    if (!task) {
      throw new Error(`Task not found: ${taskId}`);
    }

    // Mark task as in progress
    task.status = 'in_progress';

    try {
      // Select the initial agent
      const initialAgentId = this.selectInitialAgent(task.query);

      // Process the task with the initial agent
      let currentAgentId = initialAgentId;
      let currentInput = task.query;
      let finalOutput = '';

      // Maximum number of handoffs to prevent infinite loops
      const maxHandoffs = 5;
      let handoffCount = 0;

      while (handoffCount < maxHandoffs) {
        const agent = this.agents.get(currentAgentId);
        if (!agent) {
          throw new Error(`Agent not found: ${currentAgentId}`);
        }

        // Check if we need to consult the RAG agent for additional context
        let enhancedInput = currentInput;
        let ragContext = '';

        if (this.ragAgent && currentAgentId !== 'rag-agent' && this.needsCompanyInfo(currentInput)) {
          try {
            // Get relevant company information from RAG agent
            ragContext = await this.ragAgent.processMessage(
              `I need information to help with this query: ${currentInput}`
            );

            // Enhance the input with RAG context
            enhancedInput = `${currentInput}\n\nHere is some relevant information about the company that might help:\n${ragContext}`;

            console.log('Enhanced input with RAG context');
          } catch (ragError) {
            console.error('Error consulting RAG agent:', ragError);
            // Continue with original input if RAG fails
          }
        }

        // Process the input with the current agent
        const output = await agent.processMessage(enhancedInput);

        // Record this step
        const step: SwarmTaskStep = {
          agentId: currentAgentId,
          input: currentInput,
          output,
          timestamp: new Date(),
          ragContext: ragContext || undefined
        };

        task.history.push(step);

        // Check for handoff
        const handoffInfo = this.detectHandoff(output);

        if (handoffInfo && this.allowCollaboration) {
          // Handoff to another agent
          const { agentId, reason } = handoffInfo;

          if (this.agents.has(agentId) && agentId !== currentAgentId) {
            console.log(`Handing off from ${currentAgentId} to ${agentId}: ${reason}`);

            // Prepare input for the next agent
            currentInput = this.prepareHandoffInput(task, currentAgentId, agentId, reason);
            currentAgentId = agentId;
            handoffCount++;
          } else {
            // No valid handoff, use this as the final output
            finalOutput = this.formatFinalOutput(task);
            break;
          }
        } else {
          // No handoff, use this as the final output
          finalOutput = this.formatFinalOutput(task);
          break;
        }
      }

      // Mark task as completed
      task.status = 'completed';
      return finalOutput;

    } catch (error) {
      // Mark task as failed
      task.status = 'failed';
      console.error('Error processing task:', error);
      throw error;
    }
  }

  /**
   * Select the initial agent based on the query
   */
  private selectInitialAgent(query: string): string {
    // In a real implementation, this would use a router agent or ML model
    // to select the most appropriate agent for the query

    // For now, use a simple keyword-based approach
    const queryLower = query.toLowerCase();

    // Check for ad creative related queries
    if (queryLower.includes('ad') ||
        queryLower.includes('advertisement') ||
        queryLower.includes('marketing') ||
        queryLower.includes('campaign') ||
        queryLower.includes('creative') ||
        queryLower.includes('slogan') ||
        queryLower.includes('tagline') ||
        queryLower.includes('promotion')) {
      const adCreativeAgent = this.agentConfigs.find(a => a.id === 'ad-creative-agent');
      if (adCreativeAgent && adCreativeAgent.enabled) {
        return 'ad-creative-agent';
      }
    }

    if (queryLower.includes('code') || queryLower.includes('programming') || queryLower.includes('function')) {
      const codeAgent = this.agentConfigs.find(a => a.id === 'code-agent');
      if (codeAgent && codeAgent.enabled) {
        return 'code-agent';
      }
    }

    if (queryLower.includes('research') || queryLower.includes('find') || queryLower.includes('search')) {
      const researchAgent = this.agentConfigs.find(a => a.id === 'research-agent');
      if (researchAgent && researchAgent.enabled) {
        return 'research-agent';
      }
    }

    // Default to the first enabled agent (usually RAG)
    return this.agentConfigs[0]?.id || 'rag-agent';
  }

  /**
   * Detect if the response indicates a handoff to another agent
   */
  private detectHandoff(response: string): { agentId: string; reason: string } | null {
    // Simple handoff detection based on text patterns
    // In a real implementation, this could be more sophisticated

    // Look for explicit handoff markers
    const handoffRegex = /\[handoff:([a-zA-Z0-9_-]+)\]\s*(?:\[reason:(.*?)\])?/i;
    const match = response.match(handoffRegex);

    if (match && match[1]) {
      return {
        agentId: match[1],
        reason: match[2] || 'No reason provided'
      };
    }

    // Look for implicit handoff signals
    if (response.includes('I need to consult the code agent') ||
        response.includes('This requires programming expertise')) {
      return {
        agentId: 'code-agent',
        reason: 'Query requires code expertise'
      };
    }

    if (response.includes('I need to research this') ||
        response.includes('Let me search for more information')) {
      return {
        agentId: 'research-agent',
        reason: 'Query requires research'
      };
    }

    if (response.includes('This requires marketing expertise') ||
        response.includes('Let me create an ad for you') ||
        response.includes('This is a job for the ad creative team') ||
        response.includes('I should hand this to our marketing specialist')) {
      return {
        agentId: 'ad-creative-agent',
        reason: 'Query requires marketing and advertising expertise'
      };
    }

    return null;
  }

  /**
   * Prepare input for the next agent during a handoff
   */
  private prepareHandoffInput(
    task: SwarmTask,
    fromAgentId: string,
    toAgentId: string,
    reason: string
  ): string {
    // Get the original query
    const originalQuery = task.query;

    // Get the last agent's response
    const lastStep = task.history[task.history.length - 1];
    const lastResponse = lastStep.output;

    // Create a handoff message
    return `[HANDOFF FROM ${fromAgentId} TO ${toAgentId}]
Reason: ${reason}

Original query: ${originalQuery}

Previous agent's response:
${lastResponse}

Please continue processing this query based on your expertise.`;
  }

  /**
   * Format the final output based on task history
   */
  private formatFinalOutput(task: SwarmTask): string {
    if (task.history.length === 0) {
      return "No agents processed this task.";
    }

    // Get the final response
    const finalStep = task.history[task.history.length - 1];
    let finalOutput = finalStep.output;

    // If showing reasoning is enabled and there were handoffs, include that information
    if (this.showReasoning && task.history.length > 1) {
      const agentPath = task.history.map(step => step.agentId).join(' → ');

      finalOutput = `${finalOutput}\n\n${this.showReasoning ? '---\n' : ''}${this.showReasoning ? `This response was collaboratively generated by multiple agents: ${agentPath}` : ''}`;
    }

    return finalOutput;
  }

  /**
   * Get all tasks
   */
  getTasks(): SwarmTask[] {
    return Array.from(this.tasks.values());
  }

  /**
   * Get a specific task
   */
  getTask(taskId: string): SwarmTask | undefined {
    return this.tasks.get(taskId);
  }

  /**
   * Determine if a query needs company information
   */
  private needsCompanyInfo(query: string): boolean {
    const companyInfoIndicators = [
      'company',
      'business',
      'organization',
      'contact',
      'address',
      'phone',
      'email',
      'website',
      'product',
      'service',
      'customer',
      'client',
      'target audience',
      'mission',
      'vision',
      'value',
      'about us',
      'location',
      'industry',
      'market',
      'competitor',
      'team',
      'employee',
      'staff',
      'founder',
      'ceo',
      'history',
      'background',
      'established',
      'founded',
      'create ad',
      'advertisement',
      'marketing',
      'campaign',
      'promotion',
      'brand',
      'logo',
      'slogan',
      'tagline',
      'ad ideas',
      'ad copy',
      'billboard',
      'social media ad',
      'facebook ad',
      'instagram ad',
      'google ad',
      'digital marketing',
      'print ad',
      'flyer',
      'brochure',
      'marketing material',
      'scroll stopping'
    ];

    const queryLower = query.toLowerCase();

    return companyInfoIndicators.some(indicator =>
      queryLower.includes(indicator.toLowerCase())
    );
  }
}
