import { MCPToolRegistry } from "../mcp/mcp-tool-registry";
import { ToolDefinition } from "@copilotkit/react-core";

/**
 * Adapter class to convert MCP tools to CopilotKit tool definitions
 */
export class MCPToolAdapter {
  private toolRegistry: MCPToolRegistry;
  
  constructor() {
    this.toolRegistry = MCPToolRegistry.getInstance();
  }
  
  /**
   * Initialize the tool registry with the specified server IDs
   */
  async initialize(serverIds: string[] = []): Promise<void> {
    await this.toolRegistry.initialize(serverIds);
  }
  
  /**
   * Convert MCP tools to CopilotKit tool definitions
   */
  getToolDefinitions(): ToolDefinition[] {
    const mcpTools = this.toolRegistry.getAllTools();
    
    return mcpTools.map(mcpTool => {
      const toolInfo = this.toolRegistry.getToolInfo(mcpTool.name);
      
      if (!toolInfo) {
        return null;
      }
      
      // Convert MCP tool parameters to CopilotKit parameters
      const parameters = Object.entries(toolInfo.parameters || {}).map(([name, info]) => ({
        name,
        type: info.type || "string",
        description: info.description || "",
        required: info.required || false,
      }));
      
      // Create CopilotKit tool definition
      return {
        name: mcpTool.name,
        description: mcpTool.description,
        parameters,
        handler: async (params: Record<string, any>) => {
          try {
            // Call the MCP tool through the registry
            const result = await this.toolRegistry.callTool(mcpTool.name, params);
            return result;
          } catch (error) {
            console.error(`Error calling MCP tool ${mcpTool.name}:`, error);
            throw error;
          }
        },
      };
    }).filter(Boolean) as ToolDefinition[];
  }
  
  /**
   * Get tool definitions for a specific MCP server
   */
  getToolDefinitionsForServer(serverId: string): ToolDefinition[] {
    const mcpTools = this.toolRegistry.getAllTools();
    
    return mcpTools
      .filter(tool => {
        const toolInfo = this.toolRegistry.getToolInfo(tool.name);
        return toolInfo && toolInfo.serverId === serverId;
      })
      .map(mcpTool => {
        const toolInfo = this.toolRegistry.getToolInfo(mcpTool.name);
        
        // Convert MCP tool parameters to CopilotKit parameters
        const parameters = Object.entries(toolInfo?.parameters || {}).map(([name, info]) => ({
          name,
          type: info.type || "string",
          description: info.description || "",
          required: info.required || false,
        }));
        
        // Create CopilotKit tool definition
        return {
          name: mcpTool.name,
          description: mcpTool.description,
          parameters,
          handler: async (params: Record<string, any>) => {
            try {
              // Call the MCP tool through the registry
              const result = await this.toolRegistry.callTool(mcpTool.name, params);
              return result;
            } catch (error) {
              console.error(`Error calling MCP tool ${mcpTool.name}:`, error);
              throw error;
            }
          },
        };
      });
  }
}
