/**
 * Agent Manager
 *
 * This module provides a manager for agent instances.
 * This is a placeholder implementation until the full agent system is complete.
 */

import { AgentSettings } from '../../types';
import { MCPToolRegistry } from '../mcp/mcp-tool-registry';

/**
 * Agent Manager class
 */
export class AgentManager {
  private static instance: AgentManager;
  private configs: AgentSettings[] = [];
  private toolRegistry: MCPToolRegistry;
  private activeMCPServers: Set<string> = new Set();

  /**
   * Private constructor to enforce singleton pattern
   */
  private constructor() {
    this.toolRegistry = MCPToolRegistry.getInstance();
  }

  /**
   * Get the singleton instance of the Agent Manager
   */
  public static getInstance(): AgentManager {
    if (!AgentManager.instance) {
      AgentManager.instance = new AgentManager();
    }
    return AgentManager.instance;
  }

  /**
   * Initialize the Agent Manager with agent configurations
   */
  public async initialize(configs: AgentSettings[]): Promise<void> {
    this.configs = configs;

    // Initialize MCP Tool Registry
    await this.initializeMCPToolRegistry();

    console.log('Agent Manager initialized with', configs.length, 'agent configurations');
  }

  /**
   * Initialize MCP Tool Registry
   */
  private async initializeMCPToolRegistry(): Promise<void> {
    // Collect all MCP server IDs from agent configs
    const mcpServerIds = new Set<string>();

    // Assign appropriate MCP servers based on agent type if not already assigned
    for (const config of this.configs) {
      // Assign default MCP servers based on agent type if not already assigned
      if (!config.mcpServerId) {
        switch (config.id) {
          case 'general-agent':
            // General agent can use all servers
            config.mcpServerId = 'all';
            break;
          case 'rag-agent':
            // RAG agent primarily uses Supabase for database access
            config.mcpServerId = 'supabase';
            break;
          case 'code-agent':
            // Code agent primarily uses Context7 for code context
            config.mcpServerId = 'context7';
            break;
          case 'research-agent':
            // Research agent primarily uses web search tools
            config.mcpServerId = 'github';
            break;
          case 'voice-agent':
            // Voice agent uses VAPI for telephony
            config.mcpServerId = 'vapi';
            break;
          default:
            // Default to all servers
            config.mcpServerId = 'all';
        }

        console.log(`Assigned MCP server '${config.mcpServerId}' to agent '${config.id}'`);
      }

      // Add the server ID to the set
      if (config.mcpServerId === 'all') {
        // Add all available servers
        mcpServerIds.add('context7');
        mcpServerIds.add('github');
        mcpServerIds.add('supabase');
        mcpServerIds.add('vapi');
        mcpServerIds.add('telnyx');
      } else {
        mcpServerIds.add(config.mcpServerId);
      }
    }

    if (mcpServerIds.size > 0) {
      try {
        // Initialize the tool registry with all required servers
        console.log(`Initializing MCP Tool Registry with servers: ${Array.from(mcpServerIds).join(', ')}`);
        await this.toolRegistry.initialize(Array.from(mcpServerIds));

        // Mark servers as active
        for (const serverId of mcpServerIds) {
          this.activeMCPServers.add(serverId);
        }

        // Log the active servers
        console.log(`Active MCP servers: ${Array.from(this.activeMCPServers).join(', ')}`);
      } catch (error) {
        console.error('Error initializing MCP Tool Registry:', error);
      }
    }
  }

  /**
   * Get agent configuration by ID
   */
  public getAgentConfig(agentId: string): AgentSettings | undefined {
    return this.configs.find(config => config.id === agentId);
  }

  /**
   * Get all agent configurations
   */
  public getAllAgentConfigs(): AgentSettings[] {
    return [...this.configs];
  }

  /**
   * Get active MCP servers
   */
  public getActiveMCPServers(): string[] {
    return Array.from(this.activeMCPServers);
  }

  /**
   * Process a message using the appropriate agent
   */
  public async processMessage(message: string, agentId?: string): Promise<{
    response: string;
    agentId: string;
  }> {
    // If no agent ID is provided, use the first available agent
    if (!agentId) {
      const firstAgent = this.configs.find(config => config.enabled);
      if (firstAgent) {
        agentId = firstAgent.id;
      } else {
        throw new Error('No enabled agents available');
      }
    }

    // Get the agent configuration
    const agentConfig = this.getAgentConfig(agentId);
    if (!agentConfig) {
      throw new Error(`Agent not found: ${agentId}`);
    }

    // This is a placeholder implementation until the full agent system is complete
    console.log(`Processing message with agent ${agentId}: "${message}"`);

    // Return a mock response
    return {
      response: `[Agent ${agentConfig.name}] This is a placeholder response to: "${message}"`,
      agentId
    };
  }
}
