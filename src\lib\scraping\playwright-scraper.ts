/**
 * Web Scraper
 *
 * This module provides a browser-compatible implementation of website scraping.
 * It uses CORS proxies to access websites directly from the browser.
 */

/**
 * Options for website scraping
 */
export interface ScrapingOptions {
  followLinks?: boolean;
  maxPages?: number;
  extractPdfText?: boolean;
  includeImages?: boolean;
  timeout?: number;
  userAgent?: string;
}

/**
 * Result of a scraping operation
 */
export interface ScrapingResult {
  url: string;
  title: string;
  content: string;
  links: string[];
  timestamp: Date;
  error?: string;
}

/**
 * Default scraping options
 */
const DEFAULT_OPTIONS: ScrapingOptions = {
  followLinks: true,
  maxPages: 10,
  extractPdfText: true,
  includeImages: true,
  timeout: 30000,
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
};

/**
 * Web Scraper class
 * This is a browser-compatible implementation that uses CORS proxies to scrape websites
 */
export class PlaywrightScraper {
  private visitedUrls: Set<string> = new Set();
  private options: ScrapingOptions;

  /**
   * Create a new PlaywrightScraper
   */
  constructor(options: ScrapingOptions = {}) {
    this.options = { ...DEFAULT_OPTIONS, ...options };
  }

  /**
   * Scrape a website using a CORS proxy
   */
  async scrapeWebsite(url: string, options?: ScrapingOptions): Promise<ScrapingResult[]> {
    const mergedOptions = { ...this.options, ...options };
    this.visitedUrls.clear();

    try {
      console.log(`Scraping ${url} with options:`, mergedOptions);

      // Use a CORS proxy to access the website
      // We'll try multiple CORS proxies in case one fails
      const corsProxies = [
        'https://api.allorigins.win/get?url=',
        'https://api.codetabs.com/v1/proxy?quest=',
        'https://corsproxy.io/?'
      ];

      // Use the first proxy by default
      let corsProxyUrl = corsProxies[0];

      // Try to scrape the main URL with each proxy until one works
      const results: ScrapingResult[] = [];
      let mainPageResult = null;

      // Try each proxy for the main URL
      for (const proxy of corsProxies) {
        mainPageResult = await this.scrapeUrl(url, proxy);
        if (mainPageResult) {
          console.log(`Successfully scraped main page with proxy: ${proxy}`);
          break;
        }
      }

      // If all proxies failed, try a direct fetch as a last resort
      if (!mainPageResult) {
        console.log("All proxies failed. Trying direct fetch as a last resort...");
        try {
          // Try a direct fetch (this will only work if the site has CORS enabled)
          mainPageResult = await this.scrapeUrlDirect(url);
          if (mainPageResult) {
            console.log("Successfully scraped main page with direct fetch");
          }
        } catch (directError) {
          console.error("Direct fetch also failed:", directError);
        }
      }

      // If we couldn't scrape the main page with any method, log the error
      if (!mainPageResult) {
        console.error("All scraping methods failed. No mock data will be used.");
      }

      // If we have a result (either real or mock), process it
      if (mainPageResult) {
        results.push(mainPageResult);
        this.visitedUrls.add(url);

        // Extract links from the main page
        const links = mainPageResult.links;

        // If following links is enabled, scrape the linked pages
        if (mergedOptions.followLinks && links.length > 0) {
          const maxPages = mergedOptions.maxPages || DEFAULT_OPTIONS.maxPages!;
          const domain = new URL(url).hostname;

          // Keep track of how many pages we've scraped
          let scrapedCount = 1; // We've already scraped the main page

          // Process each link
          for (const link of links) {
            // Check if we've reached the maximum number of pages
            if (scrapedCount >= maxPages) {
              break;
            }

            try {
              // Make sure the link is from the same domain
              const linkUrl = new URL(link);
              if (linkUrl.hostname === domain && !this.visitedUrls.has(link)) {
                // Try each proxy for the linked page
                let linkedPageResult = null;

                for (const proxy of corsProxies) {
                  linkedPageResult = await this.scrapeUrl(link, proxy);
                  if (linkedPageResult) break;
                }

                // If all proxies failed, try a direct fetch as a last resort
                if (!linkedPageResult) {
                  console.log(`All proxies failed for ${link}. Trying direct fetch...`);
                  try {
                    linkedPageResult = await this.scrapeUrlDirect(link);
                  } catch (directError) {
                    console.error(`Direct fetch also failed for ${link}:`, directError);
                  }
                }

                // If real scraping failed, log the error
                if (!linkedPageResult) {
                  console.error(`Failed to scrape linked page: ${link}`);
                }

                if (linkedPageResult) {
                  results.push(linkedPageResult);
                  this.visitedUrls.add(link);
                  scrapedCount++;

                  // Add a small delay to avoid overloading the server
                  await new Promise(resolve => setTimeout(resolve, 1000));
                }
              }
            } catch (linkError) {
              console.error(`Error processing link ${link}:`, linkError);
            }
          }
        }
      }

      console.log(`Scraping completed. Found ${results.length} pages.`);
      return results;
    } catch (error) {
      console.error(`Error scraping ${url}:`, error);
      return [{
        url,
        title: 'Error',
        content: 'Failed to scrape website',
        links: [],
        timestamp: new Date(),
        error: error instanceof Error ? error.message : String(error)
      }];
    }
  }

  /**
   * Scrape a single URL using a CORS proxy
   */
  private async scrapeUrl(url: string, corsProxyUrl: string): Promise<ScrapingResult | null> {
    // List of CORS proxies to try
    const corsProxies = [
      'https://api.allorigins.win/get?url=',
      'https://api.codetabs.com/v1/proxy?quest=',
      'https://corsproxy.io/?'
    ];

    // Start with the provided proxy
    let proxyIndex = corsProxies.indexOf(corsProxyUrl);
    if (proxyIndex === -1) proxyIndex = 0;

    // Try each proxy until one works
    for (let i = 0; i < corsProxies.length; i++) {
      const currentProxyIndex = (proxyIndex + i) % corsProxies.length;
      const currentProxy = corsProxies[currentProxyIndex];

      try {
        console.log(`Trying to fetch ${url} with proxy: ${currentProxy}`);

        // Use the CORS proxy to fetch the page
        console.log(`Fetching ${currentProxy}${encodeURIComponent(url)}`);
        const response = await fetch(currentProxy + encodeURIComponent(url));

        if (!response.ok) {
          console.warn(`Proxy ${currentProxy} failed with status: ${response.status}`);
          continue; // Try the next proxy
        }

        // Different proxies return different formats
        let html = '';
        const contentType = response.headers.get('content-type') || '';

        if (contentType.includes('application/json')) {
          // For proxies that return JSON (like allorigins)
          const data = await response.json();
          console.log('Received JSON response:', data);

          // Handle allorigins.win format
          if (data && data.contents) {
            html = data.contents;
          } else if (data && typeof data === 'string') {
            html = data;
          } else {
            console.warn(`Proxy ${currentProxy} returned unexpected JSON format`);
            continue;
          }
        } else {
          // For proxies that return HTML directly
          html = await response.text();
        }

        // If we got an empty response, try the next proxy
        if (!html || html.trim().length === 0) {
          console.warn(`Proxy ${currentProxy} returned empty response`);
          continue;
        }

        console.log(`Got HTML response (${html.length} characters)`);

        // Log a sample of the HTML for debugging
        console.log('HTML sample:', html.substring(0, 200) + '...');

        // Create a DOM parser to extract content
        const parser = new DOMParser();
        const doc = parser.parseFromString(html, 'text/html');

        // Extract the title
        const title = doc.title || new URL(url).pathname;

        // Extract the main content
        let content = '';

        // Try to find the main content
        const mainContent = doc.querySelector('main') ||
                           doc.querySelector('article') ||
                           doc.querySelector('.content') ||
                           doc.querySelector('#content');

        if (mainContent) {
          content = mainContent.textContent || '';
        } else {
          // If no main content container is found, extract text from paragraphs
          const paragraphs = doc.querySelectorAll('p');
          content = Array.from(paragraphs)
            .map(p => p.textContent)
            .filter(text => text && text.trim().length > 20) // Filter out short paragraphs
            .join('\n\n');
        }

        // Clean up the content
        content = content.replace(/\s+/g, ' ').trim();

        // If content is still empty, try to get text from the body
        if (!content && doc.body) {
          content = doc.body.textContent || '';
          content = content.replace(/\s+/g, ' ').trim();
        }

        // Extract links
        const links: string[] = [];
        const linkElements = doc.querySelectorAll('a[href]');

        linkElements.forEach(link => {
          const href = link.getAttribute('href');
          if (href) {
            try {
              // Convert relative URLs to absolute
              const absoluteUrl = new URL(href, url).href;
              // Only include links from the same domain
              if (new URL(absoluteUrl).hostname === new URL(url).hostname) {
                links.push(absoluteUrl);
              }
            } catch (e) {
              // Ignore invalid URLs
            }
          }
        });

        console.log(`Successfully scraped ${url} with proxy: ${currentProxy}`);
        console.log(`Found ${links.length} links and ${content.length} characters of content`);

        return {
          url,
          title,
          content,
          links,
          timestamp: new Date()
        };
      } catch (error) {
        console.error(`Error with proxy ${currentProxy} for ${url}:`, error);
        // Continue to the next proxy
      }
    }

    // If we've tried all proxies and none worked, return null
    console.error(`All proxies failed for ${url}`);
    return null;
  }

  /**
   * Try to scrape a URL directly without a proxy
   * This will only work if the site has CORS enabled
   */
  private async scrapeUrlDirect(url: string): Promise<ScrapingResult | null> {
    try {
      console.log(`Trying direct fetch for ${url}`);

      // Try a direct fetch
      const response = await fetch(url, {
        mode: 'cors',
        headers: {
          'User-Agent': this.options.userAgent || DEFAULT_OPTIONS.userAgent!
        }
      });

      if (!response.ok) {
        console.warn(`Direct fetch failed with status: ${response.status}`);
        return null;
      }

      const html = await response.text();

      // If we got an empty response, return null
      if (!html || html.trim().length === 0) {
        console.warn(`Direct fetch returned empty response`);
        return null;
      }

      console.log(`Got HTML response from direct fetch (${html.length} characters)`);

      // Create a DOM parser to extract content
      const parser = new DOMParser();
      const doc = parser.parseFromString(html, 'text/html');

      // Extract the title
      const title = doc.title || new URL(url).pathname;

      // Extract the main content
      let content = '';

      // Try to find the main content
      const mainContent = doc.querySelector('main') ||
                         doc.querySelector('article') ||
                         doc.querySelector('.content') ||
                         doc.querySelector('#content');

      if (mainContent) {
        content = mainContent.textContent || '';
      } else {
        // If no main content container is found, extract text from paragraphs
        const paragraphs = doc.querySelectorAll('p');
        content = Array.from(paragraphs)
          .map(p => p.textContent)
          .filter(text => text && text.trim().length > 20) // Filter out short paragraphs
          .join('\n\n');
      }

      // Clean up the content
      content = content.replace(/\s+/g, ' ').trim();

      // If content is still empty, try to get text from the body
      if (!content && doc.body) {
        content = doc.body.textContent || '';
        content = content.replace(/\s+/g, ' ').trim();
      }

      // Extract links
      const links: string[] = [];
      const linkElements = doc.querySelectorAll('a[href]');

      linkElements.forEach(link => {
        const href = link.getAttribute('href');
        if (href) {
          try {
            // Convert relative URLs to absolute
            const absoluteUrl = new URL(href, url).href;
            // Only include links from the same domain
            if (new URL(absoluteUrl).hostname === new URL(url).hostname) {
              links.push(absoluteUrl);
            }
          } catch (e) {
            // Ignore invalid URLs
          }
        }
      });

      console.log(`Successfully scraped ${url} with direct fetch`);
      console.log(`Found ${links.length} links and ${content.length} characters of content`);

      return {
        url,
        title,
        content,
        links,
        timestamp: new Date()
      };
    } catch (error) {
      console.error(`Error with direct fetch for ${url}:`, error);
      return null;
    }
  }
}
