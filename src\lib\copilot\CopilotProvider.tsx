"use client";

import React, { ReactNode, useEffect, useState } from "react";
import { CopilotKit, CopilotKitOptions } from "@copilotkit/react-core";
import { MCPToolRegistry } from "../mcp/mcp-tool-registry";
import { useMCPTools } from "./useMCPTools";

interface CopilotProviderProps {
  children: ReactNode;
  mcpServers?: string[];
}

/**
 * CopilotProvider component that wraps the application with CopilotKit
 * and connects it to our MCP tools
 */
export function CopilotProvider({
  children,
  mcpServers = ["context7"]
}: CopilotProviderProps) {
  // Get API key from environment variable
  const apiKey = process.env.NEXT_PUBLIC_OPENAI_API_KEY || "";
  const [isInitialized, setIsInitialized] = useState(false);

  // Initialize MCP tools
  const { isInitialized: mcpInitialized } = useMCPTools({
    serverIds: mcpServers,
    enabled: true,
  });

  // Update initialization status
  useEffect(() => {
    if (mcpInitialized) {
      setIsInitialized(true);
    }
  }, [mcpInitialized]);

  // Configure CopilotKit options
  const copilotKitOptions: CopilotKitOptions = {
    // Use our own backend API endpoint for chat
    chatApiEndpoint: "/api/copilot/chat",

    // Default system prompt for the AI assistant
    defaultSystemPrompt: `You are a helpful AI assistant for Phoenix Roofing and Repair.

Phoenix Roofing and Repair is located at 532 E. Maryland Suite F, Phoenix, AZ 85012.
Our phone number is (602) 837-ROOF (7663).
We offer FREE estimates for all roofing services.

Help users with their roofing needs, answer questions, and assist with scheduling appointments.
Use the available tools to access information and perform actions when needed.

IMPORTANT: You have access to Context7 tools that allow you to search code and get documentation.
When asked about code or technical topics, use these tools to provide accurate information.`,

    // API key for OpenAI (if using direct integration)
    apiKey: apiKey,

    // Default model to use
    models: {
      default: "gpt-4o",
    },
  };

  return (
    <CopilotKit {...copilotKitOptions}>
      {children}
    </CopilotKit>
  );
}
