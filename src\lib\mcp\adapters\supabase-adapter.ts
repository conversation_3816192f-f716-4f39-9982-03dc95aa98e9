/**
 * Supabase MCP Adapter
 *
 * This adapter provides tools for interacting with Supabase databases,
 * demonstrating how to create additional MCP server adapters.
 */

import { BaseMCPAdapter } from '../base-adapter';
import { MCPTool, MCPToolInfo, MCPServerConfig } from '../types';

/**
 * Supabase MCP Server Adapter
 */
export class SupabaseAdapter extends BaseMCPAdapter {
  private supabaseUrl: string = '';
  private supabaseKey: string = '';

  constructor() {
    super(
      'supabase',
      'Supabase Database Server',
      '1.0.0',
      {
        supportsStreaming: false,
        supportsAuthentication: true,
        supportsBatching: true,
        maxConcurrentRequests: 20,
        rateLimit: {
          requests: 1000,
          window: 60000 // 1 minute
        }
      }
    );
  }

  /**
   * Perform adapter-specific initialization
   */
  protected async doInitialize(): Promise<void> {
    this.supabaseUrl = this.config.endpoint || import.meta.env.VITE_SUPABASE_URL || '';
    this.supabaseKey = this.config.apiKey || import.meta.env.VITE_SUPABASE_ANON_KEY || '';

    if (!this.supabaseUrl || !this.supabaseKey) {
      console.warn('Supabase URL or API key not provided, using mock responses');
    }
  }

  /**
   * Perform adapter-specific shutdown
   */
  protected async doShutdown(): Promise<void> {
    // Cleanup any resources if needed
  }

  /**
   * Load tools from the Supabase server
   */
  protected async loadTools(): Promise<void> {
    // Define the tools provided by Supabase
    this.tools = [
      {
        name: 'supabase_query_table',
        description: 'Query data from a Supabase table',
        serverId: this.id,
        category: 'database',
        tags: ['query', 'select', 'data'],
        priority: 1
      },
      {
        name: 'supabase_insert_data',
        description: 'Insert data into a Supabase table',
        serverId: this.id,
        category: 'database',
        tags: ['insert', 'create', 'data'],
        priority: 2
      },
      {
        name: 'supabase_update_data',
        description: 'Update data in a Supabase table',
        serverId: this.id,
        category: 'database',
        tags: ['update', 'modify', 'data'],
        priority: 2
      },
      {
        name: 'supabase_delete_data',
        description: 'Delete data from a Supabase table',
        serverId: this.id,
        category: 'database',
        tags: ['delete', 'remove', 'data'],
        priority: 3
      },
      {
        name: 'supabase_get_schema',
        description: 'Get the schema information for a table',
        serverId: this.id,
        category: 'database',
        tags: ['schema', 'structure', 'metadata'],
        priority: 2
      }
    ];

    // Define detailed tool info for each tool
    this.toolInfoMap.set('supabase_query_table', {
      name: 'supabase_query_table',
      description: 'Query data from a Supabase table with filtering and pagination',
      serverId: this.id,
      category: 'database',
      tags: ['query', 'select', 'data'],
      parameters: {
        table: {
          type: 'string',
          description: 'The name of the table to query',
          required: true,
          minLength: 1,
          maxLength: 100
        },
        select: {
          type: 'string',
          description: 'Columns to select (comma-separated, or * for all)',
          required: false,
          default: '*'
        },
        filter: {
          type: 'string',
          description: 'Filter conditions in Supabase format (e.g., "id.eq.1")',
          required: false
        },
        limit: {
          type: 'number',
          description: 'Maximum number of rows to return',
          required: false,
          default: 100,
          minimum: 1,
          maximum: 1000
        },
        offset: {
          type: 'number',
          description: 'Number of rows to skip for pagination',
          required: false,
          default: 0,
          minimum: 0
        }
      },
      examples: [
        {
          description: 'Get all users',
          parameters: { table: 'users', select: '*', limit: 10 }
        },
        {
          description: 'Get specific user by ID',
          parameters: { table: 'users', select: 'id,name,email', filter: 'id.eq.123' }
        }
      ],
      timeout: 10000
    });

    this.toolInfoMap.set('supabase_insert_data', {
      name: 'supabase_insert_data',
      description: 'Insert new data into a Supabase table',
      serverId: this.id,
      category: 'database',
      tags: ['insert', 'create', 'data'],
      parameters: {
        table: {
          type: 'string',
          description: 'The name of the table to insert into',
          required: true,
          minLength: 1,
          maxLength: 100
        },
        data: {
          type: 'string',
          description: 'JSON string of the data to insert',
          required: true,
          minLength: 2
        }
      },
      examples: [
        {
          description: 'Insert a new user',
          parameters: {
            table: 'users',
            data: '{"name": "John Doe", "email": "<EMAIL>"}'
          }
        }
      ],
      timeout: 5000
    });

    this.toolInfoMap.set('supabase_update_data', {
      name: 'supabase_update_data',
      description: 'Update existing data in a Supabase table',
      serverId: this.id,
      category: 'database',
      tags: ['update', 'modify', 'data'],
      parameters: {
        table: {
          type: 'string',
          description: 'The name of the table to update',
          required: true,
          minLength: 1,
          maxLength: 100
        },
        data: {
          type: 'string',
          description: 'JSON string of the data to update',
          required: true,
          minLength: 2
        },
        filter: {
          type: 'string',
          description: 'Filter conditions to identify rows to update',
          required: true,
          minLength: 1
        }
      },
      examples: [
        {
          description: 'Update user email',
          parameters: {
            table: 'users',
            data: '{"email": "<EMAIL>"}',
            filter: 'id.eq.123'
          }
        }
      ],
      timeout: 5000
    });

    this.toolInfoMap.set('supabase_delete_data', {
      name: 'supabase_delete_data',
      description: 'Delete data from a Supabase table',
      serverId: this.id,
      category: 'database',
      tags: ['delete', 'remove', 'data'],
      parameters: {
        table: {
          type: 'string',
          description: 'The name of the table to delete from',
          required: true,
          minLength: 1,
          maxLength: 100
        },
        filter: {
          type: 'string',
          description: 'Filter conditions to identify rows to delete',
          required: true,
          minLength: 1
        }
      },
      examples: [
        {
          description: 'Delete a user by ID',
          parameters: {
            table: 'users',
            filter: 'id.eq.123'
          }
        }
      ],
      timeout: 5000
    });

    this.toolInfoMap.set('supabase_get_schema', {
      name: 'supabase_get_schema',
      description: 'Get schema information for a Supabase table',
      serverId: this.id,
      category: 'database',
      tags: ['schema', 'structure', 'metadata'],
      parameters: {
        table: {
          type: 'string',
          description: 'The name of the table to get schema for',
          required: true,
          minLength: 1,
          maxLength: 100
        }
      },
      examples: [
        {
          description: 'Get users table schema',
          parameters: { table: 'users' }
        }
      ],
      timeout: 5000
    });
  }

  /**
   * Perform a health check
   */
  protected async performHealthCheck(): Promise<void> {
    if (this.supabaseUrl && this.supabaseKey) {
      // In a real implementation, this would make an actual API call
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }

  /**
   * Handle configuration updates
   */
  protected async doConfigUpdate(oldConfig: MCPServerConfig, newConfig: MCPServerConfig): Promise<void> {
    if (oldConfig.apiKey !== newConfig.apiKey) {
      this.supabaseKey = newConfig.apiKey || '';
    }

    if (oldConfig.endpoint !== newConfig.endpoint) {
      this.supabaseUrl = newConfig.endpoint || '';
    }
  }

  /**
   * Execute the actual tool call
   */
  protected async doCallTool(toolName: string, params: Record<string, any>, context?: {
    agentId?: string;
    conversationId?: string;
    timeout?: number;
  }): Promise<any> {
    switch (toolName) {
      case 'supabase_query_table':
        return await this.queryTable(params.table, params.select, params.filter, params.limit, params.offset);
      case 'supabase_insert_data':
        return await this.insertData(params.table, params.data);
      case 'supabase_update_data':
        return await this.updateData(params.table, params.data, params.filter);
      case 'supabase_delete_data':
        return await this.deleteData(params.table, params.filter);
      case 'supabase_get_schema':
        return await this.getSchema(params.table);
      default:
        throw new Error(`Unknown tool: ${toolName}`);
    }
  }

  /**
   * Query data from a table
   */
  private async queryTable(table: string, select: string = '*', filter?: string, limit: number = 100, offset: number = 0): Promise<any> {
    console.log(`Supabase: Querying table ${table}, select: ${select}, filter: ${filter}, limit: ${limit}, offset: ${offset}`);

    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 200 + Math.random() * 300));

    // Mock response
    return {
      data: [
        { id: 1, name: 'John Doe', email: '<EMAIL>', created_at: '2024-01-01T00:00:00Z' },
        { id: 2, name: 'Jane Smith', email: '<EMAIL>', created_at: '2024-01-02T00:00:00Z' }
      ].slice(offset, offset + limit),
      count: 2,
      table,
      query: { select, filter, limit, offset }
    };
  }

  /**
   * Insert data into a table
   */
  private async insertData(table: string, data: string): Promise<any> {
    console.log(`Supabase: Inserting data into table ${table}:`, data);

    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 300 + Math.random() * 200));

    try {
      const parsedData = JSON.parse(data);

      // Mock response
      return {
        data: [{ ...parsedData, id: Math.floor(Math.random() * 1000) + 1, created_at: new Date().toISOString() }],
        status: 'success',
        table,
        inserted: 1
      };
    } catch (error) {
      throw new Error(`Invalid JSON data: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Update data in a table
   */
  private async updateData(table: string, data: string, filter: string): Promise<any> {
    console.log(`Supabase: Updating data in table ${table} with filter ${filter}:`, data);

    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 250 + Math.random() * 250));

    try {
      const parsedData = JSON.parse(data);

      // Mock response
      return {
        data: [{ ...parsedData, updated_at: new Date().toISOString() }],
        status: 'success',
        table,
        filter,
        updated: 1
      };
    } catch (error) {
      throw new Error(`Invalid JSON data: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Delete data from a table
   */
  private async deleteData(table: string, filter: string): Promise<any> {
    console.log(`Supabase: Deleting data from table ${table} with filter ${filter}`);

    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 200 + Math.random() * 200));

    // Mock response
    return {
      status: 'success',
      table,
      filter,
      deleted: 1
    };
  }

  /**
   * Get schema information for a table
   */
  private async getSchema(table: string): Promise<any> {
    console.log(`Supabase: Getting schema for table ${table}`);

    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 150 + Math.random() * 150));

    // Mock schema response
    return {
      table,
      columns: [
        { name: 'id', type: 'integer', nullable: false, primary_key: true },
        { name: 'name', type: 'text', nullable: false, primary_key: false },
        { name: 'email', type: 'text', nullable: false, primary_key: false },
        { name: 'created_at', type: 'timestamp', nullable: false, primary_key: false },
        { name: 'updated_at', type: 'timestamp', nullable: true, primary_key: false }
      ],
      indexes: [
        { name: 'users_pkey', columns: ['id'], unique: true },
        { name: 'users_email_idx', columns: ['email'], unique: true }
      ],
      constraints: [
        { name: 'users_email_check', type: 'check', definition: 'email ~* \'^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$\'' }
      ]
    };
  }
}
