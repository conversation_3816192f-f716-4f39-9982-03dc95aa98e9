/**
 * MCP Server Adapter Interface
 * 
 * This module defines the interface for MCP server adapters.
 * Each adapter is responsible for communicating with a specific MCP server
 * and providing tools that can be used by the MCP tool registry.
 */

import { MCPTool, MCPToolInfo } from './mcp-tool-registry';

/**
 * MCP Server Adapter interface
 */
export interface MCPServerAdapter {
  /**
   * Initialize the adapter
   */
  initialize(): Promise<void>;

  /**
   * Get all tools provided by this adapter
   */
  getTools(): MCPTool[];

  /**
   * Get tool info for a specific tool
   */
  getToolInfo(toolName: string): MCPToolInfo | undefined;

  /**
   * Call a tool with the given parameters
   */
  callTool(toolName: string, params: Record<string, any>): Promise<any>;
}
