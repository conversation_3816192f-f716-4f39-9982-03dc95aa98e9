/**
 * Universal MCP Server Adapter Interface
 *
 * This module defines the standardized interface for MCP server adapters.
 * Each adapter is responsible for communicating with a specific MCP server
 * and providing tools that can be used by the MCP tool registry.
 */

import { MCPTool, MCPToolInfo, MCPServerConfig, MCPServerStatus, MCPToolResult } from './types';

/**
 * MCP Server Health Status
 */
export interface MCPServerHealth {
  status: 'healthy' | 'degraded' | 'unhealthy';
  lastCheck: number;
  responseTime: number;
  errorCount: number;
  uptime: number;
}

/**
 * MCP Server Capabilities
 */
export interface MCPServerCapabilities {
  supportsStreaming: boolean;
  supportsAuthentication: boolean;
  supportsBatching: boolean;
  maxConcurrentRequests: number;
  rateLimit?: {
    requests: number;
    window: number; // in milliseconds
  };
}

/**
 * Universal MCP Server Adapter interface
 */
export interface MCPServerAdapter {
  /**
   * Unique identifier for this adapter
   */
  readonly id: string;

  /**
   * Human-readable name for this adapter
   */
  readonly name: string;

  /**
   * Version of the adapter
   */
  readonly version: string;

  /**
   * Server capabilities
   */
  readonly capabilities: MCPServerCapabilities;

  /**
   * Initialize the adapter with configuration
   */
  initialize(config: MCPServerConfig): Promise<void>;

  /**
   * Shutdown the adapter and cleanup resources
   */
  shutdown(): Promise<void>;

  /**
   * Check if the adapter is initialized and ready
   */
  isReady(): boolean;

  /**
   * Get current health status
   */
  getHealth(): Promise<MCPServerHealth>;

  /**
   * Get server configuration
   */
  getConfig(): MCPServerConfig;

  /**
   * Update server configuration (hot reload)
   */
  updateConfig(config: Partial<MCPServerConfig>): Promise<void>;

  /**
   * Get all tools provided by this adapter
   */
  getTools(): MCPTool[];

  /**
   * Get tool info for a specific tool
   */
  getToolInfo(toolName: string): MCPToolInfo | undefined;

  /**
   * Validate tool parameters before execution
   */
  validateToolParameters(toolName: string, params: Record<string, any>): Promise<{
    valid: boolean;
    errors?: string[];
  }>;

  /**
   * Call a tool with the given parameters
   */
  callTool(toolName: string, params: Record<string, any>, context?: {
    agentId?: string;
    conversationId?: string;
    timeout?: number;
  }): Promise<MCPToolResult>;

  /**
   * Call multiple tools in batch (if supported)
   */
  callToolsBatch?(calls: Array<{
    toolName: string;
    params: Record<string, any>;
    id: string;
  }>, context?: {
    agentId?: string;
    conversationId?: string;
    timeout?: number;
  }): Promise<Array<MCPToolResult & { id: string }>>;

  /**
   * Get tool usage statistics
   */
  getToolStats(): Record<string, {
    callCount: number;
    successCount: number;
    errorCount: number;
    averageResponseTime: number;
    lastUsed: number;
  }>;

  /**
   * Reset tool statistics
   */
  resetToolStats(): void;

  /**
   * Subscribe to adapter events
   */
  on(event: 'tool-called' | 'error' | 'health-changed' | 'config-updated',
     callback: (data: any) => void): void;

  /**
   * Unsubscribe from adapter events
   */
  off(event: string, callback: (data: any) => void): void;
}
