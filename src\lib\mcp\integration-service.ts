/**
 * MCP Integration Service
 * 
 * This service ties together all MCP components and provides a unified interface
 * for the application to interact with the comprehensive MCP framework.
 */

import EventEmitter from 'eventemitter3';
import { MCPToolRegistry } from './mcp-tool-registry';
import { MCPServerDiscovery } from './server-discovery';
import { MCPConfigManager } from '../config/mcp-config-manager';
import { MCPErrorHandler } from './error-handler';
import { EnhancedAgentManager } from '../agents/enhanced-agent-manager';
import { AgentSettings } from '../../types';

/**
 * Integration Service Status
 */
interface IntegrationStatus {
  initialized: boolean;
  componentsReady: {
    toolRegistry: boolean;
    serverDiscovery: boolean;
    configManager: boolean;
    errorHandler: boolean;
    agentManager: boolean;
  };
  serverCount: number;
  toolCount: number;
  agentCount: number;
  errors: number;
  lastUpdate: number;
}

/**
 * MCP Integration Service
 */
export class MCPIntegrationService extends EventEmitter {
  private static instance: MCPIntegrationService;
  private toolRegistry: MCPToolRegistry;
  private serverDiscovery: MCPServerDiscovery;
  private configManager: MCPConfigManager;
  private errorHandler: MCPErrorHandler;
  private agentManager: EnhancedAgentManager;
  private initialized: boolean = false;
  private status: IntegrationStatus;

  /**
   * Get the singleton instance
   */
  static getInstance(): MCPIntegrationService {
    if (!MCPIntegrationService.instance) {
      MCPIntegrationService.instance = new MCPIntegrationService();
    }
    return MCPIntegrationService.instance;
  }

  constructor() {
    super();
    
    // Initialize components
    this.toolRegistry = MCPToolRegistry.getInstance();
    this.serverDiscovery = MCPServerDiscovery.getInstance();
    this.configManager = MCPConfigManager.getInstance();
    this.errorHandler = MCPErrorHandler.getInstance();
    this.agentManager = EnhancedAgentManager.getInstance();
    
    // Initialize status
    this.status = {
      initialized: false,
      componentsReady: {
        toolRegistry: false,
        serverDiscovery: false,
        configManager: false,
        errorHandler: false,
        agentManager: false
      },
      serverCount: 0,
      toolCount: 0,
      agentCount: 0,
      errors: 0,
      lastUpdate: Date.now()
    };
  }

  /**
   * Initialize the entire MCP integration framework
   */
  async initialize(agentConfigs?: AgentSettings[]): Promise<void> {
    if (this.initialized) {
      console.log('MCP Integration Service already initialized');
      return;
    }

    try {
      console.log('Initializing MCP Integration Service...');

      // Initialize components in order
      await this.initializeComponents(agentConfigs);
      
      // Set up event listeners
      this.setupEventListeners();
      
      // Update status
      this.updateStatus();
      
      this.initialized = true;
      this.emit('initialized', this.status);
      
      console.log('MCP Integration Service initialized successfully');
    } catch (error) {
      console.error('Error initializing MCP Integration Service:', error);
      this.emit('initialization-error', error);
      throw error;
    }
  }

  /**
   * Process a message through the integrated system
   */
  async processMessage(message: string, options: {
    agentId?: string;
    conversationId?: string;
    timeout?: number;
  } = {}): Promise<{
    response: string;
    agentId: string;
    toolsUsed: string[];
    collaborations: string[];
    confidence: number;
    metadata: {
      processingTime: number;
      serversUsed: string[];
      errors: any[];
    };
  }> {
    if (!this.initialized) {
      throw new Error('MCP Integration Service not initialized');
    }

    const startTime = Date.now();
    const errors: any[] = [];
    const serversUsed: string[] = [];

    try {
      // Process message through enhanced agent manager
      const result = await this.errorHandler.executeWithProtection(
        async () => {
          return await this.agentManager.processMessage(
            message, 
            options.agentId, 
            options.conversationId
          );
        },
        {
          serverId: 'agent-manager',
          agentId: options.agentId,
          timeout: options.timeout || 30000,
          retries: 2
        }
      );

      // Track servers used (simplified - would need more detailed tracking)
      const toolsUsed = result.toolsUsed || [];
      for (const tool of toolsUsed) {
        const toolInfo = this.toolRegistry.getToolInfo(tool);
        if (toolInfo && !serversUsed.includes(toolInfo.serverId)) {
          serversUsed.push(toolInfo.serverId);
        }
      }

      return {
        ...result,
        metadata: {
          processingTime: Date.now() - startTime,
          serversUsed,
          errors
        }
      };
    } catch (error) {
      errors.push(error);
      
      // Handle error through error handler
      const mcpError = await this.errorHandler.handleError(error, {
        agentId: options.agentId,
        operation: 'processMessage'
      });

      throw mcpError;
    }
  }

  /**
   * Get comprehensive system status
   */
  getStatus(): IntegrationStatus {
    this.updateStatus();
    return { ...this.status };
  }

  /**
   * Get detailed system information
   */
  getSystemInfo(): {
    status: IntegrationStatus;
    servers: any[];
    tools: any[];
    agents: any[];
    errors: any;
    performance: any;
  } {
    return {
      status: this.getStatus(),
      servers: this.serverDiscovery.getAllServerStatuses(),
      tools: this.toolRegistry.getAllTools(),
      agents: this.agentManager.getAllAgentConfigs(),
      errors: this.errorHandler.getErrorStats(),
      performance: {
        circuitBreakers: this.errorHandler.getCircuitBreakerStatus(),
        toolConflicts: Array.from(this.toolRegistry.getToolConflicts().entries()),
        serverLoads: Array.from(this.toolRegistry.getServerLoads().entries())
      }
    };
  }

  /**
   * Add a new MCP server configuration
   */
  async addServer(serverConfig: any): Promise<void> {
    try {
      // Add to configuration
      this.configManager.addServerConfig(serverConfig);
      
      // Trigger discovery to pick up the new server
      this.serverDiscovery.addServer(serverConfig);
      
      this.emit('server-added', serverConfig);
    } catch (error) {
      await this.errorHandler.handleError(error, {
        operation: 'addServer',
        serverId: serverConfig.id
      });
      throw error;
    }
  }

  /**
   * Remove an MCP server
   */
  async removeServer(serverId: string): Promise<void> {
    try {
      // Remove from configuration
      this.configManager.removeServerConfig(serverId);
      
      // Remove from discovery
      this.serverDiscovery.removeServer(serverId);
      
      this.emit('server-removed', { serverId });
    } catch (error) {
      await this.errorHandler.handleError(error, {
        operation: 'removeServer',
        serverId
      });
      throw error;
    }
  }

  /**
   * Update server configuration
   */
  async updateServer(serverId: string, updates: any): Promise<void> {
    try {
      // Update configuration
      this.configManager.updateServerConfig(serverId, updates);
      
      // Update discovery
      this.serverDiscovery.updateServer(serverId, updates);
      
      this.emit('server-updated', { serverId, updates });
    } catch (error) {
      await this.errorHandler.handleError(error, {
        operation: 'updateServer',
        serverId
      });
      throw error;
    }
  }

  /**
   * Add a routing rule
   */
  addRoutingRule(rule: any): void {
    try {
      this.configManager.addRoutingRule(rule);
      this.toolRegistry.addRoutingRule(rule);
      this.emit('routing-rule-added', rule);
    } catch (error) {
      this.errorHandler.handleError(error, {
        operation: 'addRoutingRule'
      });
      throw error;
    }
  }

  /**
   * Get agent performance metrics
   */
  getAgentPerformance(agentId?: string): any {
    if (agentId) {
      return this.agentManager.getAgentPerformance(agentId);
    }
    
    // Get performance for all agents
    const agents = this.agentManager.getAllAgentConfigs();
    const performance: any = {};
    
    for (const agent of agents) {
      performance[agent.id] = this.agentManager.getAgentPerformance(agent.id);
    }
    
    return performance;
  }

  /**
   * Reset system components
   */
  async reset(): Promise<void> {
    try {
      // Reset error handler
      this.errorHandler.clearErrorLog();
      
      // Reset configuration to defaults
      this.configManager.resetToDefaults();
      
      // Reinitialize
      await this.initialize();
      
      this.emit('system-reset');
    } catch (error) {
      await this.errorHandler.handleError(error, {
        operation: 'reset'
      });
      throw error;
    }
  }

  /**
   * Shutdown the integration service
   */
  async shutdown(): Promise<void> {
    try {
      console.log('Shutting down MCP Integration Service...');
      
      // Shutdown components in reverse order
      await this.agentManager.shutdown();
      await this.serverDiscovery.shutdown();
      await this.configManager.shutdown();
      
      this.initialized = false;
      this.removeAllListeners();
      
      console.log('MCP Integration Service shutdown complete');
    } catch (error) {
      console.error('Error during shutdown:', error);
      throw error;
    }
  }

  // Private methods

  /**
   * Initialize all components
   */
  private async initializeComponents(agentConfigs?: AgentSettings[]): Promise<void> {
    // Initialize configuration manager
    await this.configManager.initialize();
    this.status.componentsReady.configManager = true;
    
    // Initialize error handler
    this.errorHandler.initialize();
    this.status.componentsReady.errorHandler = true;
    
    // Initialize server discovery
    await this.serverDiscovery.initialize();
    this.status.componentsReady.serverDiscovery = true;
    
    // Initialize tool registry
    await this.toolRegistry.initialize();
    this.status.componentsReady.toolRegistry = true;
    
    // Initialize agent manager
    const configs = agentConfigs || this.getDefaultAgentConfigs();
    await this.agentManager.initialize(configs);
    this.status.componentsReady.agentManager = true;
  }

  /**
   * Set up event listeners between components
   */
  private setupEventListeners(): void {
    // Config manager events
    this.configManager.on('config-changed', (event) => {
      this.emit('config-changed', event);
      this.updateStatus();
    });

    // Error handler events
    this.errorHandler.on('error', (error) => {
      this.emit('error', error);
      this.updateStatus();
    });

    // Server discovery events
    this.serverDiscovery.on('server-discovered', (server) => {
      this.emit('server-discovered', server);
      this.updateStatus();
    });

    // Agent manager events
    this.agentManager.on('performance-update', (data) => {
      this.emit('performance-update', data);
      this.updateStatus();
    });
  }

  /**
   * Update system status
   */
  private updateStatus(): void {
    this.status.serverCount = this.serverDiscovery.getDiscoveredServers().length;
    this.status.toolCount = this.toolRegistry.getAllTools().length;
    this.status.agentCount = this.agentManager.getAllAgentConfigs().length;
    this.status.errors = this.errorHandler.getErrorStats().totalErrors;
    this.status.lastUpdate = Date.now();
  }

  /**
   * Get default agent configurations
   */
  private getDefaultAgentConfigs(): AgentSettings[] {
    return [
      {
        id: 'general-agent',
        name: 'General Assistant',
        description: 'A general-purpose AI assistant',
        systemPrompt: 'You are a helpful AI assistant.',
        model: 'anthropic/claude-3-7-sonnet',
        enabled: true,
        mcpServerId: 'context7',
        icon: 'brain'
      },
      {
        id: 'rag-agent',
        name: 'Knowledge Assistant',
        description: 'An AI assistant with access to knowledge base',
        systemPrompt: 'You are a knowledgeable AI assistant.',
        model: 'anthropic/claude-3-7-sonnet',
        enabled: true,
        mcpServerId: 'context7',
        icon: 'database'
      }
    ];
  }
}
