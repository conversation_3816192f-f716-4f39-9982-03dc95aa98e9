/**
 * Ad Creative Agent
 *
 * This module provides an agent specialized in creating marketing content,
 * ad concepts, and promotional materials using GPT-4.1.
 */

import { AgentSettings } from '../../types';

/**
 * Ad Creative Agent class
 */
export class AdCreativeAgent {
  private config: AgentSettings & { apiKey?: string };
  private defaultModel = 'openai/gpt-4.1';

  /**
   * Create a new Ad Creative Agent
   */
  constructor(config: AgentSettings) {
    this.config = {
      ...config,
      apiKey: import.meta.env.VITE_OPENROUTER_API_KEY,
      model: config.model || this.defaultModel
    };
  }

  /**
   * Initialize the Ad Creative Agent
   */
  async initialize(): Promise<void> {
    console.log('Ad Creative Agent initialized');
  }

  /**
   * Process a message with the Ad Creative Agent
   */
  async processMessage(message: string): Promise<string> {
    try {
      console.log(`Ad Creative Agent processing message: "${message}"`);

      // Determine the type of creative request
      const requestType = this.determineRequestType(message);
      console.log(`Request identified as: ${requestType}`);

      // Generate the creative content
      const response = await this.generateCreativeContent(message, requestType);

      return response;
    } catch (error) {
      console.error('Error processing message with Ad Creative Agent:', error);
      return `I encountered an error while generating creative content: ${error instanceof Error ? error.message : String(error)}`;
    }
  }

  /**
   * Determine the type of creative request
   */
  private determineRequestType(message: string): string {
    const messageLower = message.toLowerCase();

    // Check for specific requests to avoid split screens or before/after comparisons
    const avoidSplitScreen = messageLower.includes('avoid split screen') ||
                             messageLower.includes('no split screen') ||
                             messageLower.includes('without split screen') ||
                             messageLower.includes('avoid before and after') ||
                             messageLower.includes('no before and after') ||
                             messageLower.includes('without before and after');

    // Ad concepts
    if (messageLower.includes('ad idea') ||
        messageLower.includes('advertisement') ||
        messageLower.includes('campaign') ||
        messageLower.includes('creative') ||
        messageLower.includes('banner') ||
        messageLower.includes('billboard') ||
        messageLower.includes('marketing image')) {
      return 'ad_concept';
    }

    // Social media
    if (messageLower.includes('social media') ||
        messageLower.includes('post') ||
        messageLower.includes('instagram') ||
        messageLower.includes('facebook') ||
        messageLower.includes('twitter') ||
        messageLower.includes('linkedin') ||
        messageLower.includes('tiktok')) {
      return 'social_media';
    }

    // Email marketing
    if (messageLower.includes('email') ||
        messageLower.includes('newsletter') ||
        messageLower.includes('mailchimp') ||
        messageLower.includes('subject line')) {
      return 'email_marketing';
    }

    // Slogans and taglines
    if (messageLower.includes('slogan') ||
        messageLower.includes('tagline') ||
        messageLower.includes('motto') ||
        messageLower.includes('catchphrase')) {
      return 'slogan';
    }

    // Headlines and titles
    if (messageLower.includes('headline') ||
        messageLower.includes('title') ||
        messageLower.includes('heading') ||
        messageLower.includes('caption')) {
      return 'headline';
    }

    // If the message specifically mentions avoiding split screens but doesn't specify a type
    if (avoidSplitScreen) {
      return 'ad_concept'; // Default to ad concept for these specific requests
    }

    // Default to general creative content
    return 'general_creative';
  }

  /**
   * Generate creative content based on the request
   */
  private async generateCreativeContent(message: string, requestType: string): Promise<string> {
    // Create a prompt based on the request type
    const prompt = this.createPromptForRequestType(message, requestType);

    // Use the OpenRouter API to generate a response with GPT-4.1
    const apiKey = this.config.apiKey;
    if (!apiKey) {
      console.error('No API key available for Ad Creative Agent');
      return 'I need an API key to generate creative content. Please check your settings.';
    }

    try {
      // Make the API call
      const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${apiKey}`,
          'HTTP-Referer': window.location.href,
          'X-Title': 'Phoenix Roofing and Repair'
        },
        body: JSON.stringify({
          model: this.config.model,
          messages: [
            { role: 'system', content: this.getSystemPrompt(requestType) },
            { role: 'user', content: prompt }
          ],
          temperature: 0.8,
          max_tokens: 1500
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Error from Ad Creative API:', errorText);
        return `I encountered an error while generating creative content. Please try again later.`;
      }

      const data = await response.json();
      return data.choices[0].message.content;
    } catch (error) {
      console.error('Error generating creative content:', error);
      return `I encountered an error while generating creative content: ${error instanceof Error ? error.message : String(error)}`;
    }
  }

  /**
   * Create a prompt based on the request type
   */
  private createPromptForRequestType(message: string, requestType: string): string {
    const companyInfo = `
Phoenix Roofing and Repair is a professional roofing company based in Phoenix, Arizona.
They provide comprehensive roofing services including installation, repair, replacement, and maintenance for residential and commercial properties.
With over 15 years of experience, their licensed and insured team delivers high-quality workmanship and exceptional customer service.
They specialize in shingle roofing, tile roofing, flat roofing, and emergency roof repairs.
Contact information: (602) 837-ROOF, 532 E. Maryland Suite F, Phoenix, AZ 85012
    `;

    const creativeGuidelines = `
IMPORTANT CREATIVE GUIDELINES:
- DO NOT use split-screen layouts or before/after comparisons in any creative concepts
- Focus on single, powerful images that convey quality and professionalism
- Use storytelling and emotional appeals rather than direct visual comparisons
- Highlight customer testimonials, completed projects, and quality workmanship
- Emphasize reliability, expertise, and peace of mind
- Create unique concepts that stand out from typical roofing advertisements
    `;

    switch (requestType) {
      case 'ad_concept':
        return `${message}\n\nPlease create compelling ad concepts for Phoenix Roofing and Repair. Here's information about the company:\n${companyInfo}\n\n${creativeGuidelines}\n\nRemember to avoid split-screen layouts and before/after comparisons completely.`;

      case 'social_media':
        return `${message}\n\nPlease create engaging social media content for Phoenix Roofing and Repair. Here's information about the company:\n${companyInfo}\n\n${creativeGuidelines}\n\nFocus on educational content, customer stories, or showcasing completed projects with compelling narratives.`;

      case 'email_marketing':
        return `${message}\n\nPlease create effective email marketing content for Phoenix Roofing and Repair. Here's information about the company:\n${companyInfo}\n\n${creativeGuidelines}\n\nFocus on value propositions, seasonal offers, or educational content that establishes expertise.`;

      case 'slogan':
        return `${message}\n\nPlease create memorable slogans or taglines for Phoenix Roofing and Repair. Here's information about the company:\n${companyInfo}\n\n${creativeGuidelines}\n\nFocus on quality, reliability, and peace of mind rather than transformation or comparison concepts.`;

      case 'headline':
        return `${message}\n\nPlease create attention-grabbing headlines for Phoenix Roofing and Repair. Here's information about the company:\n${companyInfo}\n\n${creativeGuidelines}\n\nFocus on benefits, questions, or statements that evoke emotion or curiosity.`;

      default:
        return `${message}\n\nPlease create creative marketing content for Phoenix Roofing and Repair. Here's information about the company:\n${companyInfo}\n\n${creativeGuidelines}\n\nRemember to avoid split-screen layouts and before/after comparisons completely.`;
    }
  }

  /**
   * Get the system prompt based on the request type
   */
  private getSystemPrompt(requestType: string): string {
    const basePrompt = `You are an expert marketing and advertising creative director specializing in the roofing industry.
Your task is to create compelling, professional, and effective marketing content for Phoenix Roofing and Repair.
Focus on highlighting their expertise, quality service, and customer satisfaction.
Be creative, engaging, and persuasive while maintaining a professional tone.
Include clear calls to action where appropriate.

IMPORTANT CREATIVE GUIDELINES:
1. DO NOT create split-screen ad concepts (before/after comparisons)
2. DO NOT use before/after comparison formats in any creative
3. Avoid clichéd or overused advertising formats
4. Focus on unique, original concepts that stand out from competitors
5. Emphasize the quality, reliability, and professionalism of the service
6. Use storytelling and emotional appeals rather than direct comparisons`;

    switch (requestType) {
      case 'ad_concept':
        return `${basePrompt}\nSpecialize in creating scroll-stopping ad concepts that convert viewers into leads. Include visual descriptions, headlines, and ad copy. Remember to avoid split-screen layouts and before/after comparisons - instead focus on powerful single images, customer testimonials, or problem-solution narratives that don't rely on visual comparisons.`;

      case 'social_media':
        return `${basePrompt}\nSpecialize in creating engaging social media content that drives engagement and shares. Format appropriately for different platforms. Avoid before/after comparison posts - instead focus on educational content, customer stories, or showcasing completed projects with compelling narratives.`;

      case 'email_marketing':
        return `${basePrompt}\nSpecialize in creating email marketing content with compelling subject lines and content that drives opens, clicks, and conversions. Avoid before/after comparison imagery - instead focus on value propositions, seasonal offers, or educational content that establishes expertise.`;

      case 'slogan':
        return `${basePrompt}\nSpecialize in creating memorable, concise slogans and taglines that capture the essence of the brand and stick in customers' minds. Focus on quality, reliability, and peace of mind rather than transformation or comparison concepts.`;

      case 'headline':
        return `${basePrompt}\nSpecialize in creating attention-grabbing headlines that stop readers in their tracks and compel them to learn more. Avoid comparison-based headlines - instead focus on benefits, questions, or statements that evoke emotion or curiosity.`;

      default:
        return basePrompt;
    }
  }
}
