/**
 * RAG Manager Component
 * 
 * This component provides a UI for managing RAG (Retrieval-Augmented Generation)
 * functionality including website scraping, knowledge base management, and upsert operations.
 */

import React, { useState, useEffect } from 'react';
import { RAGAgent } from '../lib/agents/rag-agent';
import { PineconeService } from '../lib/knowledge/pinecone-service';
import { Settings } from '../types';

interface RAGManagerProps {
  onClose: () => void;
  settings: Settings;
}

interface ScrapingResults {
  url: string;
  pageCount: number;
  status: 'success' | 'error';
  message?: string;
}

interface CompanyInfo {
  name: string;
  description: string;
  services: string[];
  contactInfo: {
    phone: string;
    email: string;
    address: string;
  };
  uniqueSellingPoints: string[];
}

export const RAGManager: React.FC<RAGManagerProps> = ({ onClose, settings }) => {
  const [activeTab, setActiveTab] = useState<'scrape' | 'company' | 'status'>('scrape');
  const [ragAgent, setRagAgent] = useState<RAGAgent | null>(null);
  const [isInitializing, setIsInitializing] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Website scraping state
  const [websiteUrl, setWebsiteUrl] = useState('');
  const [followLinks, setFollowLinks] = useState(true);
  const [extractPdfText, setExtractPdfText] = useState(false);
  const [includeImages, setIncludeImages] = useState(false);
  const [maxPages, setMaxPages] = useState(10);
  const [isScraping, setIsScraping] = useState(false);
  const [scrapingResults, setScrapingResults] = useState<ScrapingResults | null>(null);

  // Company info state
  const [companyInfo, setCompanyInfo] = useState<CompanyInfo>({
    name: 'Phoenix Roofing and Repair',
    description: '',
    services: [],
    contactInfo: {
      phone: '(602) 837-ROOF',
      email: '',
      address: '532 E. Maryland Suite F, Phoenix, AZ 85012'
    },
    uniqueSellingPoints: []
  });
  const [isUpserting, setIsUpserting] = useState(false);

  // Status state
  const [knowledgeBaseStats, setKnowledgeBaseStats] = useState<any>(null);

  useEffect(() => {
    initializeRAGAgent();
  }, []);

  const initializeRAGAgent = async () => {
    try {
      setIsInitializing(true);
      setError(null);

      // Validate required settings
      if (!settings.pinecone?.apiKey || !settings.pinecone?.environment || !settings.pinecone?.indexName) {
        throw new Error('Pinecone configuration is incomplete. Please configure Pinecone settings first.');
      }

      if (!settings.apiKeys?.openRouter) {
        throw new Error('OpenRouter API key is required for embeddings. Please configure it in settings.');
      }

      // Create Pinecone service
      const pineconeService = new PineconeService({
        apiKey: settings.pinecone.apiKey,
        environment: settings.pinecone.environment,
        indexName: settings.pinecone.indexName,
        host: settings.pinecone.host,
        metric: settings.pinecone.metric || 'cosine',
        dimensions: settings.pinecone.dimensions || 1536,
        embeddingModel: settings.pinecone.embeddingModel || 'text-embedding-ada-002',
        openAIApiKey: settings.apiKeys.openRouter
      });

      // Create RAG agent
      const agent = new RAGAgent(
        {
          id: 'rag-agent',
          name: 'RAG Agent',
          description: 'Retrieval-Augmented Generation agent',
          systemPrompt: 'You are a helpful assistant with access to a knowledge base.',
          model: settings.defaultModel,
          enabled: true,
          mcpServerId: 'context7',
          icon: 'database'
        },
        pineconeService
      );

      // Initialize RAG agent
      await agent.initialize();

      setRagAgent(agent);
      setIsInitialized(true);
      
      // Load knowledge base stats
      await loadKnowledgeBaseStats(pineconeService);

    } catch (err) {
      console.error('Error initializing RAG agent:', err);
      setError(err instanceof Error ? err.message : 'Failed to initialize RAG agent');
    } finally {
      setIsInitializing(false);
    }
  };

  const loadKnowledgeBaseStats = async (pineconeService: PineconeService) => {
    try {
      const stats = await pineconeService.getIndexStats();
      setKnowledgeBaseStats(stats);
    } catch (err) {
      console.error('Error loading knowledge base stats:', err);
    }
  };

  const handleScrapeWebsite = async () => {
    if (!ragAgent || !websiteUrl.trim()) {
      setError('Please enter a valid website URL');
      return;
    }

    try {
      setIsScraping(true);
      setError(null);
      setScrapingResults(null);

      const pageCount = await ragAgent.scrapeWebsite(websiteUrl, {
        followLinks,
        extractPdfText,
        includeImages,
        maxPages
      });

      setScrapingResults({
        url: websiteUrl,
        pageCount,
        status: 'success'
      });

      // Refresh knowledge base stats
      if (ragAgent) {
        const pineconeService = (ragAgent as any).knowledgeBase.pineconeService;
        await loadKnowledgeBaseStats(pineconeService);
      }

    } catch (err) {
      console.error('Error scraping website:', err);
      setScrapingResults({
        url: websiteUrl,
        pageCount: 0,
        status: 'error',
        message: err instanceof Error ? err.message : 'Unknown error'
      });
    } finally {
      setIsScraping(false);
    }
  };

  const handleUpsertCompanyInfo = async () => {
    if (!ragAgent) {
      setError('RAG agent not initialized');
      return;
    }

    try {
      setIsUpserting(true);
      setError(null);

      await ragAgent.addCompanyInfo(companyInfo);

      // Refresh knowledge base stats
      const pineconeService = (ragAgent as any).knowledgeBase.pineconeService;
      await loadKnowledgeBaseStats(pineconeService);

      alert('Company information added to knowledge base successfully!');

    } catch (err) {
      console.error('Error upserting company info:', err);
      setError(err instanceof Error ? err.message : 'Failed to add company information');
    } finally {
      setIsUpserting(false);
    }
  };

  const addService = () => {
    setCompanyInfo(prev => ({
      ...prev,
      services: [...prev.services, '']
    }));
  };

  const updateService = (index: number, value: string) => {
    setCompanyInfo(prev => ({
      ...prev,
      services: prev.services.map((service, i) => i === index ? value : service)
    }));
  };

  const removeService = (index: number) => {
    setCompanyInfo(prev => ({
      ...prev,
      services: prev.services.filter((_, i) => i !== index)
    }));
  };

  const addUSP = () => {
    setCompanyInfo(prev => ({
      ...prev,
      uniqueSellingPoints: [...prev.uniqueSellingPoints, '']
    }));
  };

  const updateUSP = (index: number, value: string) => {
    setCompanyInfo(prev => ({
      ...prev,
      uniqueSellingPoints: prev.uniqueSellingPoints.map((usp, i) => i === index ? value : usp)
    }));
  };

  const removeUSP = (index: number) => {
    setCompanyInfo(prev => ({
      ...prev,
      uniqueSellingPoints: prev.uniqueSellingPoints.filter((_, i) => i !== index)
    }));
  };

  if (isInitializing) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="ml-2">Initializing RAG Agent...</span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="bg-green-600 text-white px-6 py-4 flex justify-between items-center">
          <h2 className="text-xl font-semibold">RAG Knowledge Base Manager</h2>
          <button
            onClick={onClose}
            className="text-white hover:text-gray-200 text-2xl"
          >
            ×
          </button>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {[
              { id: 'scrape', label: 'Website Scraping', icon: '🌐' },
              { id: 'company', label: 'Company Info', icon: '🏢' },
              { id: 'status', label: 'Knowledge Base Status', icon: '📊' }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-green-500 text-green-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <span className="mr-2">{tab.icon}</span>
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
          {error && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
              {error}
            </div>
          )}

          {!isInitialized && !isInitializing && (
            <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded mb-4">
              RAG agent is not initialized. Please check your Pinecone and OpenRouter API configuration.
            </div>
          )}

          {/* Website Scraping Tab */}
          {activeTab === 'scrape' && (
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold mb-4">Scrape Website Content</h3>
                <p className="text-gray-600 mb-4">
                  Scrape website content and add it to the knowledge base for the RAG agent to use.
                </p>

                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Website URL *
                    </label>
                    <input
                      type="url"
                      value={websiteUrl}
                      onChange={(e) => setWebsiteUrl(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md"
                      placeholder="https://example.com"
                      disabled={isScraping || !isInitialized}
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Max Pages
                      </label>
                      <input
                        type="number"
                        value={maxPages}
                        onChange={(e) => setMaxPages(parseInt(e.target.value))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md"
                        min="1"
                        max="50"
                        disabled={isScraping || !isInitialized}
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={followLinks}
                        onChange={(e) => setFollowLinks(e.target.checked)}
                        className="mr-2"
                        disabled={isScraping || !isInitialized}
                      />
                      <span className="text-sm text-gray-700">Follow internal links</span>
                    </label>
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={extractPdfText}
                        onChange={(e) => setExtractPdfText(e.target.checked)}
                        className="mr-2"
                        disabled={isScraping || !isInitialized}
                      />
                      <span className="text-sm text-gray-700">Extract PDF text</span>
                    </label>
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={includeImages}
                        onChange={(e) => setIncludeImages(e.target.checked)}
                        className="mr-2"
                        disabled={isScraping || !isInitialized}
                      />
                      <span className="text-sm text-gray-700">Include image descriptions</span>
                    </label>
                  </div>

                  <button
                    onClick={handleScrapeWebsite}
                    disabled={isScraping || !isInitialized || !websiteUrl.trim()}
                    className="w-full bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
                  >
                    {isScraping ? (
                      <span className="flex items-center justify-center">
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Scraping Website...
                      </span>
                    ) : (
                      'Scrape Website'
                    )}
                  </button>
                </div>

                {scrapingResults && (
                  <div className={`mt-4 p-4 rounded-md ${
                    scrapingResults.status === 'success' 
                      ? 'bg-green-100 border border-green-400 text-green-700'
                      : 'bg-red-100 border border-red-400 text-red-700'
                  }`}>
                    <h4 className="font-semibold">Scraping Results</h4>
                    <p>URL: {scrapingResults.url}</p>
                    <p>Pages processed: {scrapingResults.pageCount}</p>
                    <p>Status: {scrapingResults.status}</p>
                    {scrapingResults.message && <p>Message: {scrapingResults.message}</p>}
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Company Info Tab */}
          {activeTab === 'company' && (
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold mb-4">Company Information</h3>
                <p className="text-gray-600 mb-4">
                  Add structured company information to the knowledge base.
                </p>

                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Company Name
                    </label>
                    <input
                      type="text"
                      value={companyInfo.name}
                      onChange={(e) => setCompanyInfo(prev => ({ ...prev, name: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md"
                      disabled={isUpserting || !isInitialized}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Company Description
                    </label>
                    <textarea
                      value={companyInfo.description}
                      onChange={(e) => setCompanyInfo(prev => ({ ...prev, description: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md"
                      rows={3}
                      disabled={isUpserting || !isInitialized}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Services
                    </label>
                    {companyInfo.services.map((service, index) => (
                      <div key={index} className="flex items-center space-x-2 mb-2">
                        <input
                          type="text"
                          value={service}
                          onChange={(e) => updateService(index, e.target.value)}
                          className="flex-1 px-3 py-2 border border-gray-300 rounded-md"
                          placeholder="Service name"
                          disabled={isUpserting || !isInitialized}
                        />
                        <button
                          onClick={() => removeService(index)}
                          className="text-red-600 hover:text-red-800"
                          disabled={isUpserting || !isInitialized}
                        >
                          🗑️
                        </button>
                      </div>
                    ))}
                    <button
                      onClick={addService}
                      className="text-blue-600 hover:text-blue-800 text-sm"
                      disabled={isUpserting || !isInitialized}
                    >
                      + Add Service
                    </button>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Phone
                      </label>
                      <input
                        type="tel"
                        value={companyInfo.contactInfo.phone}
                        onChange={(e) => setCompanyInfo(prev => ({
                          ...prev,
                          contactInfo: { ...prev.contactInfo, phone: e.target.value }
                        }))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md"
                        disabled={isUpserting || !isInitialized}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Email
                      </label>
                      <input
                        type="email"
                        value={companyInfo.contactInfo.email}
                        onChange={(e) => setCompanyInfo(prev => ({
                          ...prev,
                          contactInfo: { ...prev.contactInfo, email: e.target.value }
                        }))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md"
                        disabled={isUpserting || !isInitialized}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Address
                      </label>
                      <input
                        type="text"
                        value={companyInfo.contactInfo.address}
                        onChange={(e) => setCompanyInfo(prev => ({
                          ...prev,
                          contactInfo: { ...prev.contactInfo, address: e.target.value }
                        }))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md"
                        disabled={isUpserting || !isInitialized}
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Unique Selling Points
                    </label>
                    {companyInfo.uniqueSellingPoints.map((usp, index) => (
                      <div key={index} className="flex items-center space-x-2 mb-2">
                        <input
                          type="text"
                          value={usp}
                          onChange={(e) => updateUSP(index, e.target.value)}
                          className="flex-1 px-3 py-2 border border-gray-300 rounded-md"
                          placeholder="Unique selling point"
                          disabled={isUpserting || !isInitialized}
                        />
                        <button
                          onClick={() => removeUSP(index)}
                          className="text-red-600 hover:text-red-800"
                          disabled={isUpserting || !isInitialized}
                        >
                          🗑️
                        </button>
                      </div>
                    ))}
                    <button
                      onClick={addUSP}
                      className="text-blue-600 hover:text-blue-800 text-sm"
                      disabled={isUpserting || !isInitialized}
                    >
                      + Add USP
                    </button>
                  </div>

                  <button
                    onClick={handleUpsertCompanyInfo}
                    disabled={isUpserting || !isInitialized}
                    className="w-full bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
                  >
                    {isUpserting ? (
                      <span className="flex items-center justify-center">
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Adding to Knowledge Base...
                      </span>
                    ) : (
                      'Add to Knowledge Base'
                    )}
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Status Tab */}
          {activeTab === 'status' && (
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold mb-4">Knowledge Base Status</h3>
                
                {knowledgeBaseStats ? (
                  <div className="bg-gray-50 rounded-lg p-4">
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <span className="text-gray-600">Total Vectors:</span>
                        <span className="ml-2 font-medium">{knowledgeBaseStats.totalVectorCount || 0}</span>
                      </div>
                      <div>
                        <span className="text-gray-600">Dimensions:</span>
                        <span className="ml-2 font-medium">{knowledgeBaseStats.dimension || 'N/A'}</span>
                      </div>
                      <div>
                        <span className="text-gray-600">Index Fullness:</span>
                        <span className="ml-2 font-medium">
                          {knowledgeBaseStats.indexFullness ? `${(knowledgeBaseStats.indexFullness * 100).toFixed(2)}%` : 'N/A'}
                        </span>
                      </div>
                      <div>
                        <span className="text-gray-600">Status:</span>
                        <span className="ml-2 font-medium text-green-600">
                          {isInitialized ? 'Connected' : 'Disconnected'}
                        </span>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    {isInitialized ? 'Loading knowledge base statistics...' : 'Knowledge base not connected'}
                  </div>
                )}

                <div className="mt-6">
                  <button
                    onClick={() => ragAgent && loadKnowledgeBaseStats((ragAgent as any).knowledgeBase.pineconeService)}
                    disabled={!isInitialized}
                    className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 disabled:bg-gray-400"
                  >
                    Refresh Stats
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
