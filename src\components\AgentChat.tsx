"use client";

import React, { useState, useEffect, useRef } from "react";
import { initializeAgentService, processAgentMessage, getAvailableAgents } from "../lib/agents/agent-service";
import { AgentSettings } from "../types";
import { MCPToolRegistry } from "../lib/mcp/mcp-tool-registry";

interface AgentChatProps {
  settings?: {
    agents?: AgentSettings[];
  };
}

/**
 * AgentChat component that provides a chat interface for agents
 */
export function AgentChat({ settings }: AgentChatProps) {
  const [input, setInput] = useState("");
  const [messages, setMessages] = useState<any[]>([]);
  const [availableAgents, setAvailableAgents] = useState<AgentSettings[]>([]);
  const [currentAgentId, setCurrentAgentId] = useState<string | undefined>();
  const [isProcessing, setIsProcessing] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Initialize agents
  useEffect(() => {
    const init = async () => {
      try {
        // Initialize the MCP Tool Registry with Context7
        const mcpToolRegistry = MCPToolRegistry.getInstance();
        await mcpToolRegistry.initialize(["context7"]);

        // Initialize the agent service
        await initializeAgentService(settings?.agents);

        // Get available agents
        const agents = getAvailableAgents();
        setAvailableAgents(agents);

        // Set the current agent to the first enabled agent
        const enabledAgent = agents.find(agent => agent.enabled);
        if (enabledAgent) {
          setCurrentAgentId(enabledAgent.id);
        }

        console.log("Agents initialized successfully with Context7");
      } catch (error) {
        console.error("Error initializing agents:", error);
      }
    };

    init();
  }, [settings]);

  // Scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  // Handle sending a message
  const handleSendMessage = async () => {
    if (!input.trim() || isProcessing) return;

    // Add user message to the chat
    const userMessage = {
      id: Date.now().toString(),
      content: input,
      role: "user",
    };
    setMessages(prev => [...prev, userMessage]);
    setInput("");
    setIsProcessing(true);

    try {
      // Process the message with the selected agent
      const { response, agentId } = await processAgentMessage(input, currentAgentId);

      // Get the agent name
      const agent = availableAgents.find(a => a.id === agentId);
      const agentName = agent?.name || "AI Assistant";

      // Add assistant message to the chat
      const assistantMessage = {
        id: (Date.now() + 1).toString(),
        content: response,
        role: "assistant",
        agentId,
        agentName,
      };
      setMessages(prev => [...prev, assistantMessage]);
    } catch (error) {
      console.error("Error processing message:", error);

      // Add error message to the chat
      const errorMessage = {
        id: (Date.now() + 1).toString(),
        content: `Error: ${error instanceof Error ? error.message : String(error)}`,
        role: "assistant",
        isError: true,
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsProcessing(false);
    }
  };

  // Handle key press
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // Handle agent change
  const handleAgentChange = (agentId: string) => {
    setCurrentAgentId(agentId);
  };

  return (
    <div className="flex flex-col h-full bg-gray-900 text-white">
      {/* Header */}
      <div className="p-4 border-b border-gray-700">
        <div className="flex items-center justify-between">
          <h1 className="text-xl font-bold">Agent Chat</h1>

          {/* Agent selector */}
          {availableAgents.length > 0 && (
            <select
              value={currentAgentId}
              onChange={(e) => handleAgentChange(e.target.value)}
              className="bg-gray-800 text-white border border-gray-700 rounded px-3 py-1"
            >
              {availableAgents.map((agent) => (
                <option key={agent.id} value={agent.id}>
                  {agent.name}
                </option>
              ))}
            </select>
          )}
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.length === 0 ? (
          <div className="flex items-center justify-center h-full text-gray-400">
            <p>Send a message to start chatting with the agent.</p>
          </div>
        ) : (
          messages.map((message) => (
            <div
              key={message.id}
              className={`max-w-3xl mx-auto ${
                message.role === "user" ? "ml-auto" : "mr-auto"
              }`}
            >
              <div
                className={`p-3 rounded-lg ${
                  message.role === "user"
                    ? "bg-blue-600 text-white"
                    : message.isError
                    ? "bg-red-600 text-white"
                    : "bg-gray-700 text-white"
                }`}
              >
                {message.role === "assistant" && message.agentName && (
                  <div className="text-xs text-gray-300 mb-1">
                    {message.agentName}
                  </div>
                )}
                <div className="whitespace-pre-wrap">{message.content}</div>
              </div>
            </div>
          ))
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* Input */}
      <div className="p-4 border-t border-gray-700">
        <div className="flex items-center">
          <textarea
            value={input}
            onChange={(e) => setInput(e.target.value)}
            onKeyDown={handleKeyPress}
            placeholder="Type your message..."
            className="flex-1 bg-gray-800 text-white border border-gray-700 rounded-lg p-3 resize-none"
            rows={1}
            disabled={isProcessing}
          />
          <button
            onClick={handleSendMessage}
            disabled={!input.trim() || isProcessing}
            className="ml-2 bg-blue-600 text-white px-4 py-2 rounded-lg disabled:opacity-50"
          >
            {isProcessing ? "Sending..." : "Send"}
          </button>
        </div>
      </div>
    </div>
  );
}
