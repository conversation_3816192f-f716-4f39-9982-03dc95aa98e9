import React, { useState, useRef, useEffect } from 'react';
import { Send, Paperclip, X, Plus, MessageSquare, Trash2, Edit2, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from 'lucide-react';
import { getChatCompletion, processFile, type Message as OpenAIMessage } from '../lib/openrouter';
import { Message, Conversation, Settings as SettingsType, MessageContent } from '../types';
import { processAgentMessage } from '../lib/agents/agent-service';
import { processSwarmMessage, isSwarmInitialized } from '../lib/agents/swarm-service';

interface AgentChatProps {
  settings?: SettingsType;
}

export function AgentChat({ }: AgentChatProps) {
  const [conversations, setConversations] = useState<Conversation[]>([
    {
      id: '1',
      title: 'New conversation',
      messages: [],
      timestamp: new Date()
    }
  ]);
  const [currentConversationId, setCurrentConversationId] = useState('1');
  const [input, setInput] = useState('');
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [filePreview, setFilePreview] = useState<string | null>(null);
  const [fileType, setFileType] = useState<string | null>(null);
  const [selectedModel, setSelectedModel] = useState('anthropic/claude-3.5-sonnet');
  const [currentAgentId, setCurrentAgentId] = useState<string | undefined>(undefined);
  const [useSwarm, setUseSwarm] = useState(true);
  const [editingConversationId, setEditingConversationId] = useState<string | null>(null);
  const [editingTitle, setEditingTitle] = useState('');
  const [showModelSelector, setShowModelSelector] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const currentConversation = conversations.find(c => c.id === currentConversationId)!;

  // Available models for selection
  const availableModels = [
    { id: 'anthropic/claude-3.5-sonnet', name: 'Claude 3.5 Sonnet', icon: '🧠' },
    { id: 'anthropic/claude-3-opus', name: 'Claude 3 Opus', icon: '🎓' },
    { id: 'openai/gpt-4-turbo', name: 'GPT-4 Turbo', icon: '🚀' },
    { id: 'openai/gpt-4', name: 'GPT-4', icon: '🤖' },
    { id: 'openai/gpt-3.5-turbo', name: 'GPT-3.5 Turbo', icon: '⚡' },
    { id: 'meta-llama/llama-3-70b-instruct', name: 'Llama 3 70B', icon: '🦙' },
  ];

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [currentConversation.messages]);

  // Create a new conversation
  const handleNewChat = () => {
    const newConversation: Conversation = {
      id: Date.now().toString(),
      title: 'New conversation',
      messages: [],
      timestamp: new Date()
    };
    setConversations(prev => [newConversation, ...prev]);
    setCurrentConversationId(newConversation.id);
    setInput('');
    setUploadedFile(null);
    setFilePreview(null);
    setFileType(null);
  };

  // Delete a conversation
  const handleDeleteConversation = (id: string) => {
    if (conversations.length === 1) {
      // If it's the last conversation, create a new one
      handleNewChat();
    } else {
      setConversations(prev => prev.filter(c => c.id !== id));
      if (currentConversationId === id) {
        // Switch to another conversation if deleting the current one
        const remainingConversations = conversations.filter(c => c.id !== id);
        setCurrentConversationId(remainingConversations[0].id);
      }
    }
  };

  // Start editing a conversation title
  const handleStartEdit = (id: string, title: string) => {
    setEditingConversationId(id);
    setEditingTitle(title);
  };

  // Save edited conversation title
  const handleSaveEdit = () => {
    if (editingConversationId && editingTitle.trim()) {
      setConversations(prev => prev.map(conv => 
        conv.id === editingConversationId 
          ? { ...conv, title: editingTitle.trim() }
          : conv
      ));
    }
    setEditingConversationId(null);
    setEditingTitle('');
  };

  // Cancel editing
  const handleCancelEdit = () => {
    setEditingConversationId(null);
    setEditingTitle('');
  };

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setUploadedFile(file);
      setFileType(file.type);

      if (file.type.startsWith('image/')) {
        const reader = new FileReader();
        reader.onload = (event) => {
          if (event.target?.result) {
            const dataUrl = event.target.result as string;
            setFilePreview(dataUrl);
          }
        };
        reader.readAsDataURL(file);
      } else {
        setFilePreview(null);
      }
    }
  };

  const removeFile = () => {
    setUploadedFile(null);
    setFilePreview(null);
    setFileType(null);
  };

  const fallbackToAgentOrModel = async (userMessageText: string, messages: OpenAIMessage[], assistantMessageId: string): Promise<void> => {
    if (currentAgentId) {
      try {
        const agentResponse = await processAgentMessage(userMessageText, currentAgentId);

        setConversations(prev => prev.map(conv => {
          if (conv.id === currentConversationId) {
            return {
              ...conv,
              messages: conv.messages.map(msg => {
                if (msg.id === assistantMessageId) {
                  const updatedMessage: Message = {
                    id: msg.id,
                    role: msg.role,
                    content: agentResponse.response,
                    timestamp: msg.timestamp,
                    processing: false,
                    agentId: currentAgentId,
                    agentName: 'Agent'
                  };
                  return updatedMessage;
                }
                return msg;
              })
            };
          }
          return conv;
        }));
      } catch (error) {
        console.error('Error processing with agent, falling back to model:', error);
        await processWithModel(messages, assistantMessageId);
      }
    } else {
      await processWithModel(messages, assistantMessageId);
    }
  };

  const processWithModel = async (messages: OpenAIMessage[], assistantMessageId: string) => {
    try {
      const stream = await getChatCompletion(messages, selectedModel);
      let fullResponse = '';
      
      for await (const chunk of stream) {
        const content = chunk.choices[0]?.delta?.content || '';
        fullResponse += content;
        
        // Update message with accumulated response
        setConversations(prev => prev.map(conv => {
          if (conv.id === currentConversationId) {
            return {
              ...conv,
              messages: conv.messages.map(msg => {
                if (msg.id === assistantMessageId) {
                  const updatedMessage: Message = {
                    id: msg.id,
                    role: msg.role,
                    content: fullResponse,
                    timestamp: msg.timestamp,
                    processing: true,
                    agentId: 'model',
                    agentName: selectedModel.split('/')[1] || selectedModel
                  };
                  return updatedMessage;
                }
                return msg;
              })
            };
          }
          return conv;
        }));
      }
      
      // Mark as complete
      setConversations(prev => prev.map(conv => {
        if (conv.id === currentConversationId) {
          return {
            ...conv,
            messages: conv.messages.map(msg => {
              if (msg.id === assistantMessageId) {
                const updatedMessage: Message = {
                  id: msg.id,
                  role: msg.role,
                  content: fullResponse,
                  timestamp: msg.timestamp,
                  processing: false,
                  agentId: 'model',
                  agentName: selectedModel.split('/')[1] || selectedModel
                };
                return updatedMessage;
              }
              return msg;
            })
          };
        }
        return conv;
      }));
    } catch (error) {
      console.error('Error processing with model:', error);
      
      setConversations(prev => prev.map(conv => {
        if (conv.id === currentConversationId) {
          return {
            ...conv,
            messages: conv.messages.map(msg => {
              if (msg.id === assistantMessageId) {
                const updatedMessage: Message = {
                  id: msg.id,
                  role: msg.role,
                  content: 'I apologize, but I encountered an error processing your request. Please try again.',
                  timestamp: msg.timestamp,
                  processing: false,
                  agentId: 'model',
                  agentName: 'Error'
                };
                return updatedMessage;
              }
              return msg;
            })
          };
        }
        return conv;
      }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!input.trim() && !uploadedFile) return;

    let userMessageContent: string | MessageContent[] = input;
    
    if (uploadedFile) {
      if (fileType?.startsWith('image/')) {
        const reader = new FileReader();
        const base64Promise = new Promise<string>((resolve) => {
          reader.onloadend = () => resolve(reader.result as string);
          reader.readAsDataURL(uploadedFile);
        });
        const base64 = await base64Promise;
        
        userMessageContent = [
          { type: 'text', content: input || "What's in this image?" },
          { type: 'image', content: base64 }
        ];
      } else {
        userMessageContent = [
          { type: 'text', content: input || `Analyze this file: ${uploadedFile.name}` },
          { type: 'file', content: uploadedFile.name }
        ];
      }
    }

    const userMessage: Message = {
      id: Date.now().toString(),
      role: 'user',
      content: userMessageContent,
      timestamp: new Date(),
      processing: false
    };

    const assistantMessage: Message = {
      id: (Date.now() + 1).toString(),
      role: 'assistant',
      content: '',
      timestamp: new Date(),
      processing: true
    };

    // Update conversation title if it's the first message
    const shouldUpdateTitle = currentConversation.messages.length === 0 && input.trim();
    
    setConversations(prev => prev.map(conv => {
      if (conv.id === currentConversationId) {
        return {
          ...conv,
          messages: [...conv.messages, userMessage, assistantMessage],
          title: shouldUpdateTitle ? input.trim().slice(0, 50) : conv.title
        };
      }
      return conv;
    }));

    setInput('');
    setUploadedFile(null);
    setFilePreview(null);
    setFileType(null);

    // Process the message
    let userMessageText = input;
    if (uploadedFile && !fileType?.startsWith('image/')) {
      try {
        const response = await processFile(uploadedFile);
        userMessageText = `${input}\n\nFile content:\n${response}`;
      } catch (error) {
        console.error('Error processing file:', error);
        userMessageText = `${input}\n\nError processing file: ${error}`;
      }
    }

    const messages: OpenAIMessage[] = currentConversation.messages
      .filter(msg => !msg.processing)
      .map(msg => ({
        role: msg.role as 'user' | 'assistant',
        content: typeof msg.content === 'string' 
          ? msg.content 
          : msg.content.map(item => 
              item.type === 'text' ? item.content : ''
            ).join('\n')
      }));

    messages.push({
      role: 'user',
      content: userMessageText
    });

    if (useSwarm && isSwarmInitialized()) {
      try {
        const swarmResponse = await processSwarmMessage(userMessageText);
        setConversations(prev => prev.map(conv => {
          if (conv.id === currentConversationId) {
            return {
              ...conv,
              messages: conv.messages.map(msg => {
                if (msg.id === assistantMessage.id) {
                  const updatedMessage: Message = {
                    id: msg.id,
                    role: msg.role,
                    content: swarmResponse,
                    timestamp: msg.timestamp,
                    processing: false,
                    agentId: 'swarm',
                    agentName: 'Swarm'
                  };
                  return updatedMessage;
                }
                return msg;
              })
            };
          }
          return conv;
        }));
      } catch (error) {
        console.error('Error using swarm, falling back to agent or model:', error);
        await fallbackToAgentOrModel(userMessageText, messages, assistantMessage.id);
      }
    } else {
      await fallbackToAgentOrModel(userMessageText, messages, assistantMessage.id);
    }
  };

  const renderMessageContent = (content: string | MessageContent[]) => {
    if (typeof content === 'string') {
      return <div className="text-white">{content}</div>;
    }

    return content.map((item, index) => {
      if (item.type === 'text') {
        return <div key={index} className="text-white">{item.content}</div>;
      } else if (item.type === 'image') {
        return (
          <img
            key={index}
            src={item.content}
            alt="User uploaded"
            className="max-w-xs rounded-lg border border-white/10"
          />
        );
      } else if (item.type === 'file') {
        return (
          <div key={index} className="flex items-center gap-2 p-2 bg-white/5 rounded border border-white/10">
            <Paperclip className="w-4 h-4 text-neon-green" />
            <span className="text-sm text-white">{item.content}</span>
          </div>
        );
      }
      return null;
    });
  };

  return (
    <div className="flex h-screen bg-base-black text-white">
      {/* Sidebar */}
      <div className="w-80 bg-surface-gray border-r border-white/10 flex flex-col">
        {/* Sidebar Header */}
        <div className="p-4 border-b border-white/10">
          <button
            onClick={handleNewChat}
            className="w-full flex items-center justify-center gap-2 py-3 px-4 bg-neon-green 
                     text-black font-medium rounded-xl hover:bg-neon-green/90 transition-colors"
          >
            <Plus className="w-5 h-5" />
            New chat
          </button>
        </div>

        {/* Conversations List */}
        <div className="flex-1 overflow-y-auto p-4">
          <div className="space-y-2">
            {conversations.map((conv) => (
              <div
                key={conv.id}
                className={`group relative rounded-lg transition-colors cursor-pointer
                          ${conv.id === currentConversationId 
                            ? 'bg-white/10' 
                            : 'hover:bg-white/5'}`}
              >
                {editingConversationId === conv.id ? (
                  <div className="flex items-center gap-2 p-3">
                    <input
                      type="text"
                      value={editingTitle}
                      onChange={(e) => setEditingTitle(e.target.value)}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') handleSaveEdit();
                        if (e.key === 'Escape') handleCancelEdit();
                      }}
                      className="flex-1 bg-white/10 border border-white/20 rounded px-2 py-1 
                               text-sm text-white focus:outline-none focus:border-neon-green"
                      autoFocus
                    />
                    <button
                      onClick={handleSaveEdit}
                      className="p-1 hover:bg-white/10 rounded"
                    >
                      ✓
                    </button>
                    <button
                      onClick={handleCancelEdit}
                      className="p-1 hover:bg-white/10 rounded"
                    >
                      ✗
                    </button>
                  </div>
                ) : (
                  <div
                    onClick={() => setCurrentConversationId(conv.id)}
                    className="flex items-center gap-3 p-3"
                  >
                    <MessageSquare className="w-4 h-4 text-gray-400" />
                    <div className="flex-1 min-w-0">
                      <h3 className="text-sm font-medium text-white truncate">
                        {conv.title}
                      </h3>
                      <p className="text-xs text-gray-400">
                        {conv.messages.length} messages
                      </p>
                    </div>
                    <div className="flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleStartEdit(conv.id, conv.title);
                        }}
                        className="p-1 hover:bg-white/10 rounded"
                      >
                        <Edit2 className="w-3 h-3" />
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDeleteConversation(conv.id);
                        }}
                        className="p-1 hover:bg-white/10 rounded"
                      >
                        <Trash2 className="w-3 h-3" />
                      </button>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Model Selector and Settings */}
        <div className="p-4 border-t border-white/10 space-y-2">
          {/* Model Selector Dropdown */}
          <div className="relative">
            <button
              onClick={() => setShowModelSelector(!showModelSelector)}
              className="w-full flex items-center justify-between p-3 bg-white/5 rounded-lg 
                       hover:bg-white/10 transition-colors"
            >
              <div className="flex items-center gap-2">
                <Brain className="w-4 h-4 text-neon-green" />
                <span className="text-sm">
                  {availableModels.find(m => m.id === selectedModel)?.name || 'Select Model'}
                </span>
              </div>
              <div className={`transform transition-transform ${showModelSelector ? 'rotate-180' : ''}`}>
                ▼
              </div>
            </button>
            
            {showModelSelector && (
              <div className="absolute bottom-full left-0 right-0 mb-2 bg-surface-gray 
                            border border-white/10 rounded-lg shadow-xl overflow-hidden">
                {availableModels.map((model) => (
                  <button
                    key={model.id}
                    onClick={() => {
                      setSelectedModel(model.id);
                      setShowModelSelector(false);
                    }}
                    className={`w-full flex items-center gap-3 p-3 hover:bg-white/10 
                              transition-colors text-left
                              ${model.id === selectedModel ? 'bg-white/10' : ''}`}
                  >
                    <span className="text-lg">{model.icon}</span>
                    <span className="text-sm">{model.name}</span>
                  </button>
                ))}
              </div>
            )}
          </div>

          {/* Agent Toggle */}
          <div className="flex items-center justify-between p-3 bg-white/5 rounded-lg">
            <div className="flex items-center gap-2">
              <Bot className="w-4 h-4 text-neon-green" />
              <span className="text-sm">Use Swarm Agent</span>
            </div>
            <button
              onClick={() => setUseSwarm(!useSwarm)}
              className={`w-10 h-6 rounded-full transition-colors relative
                        ${useSwarm ? 'bg-neon-green' : 'bg-white/20'}`}
            >
              <div className={`absolute top-1 w-4 h-4 bg-white rounded-full transition-transform
                            ${useSwarm ? 'translate-x-5' : 'translate-x-1'}`} />
            </button>
          </div>

          {/* Settings Button */}
          <button
            onClick={() => setShowSettings(true)}
            className="w-full flex items-center gap-2 p-3 bg-white/5 rounded-lg 
                     hover:bg-white/10 transition-colors"
          >
            <Settings className="w-4 h-4 text-gray-400" />
            <span className="text-sm">Settings</span>
          </button>
        </div>
      </div>

      {/* Main Chat Area */}
      <div className="flex-1 flex flex-col">
        {/* Chat Header */}
        <div className="p-4 border-b border-white/10">
          <h2 className="text-lg font-medium">{currentConversation.title}</h2>
          <p className="text-sm text-gray-400">
            {useSwarm ? 'Swarm Agent' : availableModels.find(m => m.id === selectedModel)?.name}
          </p>
        </div>

        {/* Messages Area */}
        <div className="flex-1 overflow-y-auto p-6">
          {currentConversation.messages.map((message) => (
            <div key={message.id} className={`mb-6 ${message.role === 'user' ? 'text-right' : 'text-left'}`}>
              <div className={`inline-block max-w-3xl ${
                message.role === 'user' 
                  ? 'bg-neon-green/20 text-neon-green' 
                  : 'bg-white/5 text-white'
              } rounded-xl p-4`}>
                {message.agentName && (
                  <div className="text-xs text-gray-400 mb-2">
                    {message.agentName}
                  </div>
                )}
                {renderMessageContent(message.content)}
                {message.processing && (
                  <div className="flex gap-1 mt-2">
                    <span className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" />
                    <span className="w-2 h-2 bg-gray-400 rounded-full animate-bounce [animation-delay:0.2s]" />
                    <span className="w-2 h-2 bg-gray-400 rounded-full animate-bounce [animation-delay:0.4s]" />
                  </div>
                )}
              </div>
            </div>
          ))}
          <div ref={messagesEndRef} />
        </div>

        {/* Chat Input */}
        <form onSubmit={handleSubmit} className="border-t border-white/10 p-4 bg-base-black">
          {uploadedFile && (
            <div className="mb-4 relative inline-block">
              {filePreview ? (
                <img
                  src={filePreview}
                  alt="Preview"
                  className="rounded-lg border border-white/10"
                  style={{
                    maxWidth: '300px',
                    maxHeight: '200px',
                    objectFit: 'contain'
                  }}
                />
              ) : (
                <div className="p-4 rounded-lg border border-white/10 bg-white/5 text-white">
                  <div className="flex items-center gap-2">
                    <Paperclip className="w-5 h-5 text-neon-green" />
                    <span className="font-medium">{uploadedFile.name}</span>
                  </div>
                  <div className="text-sm text-gray-400 mt-1">
                    {fileType || 'Unknown type'} • {(uploadedFile.size / 1024).toFixed(2)} KB
                  </div>
                </div>
              )}
              <button
                onClick={removeFile}
                className="absolute top-2 right-2 p-1 rounded-full bg-black/50 hover:bg-black/70
                         text-white transition-colors"
              >
                <X className="w-4 h-4" />
              </button>
            </div>
          )}
          <div className="flex flex-col gap-4">
            <div className="flex gap-4">
              <input
                type="text"
                value={input}
                onChange={(e) => setInput(e.target.value)}
                placeholder="Type your message..."
                className="flex-1 bg-white/5 border border-white/10 rounded-xl px-4 py-3
                         text-white placeholder-gray-400 focus:outline-none focus:border-neon-green
                         transition-colors"
              />
              <label className="px-6 rounded-xl bg-white/5 border border-white/10 hover:bg-white/10
                               transition-colors cursor-pointer flex items-center">
                <Paperclip className="w-5 h-5 text-gray-400" />
                <input
                  type="file"
                  accept="*/*"
                  onChange={handleFileUpload}
                  className="hidden"
                />
              </label>
              <button
                type="submit"
                disabled={!input.trim() && !uploadedFile}
                className="px-6 rounded-xl bg-neon-green/20 text-neon-green hover:bg-neon-green/30
                         transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <Send className="w-5 h-5" />
              </button>
            </div>
          </div>
        </form>
      </div>

      {/* Settings Panel */}
      {showSettings && (
        <div className="fixed inset-0 bg-black/50 z-50 flex justify-end">
          <div className="w-96 bg-surface-gray h-full shadow-2xl flex flex-col">
            <div className="p-4 border-b border-white/10 flex items-center justify-between">
              <h2 className="text-lg font-medium">Settings</h2>
              <button
                onClick={() => setShowSettings(false)}
                className="p-2 hover:bg-white/10 rounded-lg transition-colors"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
            
            <div className="flex-1 overflow-y-auto p-4 space-y-6">
              {/* API Configuration */}
              <div>
                <h3 className="text-sm font-medium text-gray-400 mb-3">API Configuration</h3>
                <div className="space-y-3">
                  <div>
                    <label className="block text-sm text-gray-300 mb-1">OpenRouter API Key</label>
                    <input
                      type="password"
                      placeholder="sk-or-v1-..."
                      className="w-full bg-white/5 border border-white/10 rounded-lg px-3 py-2
                               text-white placeholder-gray-500 focus:outline-none focus:border-neon-green"
                    />
                  </div>
                </div>
              </div>

              {/* Agent Selection */}
              <div>
                <h3 className="text-sm font-medium text-gray-400 mb-3">Agent Configuration</h3>
                <div className="space-y-2">
                  <label className="flex items-center gap-3 p-3 bg-white/5 rounded-lg cursor-pointer
                                 hover:bg-white/10 transition-colors">
                    <input
                      type="radio"
                      name="agent"
                      value=""
                      checked={!currentAgentId}
                      onChange={() => setCurrentAgentId(undefined)}
                      className="text-neon-green"
                    />
                    <span className="text-sm">No Agent (Direct Model)</span>
                  </label>
                  <label className="flex items-center gap-3 p-3 bg-white/5 rounded-lg cursor-pointer
                                 hover:bg-white/10 transition-colors">
                    <input
                      type="radio"
                      name="agent"
                      value="general"
                      checked={currentAgentId === 'general'}
                      onChange={() => setCurrentAgentId('general')}
                      className="text-neon-green"
                    />
                    <span className="text-sm">General Purpose Agent</span>
                  </label>
                  <label className="flex items-center gap-3 p-3 bg-white/5 rounded-lg cursor-pointer
                                 hover:bg-white/10 transition-colors">
                    <input
                      type="radio"
                      name="agent"
                      value="coding"
                      checked={currentAgentId === 'coding'}
                      onChange={() => setCurrentAgentId('coding')}
                      className="text-neon-green"
                    />
                    <span className="text-sm">Coding Assistant Agent</span>
                  </label>
                </div>
              </div>

              {/* Theme Settings */}
              <div>
                <h3 className="text-sm font-medium text-gray-400 mb-3">Appearance</h3>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-300">Dark Mode</span>
                    <button className="w-10 h-6 bg-neon-green rounded-full relative">
                      <div className="absolute top-1 right-1 w-4 h-4 bg-white rounded-full" />
                    </button>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-300">Compact Mode</span>
                    <button className="w-10 h-6 bg-white/20 rounded-full relative">
                      <div className="absolute top-1 left-1 w-4 h-4 bg-white rounded-full" />
                    </button>
                  </div>
                </div>
              </div>

              {/* About */}
              <div>
                <h3 className="text-sm font-medium text-gray-400 mb-3">About</h3>
                <div className="text-sm text-gray-300 space-y-2">
                  <p>Chat UI v1.0.0</p>
                  <p className="text-xs text-gray-500">
                    A modern chat interface with AI agent support and multiple model integration.
                  </p>
                </div>
              </div>
            </div>

            <div className="p-4 border-t border-white/10">
              <button
                onClick={() => setShowSettings(false)}
                className="w-full py-2 px-4 bg-neon-green text-black font-medium rounded-lg
                         hover:bg-neon-green/90 transition-colors"
              >
                Save Settings
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
