import React, { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { AgentChat } from './pages/AgentChat';
import { Settings } from './pages/Settings';
import { Settings as SettingsType } from './types';
import { initializeSwarmService } from './lib/agents/swarm-service';
import { initializeVoiceAgentService } from './lib/agents/voice-agent-service';

function App() {
  const [settings, setSettings] = useState<SettingsType>({
    defaultModel: 'anthropic/claude-3-7-sonnet',
    theme: 'dark',
    apiKeys: {
      openRouter: import.meta.env.VITE_OPENROUTER_API_KEY || '',
      pinecone: import.meta.env.VITE_PINECONE_API_KEY || ''
    },
    maxTokens: 2048,
    temperature: 0.7,
    agents: [
      {
        id: 'rag-agent',
        name: 'RAG Agent',
        description: 'Retrieval-Augmented Generation agent that uses your knowledge base to provide more accurate responses.',
        systemPrompt: `You are a helpful AI assistant that uses a knowledge base to provide accurate information. When asked about company information, use the knowledge base to provide specific details.

When you need to access information from the database or perform data operations, use the available tools. These tools allow you to query databases, retrieve information, and perform other operations.

Always use the appropriate tool when you need to access external information or perform actions. Format your tool calls exactly as instructed in the tool documentation.`,
        model: 'anthropic/claude-3-7-sonnet',
        mcpServerId: 'supabase',
        enabled: true
      },
      {
        id: 'ad-creative-agent',
        name: 'Ad Creative Agent',
        description: 'Specialized in creating marketing content, ad concepts, and promotional materials using GPT-4.1.',
        systemPrompt: `You are an expert marketing and advertising creative director specializing in the roofing industry. Create compelling, professional, and effective marketing content.

When you need to access marketing data, customer information, or perform database operations, use the available tools. These tools allow you to query databases, retrieve information, and perform other operations.

Always use the appropriate tool when you need to access external information or perform actions. Format your tool calls exactly as instructed in the tool documentation.`,
        model: 'openai/gpt-4.1',
        mcpServerId: 'supabase',
        enabled: true
      },
      {
        id: 'code-agent',
        name: 'Code Agent',
        description: 'Specialized in code generation, analysis, and debugging across multiple programming languages.',
        systemPrompt: `You are a code assistant specialized in programming. Help users write, debug, and understand code across various programming languages.

You have access to powerful code context tools that allow you to search for code, get documentation, and analyze code patterns. Use these tools whenever you need to:
- Search for specific code patterns or functions
- Look up documentation for libraries or frameworks
- Find examples of code usage
- Analyze code structure

Always use the appropriate tool when you need to access code information or perform code-related actions. Format your tool calls exactly as instructed in the tool documentation.`,
        model: 'anthropic/claude-3-7-sonnet',
        mcpServerId: 'context7',
        enabled: true
      },
      {
        id: 'research-agent',
        name: 'Research Agent',
        description: 'Performs web searches and analyzes information to provide comprehensive research on any topic.',
        systemPrompt: `You are a research assistant. Help users find information on topics by searching the web and analyzing the results.

You have access to powerful research tools that allow you to access GitHub repositories, search for information, and analyze data. Use these tools whenever you need to:
- Search for information in repositories
- Access documentation
- Find examples and references
- Analyze code and data

Always use the appropriate tool when you need to access external information or perform research actions. Format your tool calls exactly as instructed in the tool documentation.`,
        model: 'anthropic/claude-3-7-sonnet',
        mcpServerId: 'github',
        enabled: true
      },
      {
        id: 'voice-receptionist',
        name: 'Phoenix Roofing Receptionist',
        description: 'Answers incoming calls for Phoenix Roofing and Repair, providing information and scheduling free estimates.',
        systemPrompt: `You are the virtual receptionist for Phoenix Roofing and Repair, located at 532 E. Maryland Suite F, Phoenix, AZ 85012.
Our phone number is (602) 837-ROOF (7663).

When answering calls:
1. Greet callers professionally with "Thank you for calling Phoenix Roofing and Repair, how may I help you today?"
2. Provide information about our roofing services including repairs, replacements, and inspections
3. Emphasize that we offer FREE estimates for all services
4. Collect caller information (name, phone number, address) when they request an estimate
5. Schedule appointments for free estimates based on availability
6. For technical questions or complex issues, offer to transfer the call to a roofing specialist
7. Always be polite, professional, and helpful

If callers ask about pricing, explain that we need to inspect the roof first to provide an accurate estimate, which is why we offer free on-site evaluations.

You have access to voice and call management tools. Use these tools when appropriate:

1. Use the transferCall tool when callers need to speak with a specialist or have urgent issues that require immediate attention. Format your tool call exactly as shown in the tool documentation.

2. Use the endCall tool only when the conversation has reached a natural conclusion and all caller needs have been addressed. Format your tool call exactly as shown in the tool documentation.

3. Use any other available tools as needed to help callers. Always format your tool calls exactly as instructed in the tool documentation.`,
        model: 'anthropic/claude-3-7-sonnet',
        mcpServerId: 'vapi',
        enabled: true
      }
    ],
    knowledgeBase: {
      companyInfo: {
        name: '',
        description: '',
      }
    }
  });

  // Load settings from localStorage
  useEffect(() => {
    const savedSettings = localStorage.getItem('ag3nt-settings');
    if (savedSettings) {
      try {
        const parsedSettings = JSON.parse(savedSettings) as SettingsType;
        setSettings(parsedSettings);
      } catch (error) {
        console.error('Error parsing saved settings:', error);
      }
    }
  }, []);

  // Initialize the swarm service and voice agent when settings change
  useEffect(() => {
    const initServices = async () => {
      if (settings.agents && settings.agents.length > 0) {
        try {
          // Initialize swarm service
          await initializeSwarmService(
            settings.agents,
            {
              allowCollaboration: true, // This could be a setting in the future
              showReasoning: true, // This could be a setting in the future
              pineconeApiKey: settings.apiKeys.pinecone,
              pineconeEnvironment: settings.knowledgeBase?.pineconeEnvironment,
              pineconeIndex: settings.knowledgeBase?.pineconeIndex,
              pineconeHost: settings.knowledgeBase?.pineconeHost,
              pineconeMetric: settings.knowledgeBase?.pineconeMetric,
              pineconeDimensions: settings.knowledgeBase?.pineconeDimensions,
              embeddingModel: settings.knowledgeBase?.embeddingModel
            }
          );
          console.log('Swarm service initialized successfully');

          // Find the voice receptionist agent
          const voiceAgent = settings.agents.find(agent => agent.id === 'voice-receptionist');

          // Initialize voice agent service if the voice agent is enabled
          if (voiceAgent && voiceAgent.enabled) {
            await initializeVoiceAgentService(voiceAgent);
            console.log('Voice agent service initialized successfully');
          }
        } catch (error) {
          console.error('Error initializing services:', error);
        }
      }
    };

    initServices();
  }, [
    settings.agents,
    settings.apiKeys.pinecone,
    settings.knowledgeBase?.pineconeEnvironment,
    settings.knowledgeBase?.pineconeIndex,
    settings.knowledgeBase?.pineconeHost,
    settings.knowledgeBase?.pineconeMetric,
    settings.knowledgeBase?.pineconeDimensions,
    settings.knowledgeBase?.embeddingModel
  ]);

  const handleSaveSettings = (newSettings: SettingsType) => {
    setSettings(newSettings);

    // Save settings to localStorage
    localStorage.setItem('ag3nt-settings', JSON.stringify(newSettings));

    // Update environment variables if API key changed
    if (newSettings.apiKeys.openRouter) {
      window.localStorage.setItem('openrouter-api-key', newSettings.apiKeys.openRouter);
    }
  };

  return (
    <div className="h-screen overflow-hidden">
      <Router>
        <Routes>
          <Route path="/" element={<AgentChat settings={settings} />} />
          <Route path="/settings" element={<Settings settings={settings} onSaveSettings={handleSaveSettings} />} />
        </Routes>
      </Router>
    </div>
  );
}

export default App;