import { NextRequest, NextResponse } from "next/server";

export async function POST(req: NextRequest) {
  try {
    // Parse request body
    const body = await req.json();
    const { 
      callId, 
      customerName, 
      phoneNumber, 
      address, 
      serviceType, 
      preferredDate, 
      preferredTime 
    } = body;
    
    // Validate request
    if (!callId || !customerName || !phoneNumber || !address || !serviceType || !preferredDate || !preferredTime) {
      return NextResponse.json(
        { error: "Invalid request: missing required fields" },
        { status: 400 }
      );
    }
    
    // In a real implementation, you would:
    // 1. Save the appointment to a database
    // 2. Send confirmation emails/SMS
    // 3. Update the call record
    
    // For now, we'll just log the appointment
    console.log('Appointment scheduled:', {
      callId,
      customerName,
      phoneNumber,
      address,
      serviceType,
      preferredDate,
      preferredTime
    });
    
    // Return success response
    return NextResponse.json({
      success: true,
      message: `Appointment scheduled successfully for ${customerName} on ${preferredDate} at ${preferredTime}.`,
      appointmentId: `appt_${Date.now()}` // Generate a mock appointment ID
    });
  } catch (error: any) {
    console.error("Error scheduling appointment:", error);
    
    return NextResponse.json(
      { error: error.message || "An error occurred while scheduling the appointment" },
      { status: 500 }
    );
  }
}
