/**
 * Comprehensive MCP Types
 * 
 * This module defines all types used in the MCP integration framework.
 */

/**
 * MCP Tool interface
 */
export interface MCPTool {
  name: string;
  description: string;
  serverId: string;
  category?: string;
  tags?: string[];
  priority?: number;
  deprecated?: boolean;
}

/**
 * MCP Tool Parameter Info
 */
export interface MCPToolParameterInfo {
  type: string;
  description: string;
  required?: boolean;
  default?: any;
  enum?: any[];
  pattern?: string;
  minimum?: number;
  maximum?: number;
  minLength?: number;
  maxLength?: number;
}

/**
 * MCP Tool Info
 */
export interface MCPToolInfo {
  name: string;
  description: string;
  serverId: string;
  category?: string;
  tags?: string[];
  parameters?: Record<string, MCPToolParameterInfo>;
  examples?: Array<{
    description: string;
    parameters: Record<string, any>;
    expectedResult?: any;
  }>;
  rateLimit?: {
    requests: number;
    window: number;
  };
  timeout?: number;
  retryPolicy?: {
    maxRetries: number;
    backoffMultiplier: number;
    initialDelay: number;
  };
}

/**
 * MCP Tool Result
 */
export interface MCPToolResult {
  success: boolean;
  data?: any;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  metadata?: {
    executionTime: number;
    serverId: string;
    toolName: string;
    timestamp: number;
    retryCount?: number;
  };
}

/**
 * MCP Server Configuration
 */
export interface MCPServerConfig {
  id: string;
  name: string;
  type: string;
  enabled: boolean;
  priority: number;
  endpoint?: string;
  apiKey?: string;
  timeout?: number;
  retryPolicy?: {
    maxRetries: number;
    backoffMultiplier: number;
    initialDelay: number;
  };
  rateLimit?: {
    requests: number;
    window: number;
  };
  healthCheck?: {
    enabled: boolean;
    interval: number;
    timeout: number;
    retries: number;
  };
  authentication?: {
    type: 'none' | 'api-key' | 'oauth' | 'basic';
    credentials?: Record<string, string>;
  };
  customConfig?: Record<string, any>;
}

/**
 * MCP Server Status
 */
export interface MCPServerStatus {
  id: string;
  status: 'online' | 'offline' | 'degraded' | 'initializing' | 'error';
  health: {
    status: 'healthy' | 'degraded' | 'unhealthy';
    lastCheck: number;
    responseTime: number;
    errorCount: number;
    uptime: number;
  };
  toolCount: number;
  lastActivity: number;
  errorMessage?: string;
}

/**
 * Tool Usage Event
 */
export interface ToolUsageEvent {
  id: string;
  agentId: string;
  toolName: string;
  serverId: string;
  parameters: Record<string, any>;
  success: boolean;
  result?: any;
  error?: string;
  duration: number;
  timestamp: number;
  conversationId?: string;
  retryCount?: number;
}

/**
 * Agent Tool Assignment
 */
export interface AgentToolAssignment {
  agentId: string;
  toolName: string;
  serverId: string;
  priority: number;
  conditions?: {
    keywords?: string[];
    patterns?: string[];
    context?: string[];
  };
}

/**
 * Tool Conflict Resolution Strategy
 */
export type ToolConflictStrategy = 
  | 'priority' // Use tool from server with highest priority
  | 'round-robin' // Rotate between servers
  | 'load-balance' // Use server with lowest load
  | 'failover' // Use primary, fallback to secondary
  | 'best-match'; // Use server that best matches the query

/**
 * Tool Routing Rule
 */
export interface ToolRoutingRule {
  id: string;
  name: string;
  description: string;
  conditions: {
    toolNames?: string[];
    agentTypes?: string[];
    keywords?: string[];
    patterns?: RegExp[];
  };
  action: {
    strategy: ToolConflictStrategy;
    preferredServers?: string[];
    fallbackServers?: string[];
  };
  priority: number;
  enabled: boolean;
}

/**
 * MCP Registry Configuration
 */
export interface MCPRegistryConfig {
  servers: MCPServerConfig[];
  toolRouting: {
    defaultStrategy: ToolConflictStrategy;
    rules: ToolRoutingRule[];
  };
  monitoring: {
    enabled: boolean;
    metricsRetention: number; // in milliseconds
    healthCheckInterval: number;
  };
  performance: {
    maxConcurrentCalls: number;
    defaultTimeout: number;
    cacheEnabled: boolean;
    cacheTTL: number;
  };
}

/**
 * Agent Memory Entry
 */
export interface AgentMemoryEntry {
  id: string;
  agentId: string;
  conversationId: string;
  type: 'fact' | 'preference' | 'context' | 'tool-result';
  content: any;
  importance: number; // 0-1 scale
  timestamp: number;
  expiresAt?: number;
  tags?: string[];
}

/**
 * Agent Collaboration Request
 */
export interface AgentCollaborationRequest {
  id: string;
  requestingAgentId: string;
  targetAgentId: string;
  task: string;
  context: Record<string, any>;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  timeout?: number;
  expectedResponse?: 'data' | 'action' | 'analysis';
}

/**
 * Agent Collaboration Response
 */
export interface AgentCollaborationResponse {
  requestId: string;
  respondingAgentId: string;
  success: boolean;
  data?: any;
  error?: string;
  metadata?: {
    processingTime: number;
    toolsUsed: string[];
    confidence: number;
  };
}
