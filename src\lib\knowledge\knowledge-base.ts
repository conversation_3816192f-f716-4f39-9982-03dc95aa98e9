/**
 * Knowledge Base
 *
 * This module provides functionality to manage a knowledge base using Pinecone and web scraping.
 */

import { PlaywrightScraper, ScrapingOptions, ScrapingResult } from '../scraping/playwright-scraper';
import { PineconeService, DocumentWithMetadata } from './pinecone-service';
import { CompanyInfo } from '../../types';

/**
 * Knowledge Base Service class
 */
export class KnowledgeBaseService {
  private pineconeService: PineconeService;
  private scraper: PlaywrightScraper;

  /**
   * Create a new KnowledgeBaseService
   */
  constructor(pineconeService: PineconeService) {
    this.pineconeService = pineconeService;
    this.scraper = new PlaywrightScraper();
  }

  /**
   * Scrape a website and add it to the knowledge base
   */
  async scrapeAndUpsert(url: string, options?: ScrapingOptions): Promise<number> {
    try {
      console.log(`Scraping website: ${url}`);

      // Scrape the website
      const scrapingResults = await this.scraper.scrapeWebsite(url, options);

      // Convert scraping results to documents
      const documents = this.convertScrapingResultsToDocuments(scrapingResults);

      // Upsert documents to Pinecone
      await this.pineconeService.upsertDocuments(documents);

      return documents.length;
    } catch (error) {
      console.error('Error scraping and upserting website:', error);
      throw error;
    }
  }

  /**
   * Convert scraping results to documents
   */
  private convertScrapingResultsToDocuments(results: ScrapingResult[]): DocumentWithMetadata[] {
    const documents: DocumentWithMetadata[] = [];

    for (const result of results) {
      if (!result.content || result.error) {
        continue;
      }

      // Split content into chunks
      const chunks = this.pineconeService.splitTextIntoChunks(result.content);

      // Create a document for each chunk
      chunks.forEach((chunk, index) => {
        documents.push({
          pageContent: chunk,
          metadata: {
            source: 'web_scraping',
            title: result.title,
            url: result.url,
            timestamp: result.timestamp.toISOString(),
            type: 'website',
            chunkIndex: index,
            totalChunks: chunks.length
          }
        });
      });
    }

    return documents;
  }

  /**
   * Add company information to the knowledge base
   */
  async addCompanyInfo(companyInfo: CompanyInfo): Promise<void> {
    try {
      // Create a structured document from company info
      const document = this.createCompanyInfoDocument(companyInfo);

      // Upsert to Pinecone
      await this.pineconeService.upsertDocuments([document]);

      console.log('Added company information to knowledge base');
    } catch (error) {
      console.error('Error adding company information:', error);
      throw error;
    }
  }

  /**
   * Create a document from company information
   */
  private createCompanyInfoDocument(info: CompanyInfo): DocumentWithMetadata {
    // Build a structured text representation of the company info
    let content = '';

    if (info.name) {
      content += `Company Name: ${info.name}\n\n`;
    }

    if (info.foundedYear) {
      content += `Founded: ${info.foundedYear}\n\n`;
    }

    if (info.description) {
      content += `Description: ${info.description}\n\n`;
    }

    if (info.industry) {
      content += `Industry: ${info.industry}\n\n`;
    }

    if (info.size) {
      content += `Company Size: ${info.size}\n\n`;
    }

    if (info.website) {
      content += `Website: ${info.website}\n\n`;
    }

    if (info.email) {
      content += `Email: ${info.email}\n\n`;
    }

    if (info.phone) {
      content += `Phone: ${info.phone}\n\n`;
    }

    if (info.address) {
      content += `Address: ${info.address}\n\n`;
    }

    if (info.products) {
      content += `Products/Services: ${info.products}\n\n`;
    }

    if (info.targetAudience) {
      content += `Target Audience: ${info.targetAudience}\n\n`;
    }

    if (info.uniqueSellingProposition) {
      content += `Unique Selling Proposition: ${info.uniqueSellingProposition}\n\n`;
    }

    if (info.coreValues) {
      content += `Core Values: ${info.coreValues}\n\n`;
    }

    if (info.missionStatement) {
      content += `Mission Statement: ${info.missionStatement}\n\n`;
    }

    if (info.visionStatement) {
      content += `Vision Statement: ${info.visionStatement}\n\n`;
    }

    if (info.socialMedia) {
      if (info.socialMedia.linkedin) {
        content += `LinkedIn: ${info.socialMedia.linkedin}\n\n`;
      }
      if (info.socialMedia.twitter) {
        content += `Twitter: ${info.socialMedia.twitter}\n\n`;
      }
    }

    if (info.achievements) {
      content += `Key Achievements: ${info.achievements}\n\n`;
    }

    if (info.faq) {
      content += `FAQ: ${info.faq}\n\n`;
    }

    return {
      pageContent: content,
      metadata: {
        source: 'company_info',
        title: info.name || 'Company Information',
        type: 'company_profile',
        timestamp: new Date().toISOString()
      }
    };
  }

  /**
   * Search the knowledge base
   */
  async search(query: string, k: number = 5): Promise<string> {
    try {
      console.log(`KnowledgeBase searching for: "${query}" with k=${k}`);

      // Search for similar documents
      const results = await this.pineconeService.similaritySearch(query, k);

      console.log(`Found ${results.length} results`);

      // If no results, check if we have any documents at all
      if (results.length === 0) {
        // For debugging, try to get any documents
        try {
          const anyResults = await this.pineconeService.similaritySearch("", 1);
          console.log(`Debug search found ${anyResults.length} results`);

          if (anyResults.length > 0) {
            console.log(`Sample document: ${JSON.stringify(anyResults[0].metadata)}`);
          } else {
            console.log("No documents found in the knowledge base at all");
          }
        } catch (e) {
          console.error("Error in debug search:", e);
        }
      }

      // Format the results
      let formattedResults = '';

      results.forEach((doc, index) => {
        // Add source information
        formattedResults += `Source ${index + 1}: ${doc.metadata.title || 'Unknown'}\n`;

        // Add URL if available
        if (doc.metadata.url) {
          formattedResults += `URL: ${doc.metadata.url}\n`;
        }

        // Add source type if available
        if (doc.metadata.source) {
          formattedResults += `Type: ${doc.metadata.source}\n`;
        }

        // Add content with better formatting
        formattedResults += `Content: ${doc.pageContent.trim()}\n\n`;

        // Add similarity score if available
        if (doc.metadata.score) {
          formattedResults += `Relevance: ${(doc.metadata.score * 100).toFixed(1)}%\n`;
        }

        formattedResults += '\n';
      });

      return formattedResults;
    } catch (error) {
      console.error('Error searching knowledge base:', error);
      throw error;
    }
  }

  /**
   * Clear the knowledge base
   */
  async clearKnowledgeBase(): Promise<void> {
    try {
      await this.pineconeService.deleteNamespace();
      console.log('Knowledge base cleared');
    } catch (error) {
      console.error('Error clearing knowledge base:', error);
      throw error;
    }
  }

  /**
   * Initialize the knowledge base
   */
  async initialize(): Promise<void> {
    try {
      await this.pineconeService.createIndexIfNotExists();
      console.log('Knowledge base initialized');
    } catch (error) {
      console.error('Error initializing knowledge base:', error);
      throw error;
    }
  }
}
