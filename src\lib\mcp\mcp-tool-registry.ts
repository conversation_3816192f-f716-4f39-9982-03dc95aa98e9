/**
 * MCP Tool Registry
 *
 * This module provides a registry for MCP tools.
 * It manages the available tools and routes tool calls to the appropriate MCP server.
 */

import { Context7Adapter } from './context7-adapter';
import { MCPServerAdapter } from './mcp-server-adapter';

/**
 * MCP Tool interface
 */
export interface MCPTool {
  name: string;
  description: string;
  serverId: string;
}

/**
 * MCP Tool Parameter Info
 */
export interface MCPToolParameterInfo {
  type: string;
  description: string;
  required?: boolean;
}

/**
 * Interface for MCP Tool information
 */
export interface MCPToolInfo {
  name: string;
  description: string;
  serverId: string;
  parameters?: Record<string, MCPToolParameterInfo>;
}

/**
 * Tool Usage Event interface
 */
export interface ToolUsageEvent {
  agentId: string;
  toolName: string;
  serverId: string;
  parameters: Record<string, any>;
  success: boolean;
  result?: string;
  error?: string;
  duration: number;
  timestamp: number;
}

/**
 * Interface for tool usage event
 */
export interface ToolUsageEvent {
  agentId: string;
  toolName: string;
  serverId: string;
  parameters: Record<string, any>;
  success: boolean;
  result?: string;
  error?: string;
  duration: number;
  timestamp: number;
}

/**
 * MCP Tool Registry class
 */
export class MCPToolRegistry {
  private static instance: MCPToolRegistry;
  private initialized: boolean = false;
  private adapters: Map<string, MCPServerAdapter> = new Map();
  private tools: MCPTool[] = [];
  private toolInfoMap: Map<string, MCPToolInfo> = new Map();
  private toolUsageListeners: Array<() => void> = [];
  private toolUsageEvents: ToolUsageEvent[] = [];
  private toolUsageStats: Map<string, { totalCalls: number; successfulCalls: number; failedCalls: number; totalDuration: number }> = new Map();

  /**
   * Get the singleton instance of the MCP Tool Registry
   */
  public static getInstance(): MCPToolRegistry {
    if (!MCPToolRegistry.instance) {
      MCPToolRegistry.instance = new MCPToolRegistry();
    }
    return MCPToolRegistry.instance;
  }

  /**
   * Initialize the tool registry
   */
  public async initialize(serverIds: string[] = []): Promise<void> {
    if (this.initialized && serverIds.every(id => this.adapters.has(id))) {
      console.log('MCP Tool Registry already initialized with these servers');
      return;
    }

    try {
      console.log(`Initializing MCP Tool Registry with servers: ${serverIds.join(', ')}`);

      // Create adapters for each server ID
      for (const serverId of serverIds) {
        if (!this.adapters.has(serverId)) {
          await this.createAdapter(serverId);
        }
      }

      // Collect tools from all adapters
      this.collectTools();

      this.initialized = true;
      console.log(`MCP Tool Registry initialized with ${this.tools.length} tools`);
    } catch (error) {
      console.error('Error initializing MCP Tool Registry:', error);
      throw error;
    }
  }

  /**
   * Create an adapter for the specified server ID
   */
  private async createAdapter(serverId: string): Promise<void> {
    try {
      let adapter: MCPServerAdapter;

      switch (serverId) {
        case 'context7':
          adapter = new Context7Adapter();
          break;
        // Add cases for other server types here
        default:
          console.warn(`Unknown server ID: ${serverId}, skipping`);
          return;
      }

      // Initialize the adapter
      await adapter.initialize();

      // Add the adapter to the map
      this.adapters.set(serverId, adapter);

      console.log(`Adapter created for server ID: ${serverId}`);
    } catch (error) {
      console.error(`Error creating adapter for server ID: ${serverId}:`, error);
      throw error;
    }
  }

  /**
   * Collect tools from all adapters
   */
  private collectTools(): void {
    this.tools = [];
    this.toolInfoMap.clear();

    // Collect tools from all adapters
    for (const adapter of this.adapters.values()) {
      const adapterTools = adapter.getTools();
      this.tools.push(...adapterTools);

      // Collect tool info for each tool
      for (const tool of adapterTools) {
        const toolInfo = adapter.getToolInfo(tool.name);
        if (toolInfo) {
          this.toolInfoMap.set(tool.name, toolInfo);
        }
      }
    }

    console.log(`Collected ${this.tools.length} tools from ${this.adapters.size} adapters`);
  }

  /**
   * Get all tools
   */
  public getAllTools(): MCPTool[] {
    return [...this.tools];
  }

  /**
   * Get tool information
   */
  public getToolInfo(toolName: string): MCPToolInfo | undefined {
    return this.toolInfoMap.get(toolName);
  }

  /**
   * Call a tool by name
   */
  public async callTool(toolName: string, parameters: Record<string, any>, agentId: string = 'unknown'): Promise<any> {
    if (!this.initialized) {
      await this.initialize();
    }

    // Get the tool info
    const toolInfo = this.getToolInfo(toolName);
    if (!toolInfo) {
      throw new Error(`Tool not found: ${toolName}`);
    }

    // Get the adapter for the tool's server
    const adapter = this.adapters.get(toolInfo.serverId);
    if (!adapter) {
      throw new Error(`Adapter not found for server ID: ${toolInfo.serverId}`);
    }

    // Record the start time
    const startTime = Date.now();
    let success = false;
    let result: any;
    let error: string | undefined;

    try {
      // Call the tool
      result = await adapter.callTool(toolName, parameters);
      success = true;
      return result;
    } catch (err) {
      error = err instanceof Error ? err.message : String(err);
      throw err;
    } finally {
      // Record the end time
      const endTime = Date.now();
      const duration = endTime - startTime;

      // Create a tool usage event
      const event: ToolUsageEvent = {
        agentId,
        toolName,
        serverId: toolInfo.serverId,
        parameters,
        success,
        result: success ? JSON.stringify(result) : undefined,
        error,
        duration,
        timestamp: endTime,
      };

      // Add the event to the list
      this.toolUsageEvents.push(event);

      // Update the stats
      this.updateToolUsageStats(agentId, toolName, success, duration);

      // Notify listeners
      this.notifyToolUsageListeners();
    }
  }

  /**
   * Update tool usage stats
   */
  private updateToolUsageStats(agentId: string, toolName: string, success: boolean, duration: number): void {
    const key = `${agentId}:${toolName}`;
    const stats = this.toolUsageStats.get(key) || { totalCalls: 0, successfulCalls: 0, failedCalls: 0, totalDuration: 0 };

    stats.totalCalls++;
    if (success) {
      stats.successfulCalls++;
    } else {
      stats.failedCalls++;
    }
    stats.totalDuration += duration;

    this.toolUsageStats.set(key, stats);
  }

  /**
   * Add a tool usage listener
   */
  public addToolUsageListener(listener: () => void): void {
    this.toolUsageListeners.push(listener);
  }

  /**
   * Remove a tool usage listener
   */
  public removeToolUsageListener(listener: () => void): void {
    const index = this.toolUsageListeners.indexOf(listener);
    if (index !== -1) {
      this.toolUsageListeners.splice(index, 1);
    }
  }

  /**
   * Notify tool usage listeners
   */
  private notifyToolUsageListeners(): void {
    for (const listener of this.toolUsageListeners) {
      try {
        listener();
      } catch (error) {
        console.error('Error in tool usage listener:', error);
      }
    }
  }

  /**
   * Get tool usage events
   */
  public getToolUsageEvents(): ToolUsageEvent[] {
    return [...this.toolUsageEvents];
  }

  /**
   * Get tool usage stats
   */
  public getToolUsageStats(): Map<string, { totalCalls: number; successfulCalls: number; failedCalls: number; totalDuration: number; averageDuration: number }> {
    const result = new Map<string, { totalCalls: number; successfulCalls: number; failedCalls: number; totalDuration: number; averageDuration: number }>();

    for (const [key, stats] of this.toolUsageStats.entries()) {
      result.set(key, {
        ...stats,
        averageDuration: stats.totalCalls > 0 ? stats.totalDuration / stats.totalCalls : 0,
      });
    }

    return result;
  }

  /**
   * Clear tool usage events and stats
   */
  public clearToolUsage(): void {
    this.toolUsageEvents = [];
    this.toolUsageStats.clear();
    this.notifyToolUsageListeners();
  }
}
