/**
 * MCP Tool Registry
 *
 * This module provides a registry for MCP tools.
 * It manages the available tools and routes tool calls to the appropriate MCP server.
 */

import { Context7Adapter } from './context7-adapter';
import { MCPServerAdapter } from './mcp-server-adapter';
import { MCPServerDiscovery, MCPServerFactory } from './server-discovery';
import { ToolRoutingRule, ToolConflictStrategy } from './types';

/**
 * MCP Tool interface
 */
export interface MCPTool {
  name: string;
  description: string;
  serverId: string;
}

/**
 * MCP Tool Parameter Info
 */
export interface MCPToolParameterInfo {
  type: string;
  description: string;
  required?: boolean;
}

/**
 * Interface for MCP Tool information
 */
export interface MCPToolInfo {
  name: string;
  description: string;
  serverId: string;
  parameters?: Record<string, MCPToolParameterInfo>;
}

/**
 * Tool Usage Event interface
 */
export interface ToolUsageEvent {
  agentId: string;
  toolName: string;
  serverId: string;
  parameters: Record<string, any>;
  success: boolean;
  result?: string;
  error?: string;
  duration: number;
  timestamp: number;
}

/**
 * Interface for tool usage event
 */
export interface ToolUsageEvent {
  agentId: string;
  toolName: string;
  serverId: string;
  parameters: Record<string, any>;
  success: boolean;
  result?: string;
  error?: string;
  duration: number;
  timestamp: number;
}

/**
 * Enhanced MCP Tool Registry class with intelligent routing and conflict resolution
 */
export class MCPToolRegistry {
  private static instance: MCPToolRegistry;
  private initialized: boolean = false;
  private adapters: Map<string, MCPServerAdapter> = new Map();
  private tools: MCPTool[] = [];
  private toolInfoMap: Map<string, MCPToolInfo> = new Map();
  private toolUsageListeners: Array<() => void> = [];
  private toolUsageEvents: ToolUsageEvent[] = [];
  private toolUsageStats: Map<string, { totalCalls: number; successfulCalls: number; failedCalls: number; totalDuration: number }> = new Map();
  private toolRoutingRules: ToolRoutingRule[] = [];
  private toolConflicts: Map<string, string[]> = new Map(); // toolName -> serverIds
  private serverPriorities: Map<string, number> = new Map();
  private loadBalancer: Map<string, number> = new Map(); // serverId -> current load

  /**
   * Get the singleton instance of the MCP Tool Registry
   */
  public static getInstance(): MCPToolRegistry {
    if (!MCPToolRegistry.instance) {
      MCPToolRegistry.instance = new MCPToolRegistry();
    }
    return MCPToolRegistry.instance;
  }

  /**
   * Initialize the tool registry with dynamic server discovery
   */
  public async initialize(serverIds: string[] = []): Promise<void> {
    if (this.initialized) {
      console.log('MCP Tool Registry already initialized');
      return;
    }

    try {
      console.log(`Initializing Enhanced MCP Tool Registry`);

      // Initialize server discovery
      const discovery = MCPServerDiscovery.getInstance();
      await discovery.initialize();

      // Get discovered servers
      const discoveredServers = discovery.getDiscoveredServers();
      console.log(`Found ${discoveredServers.length} discovered servers`);

      // Create adapters for discovered servers
      for (const serverConfig of discoveredServers) {
        if (serverConfig.enabled && (serverIds.length === 0 || serverIds.includes(serverConfig.id))) {
          await this.createAdapterFromConfig(serverConfig);
        }
      }

      // Create adapters for explicitly requested servers not found in discovery
      for (const serverId of serverIds) {
        if (!this.adapters.has(serverId)) {
          await this.createAdapter(serverId);
        }
      }

      // Collect tools from all adapters
      this.collectTools();

      // Detect and resolve tool conflicts
      this.detectToolConflicts();

      // Set up default routing rules
      this.setupDefaultRoutingRules();

      // Listen for server discovery events
      this.setupDiscoveryListeners(discovery);

      this.initialized = true;
      console.log(`Enhanced MCP Tool Registry initialized with ${this.tools.length} tools from ${this.adapters.size} servers`);
    } catch (error) {
      console.error('Error initializing Enhanced MCP Tool Registry:', error);
      throw error;
    }
  }

  /**
   * Create an adapter from server configuration
   */
  private async createAdapterFromConfig(serverConfig: any): Promise<void> {
    try {
      const adapter = MCPServerFactory.createAdapter(serverConfig.type);
      if (!adapter) {
        console.warn(`No adapter available for server type: ${serverConfig.type}`);
        return;
      }

      // Initialize the adapter with configuration
      await adapter.initialize(serverConfig);

      // Add the adapter to the map
      this.adapters.set(serverConfig.id, adapter);
      this.serverPriorities.set(serverConfig.id, serverConfig.priority || 1);
      this.loadBalancer.set(serverConfig.id, 0);

      console.log(`Adapter created for server: ${serverConfig.name} (${serverConfig.id})`);
    } catch (error) {
      console.error(`Error creating adapter for server ${serverConfig.id}:`, error);
      throw error;
    }
  }

  /**
   * Create an adapter for the specified server ID (legacy method)
   */
  private async createAdapter(serverId: string): Promise<void> {
    try {
      let adapter: MCPServerAdapter;

      switch (serverId) {
        case 'context7':
          adapter = new Context7Adapter();
          break;
        // Add cases for other server types here
        default:
          console.warn(`Unknown server ID: ${serverId}, skipping`);
          return;
      }

      // Create a basic config for legacy initialization
      const config = {
        id: serverId,
        name: `${serverId} Server`,
        type: serverId,
        enabled: true,
        priority: 1,
        timeout: 30000,
        customConfig: {}
      };

      // Initialize the adapter
      await adapter.initialize(config);

      // Add the adapter to the map
      this.adapters.set(serverId, adapter);
      this.serverPriorities.set(serverId, 1);
      this.loadBalancer.set(serverId, 0);

      console.log(`Legacy adapter created for server ID: ${serverId}`);
    } catch (error) {
      console.error(`Error creating legacy adapter for server ID: ${serverId}:`, error);
      throw error;
    }
  }

  /**
   * Collect tools from all adapters
   */
  private collectTools(): void {
    this.tools = [];
    this.toolInfoMap.clear();

    // Collect tools from all adapters
    for (const adapter of this.adapters.values()) {
      const adapterTools = adapter.getTools();
      this.tools.push(...adapterTools);

      // Collect tool info for each tool
      for (const tool of adapterTools) {
        const toolInfo = adapter.getToolInfo(tool.name);
        if (toolInfo) {
          this.toolInfoMap.set(tool.name, toolInfo);
        }
      }
    }

    console.log(`Collected ${this.tools.length} tools from ${this.adapters.size} adapters`);
  }

  /**
   * Get all tools
   */
  public getAllTools(): MCPTool[] {
    return [...this.tools];
  }

  /**
   * Get tool information
   */
  public getToolInfo(toolName: string): MCPToolInfo | undefined {
    return this.toolInfoMap.get(toolName);
  }

  /**
   * Call a tool by name with intelligent routing and conflict resolution
   */
  public async callTool(toolName: string, parameters: Record<string, any>, agentId: string = 'unknown', context?: {
    conversationId?: string;
    query?: string;
    agentType?: string;
  }): Promise<any> {
    if (!this.initialized) {
      await this.initialize();
    }

    // Find the best server for this tool call
    const selectedServerId = await this.selectBestServer(toolName, parameters, agentId, context);
    if (!selectedServerId) {
      throw new Error(`No available server found for tool: ${toolName}`);
    }

    // Get the adapter for the selected server
    const adapter = this.adapters.get(selectedServerId);
    if (!adapter) {
      throw new Error(`Adapter not found for server ID: ${selectedServerId}`);
    }

    // Record the start time
    const startTime = Date.now();
    let success = false;
    let result: any;
    let error: string | undefined;
    let retryCount = 0;
    const maxRetries = 3;

    while (retryCount <= maxRetries) {
      try {
        // Update load balancer
        this.incrementServerLoad(selectedServerId);

        // Call the tool with enhanced context
        const toolResult = await adapter.callTool(toolName, parameters, {
          agentId,
          conversationId: context?.conversationId,
          timeout: 30000
        });

        result = toolResult.success ? toolResult.data : toolResult;
        success = true;
        break;
      } catch (err) {
        error = err instanceof Error ? err.message : String(err);
        retryCount++;

        // Try failover if available and this isn't the last retry
        if (retryCount <= maxRetries) {
          const failoverServerId = await this.getFailoverServer(toolName, selectedServerId);
          if (failoverServerId) {
            console.log(`Retrying tool ${toolName} on failover server: ${failoverServerId}`);
            const failoverAdapter = this.adapters.get(failoverServerId);
            if (failoverAdapter) {
              try {
                const toolResult = await failoverAdapter.callTool(toolName, parameters, {
                  agentId,
                  conversationId: context?.conversationId,
                  timeout: 30000
                });
                result = toolResult.success ? toolResult.data : toolResult;
                success = true;
                break;
              } catch (failoverErr) {
                console.error(`Failover attempt failed:`, failoverErr);
              }
            }
          }
        }

        if (retryCount > maxRetries) {
          throw err;
        }

        // Exponential backoff
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, retryCount) * 1000));
      } finally {
        // Decrement load balancer
        this.decrementServerLoad(selectedServerId);
      }
    }

    // Record the end time
    const endTime = Date.now();
    const duration = endTime - startTime;

    // Create a tool usage event
    const event: ToolUsageEvent = {
      id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      agentId,
      toolName,
      serverId: selectedServerId,
      parameters,
      success,
      result: success ? JSON.stringify(result) : undefined,
      error,
      duration,
      timestamp: endTime,
      conversationId: context?.conversationId,
      retryCount
    };

    // Add the event to the list
    this.toolUsageEvents.push(event);

    // Update the stats
    this.updateToolUsageStats(agentId, toolName, success, duration);

    // Notify listeners
    this.notifyToolUsageListeners();

    if (success) {
      return result;
    } else {
      throw new Error(error || 'Tool execution failed');
    }
  }

  /**
   * Update tool usage stats
   */
  private updateToolUsageStats(agentId: string, toolName: string, success: boolean, duration: number): void {
    const key = `${agentId}:${toolName}`;
    const stats = this.toolUsageStats.get(key) || { totalCalls: 0, successfulCalls: 0, failedCalls: 0, totalDuration: 0 };

    stats.totalCalls++;
    if (success) {
      stats.successfulCalls++;
    } else {
      stats.failedCalls++;
    }
    stats.totalDuration += duration;

    this.toolUsageStats.set(key, stats);
  }

  /**
   * Add a tool usage listener
   */
  public addToolUsageListener(listener: () => void): void {
    this.toolUsageListeners.push(listener);
  }

  /**
   * Remove a tool usage listener
   */
  public removeToolUsageListener(listener: () => void): void {
    const index = this.toolUsageListeners.indexOf(listener);
    if (index !== -1) {
      this.toolUsageListeners.splice(index, 1);
    }
  }

  /**
   * Notify tool usage listeners
   */
  private notifyToolUsageListeners(): void {
    for (const listener of this.toolUsageListeners) {
      try {
        listener();
      } catch (error) {
        console.error('Error in tool usage listener:', error);
      }
    }
  }

  /**
   * Get tool usage events
   */
  public getToolUsageEvents(): ToolUsageEvent[] {
    return [...this.toolUsageEvents];
  }

  /**
   * Get tool usage stats
   */
  public getToolUsageStats(): Map<string, { totalCalls: number; successfulCalls: number; failedCalls: number; totalDuration: number; averageDuration: number }> {
    const result = new Map<string, { totalCalls: number; successfulCalls: number; failedCalls: number; totalDuration: number; averageDuration: number }>();

    for (const [key, stats] of this.toolUsageStats.entries()) {
      result.set(key, {
        ...stats,
        averageDuration: stats.totalCalls > 0 ? stats.totalDuration / stats.totalCalls : 0,
      });
    }

    return result;
  }

  /**
   * Clear tool usage events and stats
   */
  public clearToolUsage(): void {
    this.toolUsageEvents = [];
    this.toolUsageStats.clear();
    this.notifyToolUsageListeners();
  }

  // Enhanced Methods for Intelligent Routing and Conflict Resolution

  /**
   * Select the best server for a tool call based on routing rules and load balancing
   */
  private async selectBestServer(toolName: string, parameters: Record<string, any>, agentId: string, context?: {
    conversationId?: string;
    query?: string;
    agentType?: string;
  }): Promise<string | null> {
    // Get all servers that provide this tool
    const availableServers = this.getServersForTool(toolName);
    if (availableServers.length === 0) {
      return null;
    }

    // If only one server, return it
    if (availableServers.length === 1) {
      return availableServers[0];
    }

    // Apply routing rules
    const routedServer = this.applyRoutingRules(toolName, availableServers, agentId, context);
    if (routedServer) {
      return routedServer;
    }

    // Fall back to default strategy (priority + load balancing)
    return this.selectServerByStrategy('priority', availableServers);
  }

  /**
   * Get all servers that provide a specific tool
   */
  private getServersForTool(toolName: string): string[] {
    const servers: string[] = [];
    for (const [serverId, adapter] of this.adapters) {
      if (adapter.getTools().some(tool => tool.name === toolName)) {
        servers.push(serverId);
      }
    }
    return servers;
  }

  /**
   * Apply routing rules to select the best server
   */
  private applyRoutingRules(toolName: string, availableServers: string[], agentId: string, context?: {
    conversationId?: string;
    query?: string;
    agentType?: string;
  }): string | null {
    // Sort rules by priority
    const sortedRules = this.toolRoutingRules.sort((a, b) => b.priority - a.priority);

    for (const rule of sortedRules) {
      if (!rule.enabled) continue;

      // Check if rule conditions match
      if (this.ruleMatches(rule, toolName, agentId, context)) {
        // Apply the rule's strategy
        const selectedServer = this.selectServerByStrategy(rule.action.strategy, availableServers, rule.action);
        if (selectedServer) {
          console.log(`Applied routing rule "${rule.name}" for tool ${toolName}, selected server: ${selectedServer}`);
          return selectedServer;
        }
      }
    }

    return null;
  }

  /**
   * Check if a routing rule matches the current context
   */
  private ruleMatches(rule: ToolRoutingRule, toolName: string, agentId: string, context?: {
    conversationId?: string;
    query?: string;
    agentType?: string;
  }): boolean {
    const conditions = rule.conditions;

    // Check tool names
    if (conditions.toolNames && !conditions.toolNames.includes(toolName)) {
      return false;
    }

    // Check agent types
    if (conditions.agentTypes && context?.agentType && !conditions.agentTypes.includes(context.agentType)) {
      return false;
    }

    // Check keywords in query
    if (conditions.keywords && context?.query) {
      const queryLower = context.query.toLowerCase();
      const hasKeyword = conditions.keywords.some(keyword => queryLower.includes(keyword.toLowerCase()));
      if (!hasKeyword) {
        return false;
      }
    }

    // Check patterns
    if (conditions.patterns && context?.query) {
      const hasPattern = conditions.patterns.some(pattern => pattern.test(context.query!));
      if (!hasPattern) {
        return false;
      }
    }

    return true;
  }

  /**
   * Select server based on strategy
   */
  private selectServerByStrategy(strategy: ToolConflictStrategy, availableServers: string[], action?: {
    preferredServers?: string[];
    fallbackServers?: string[];
  }): string | null {
    switch (strategy) {
      case 'priority':
        return this.selectByPriority(availableServers);
      case 'load-balance':
        return this.selectByLoadBalance(availableServers);
      case 'round-robin':
        return this.selectByRoundRobin(availableServers);
      case 'failover':
        return this.selectByFailover(availableServers, action);
      case 'best-match':
        return this.selectByBestMatch(availableServers);
      default:
        return this.selectByPriority(availableServers);
    }
  }

  /**
   * Select server by priority
   */
  private selectByPriority(availableServers: string[]): string | null {
    let bestServer = null;
    let highestPriority = -1;

    for (const serverId of availableServers) {
      const priority = this.serverPriorities.get(serverId) || 0;
      if (priority > highestPriority) {
        highestPriority = priority;
        bestServer = serverId;
      }
    }

    return bestServer;
  }

  /**
   * Select server by load balancing
   */
  private selectByLoadBalance(availableServers: string[]): string | null {
    let bestServer = null;
    let lowestLoad = Infinity;

    for (const serverId of availableServers) {
      const load = this.loadBalancer.get(serverId) || 0;
      if (load < lowestLoad) {
        lowestLoad = load;
        bestServer = serverId;
      }
    }

    return bestServer;
  }

  /**
   * Select server by round-robin
   */
  private selectByRoundRobin(availableServers: string[]): string | null {
    // Simple round-robin based on timestamp
    const index = Date.now() % availableServers.length;
    return availableServers[index];
  }

  /**
   * Select server by failover strategy
   */
  private selectByFailover(availableServers: string[], action?: {
    preferredServers?: string[];
    fallbackServers?: string[];
  }): string | null {
    // Try preferred servers first
    if (action?.preferredServers) {
      for (const serverId of action.preferredServers) {
        if (availableServers.includes(serverId)) {
          return serverId;
        }
      }
    }

    // Fall back to priority selection
    return this.selectByPriority(availableServers);
  }

  /**
   * Select server by best match (placeholder for more sophisticated matching)
   */
  private selectByBestMatch(availableServers: string[]): string | null {
    // For now, use priority as the best match criteria
    return this.selectByPriority(availableServers);
  }

  /**
   * Get failover server for a tool
   */
  private async getFailoverServer(toolName: string, currentServerId: string): Promise<string | null> {
    const availableServers = this.getServersForTool(toolName).filter(id => id !== currentServerId);
    return this.selectByPriority(availableServers);
  }

  /**
   * Increment server load
   */
  private incrementServerLoad(serverId: string): void {
    const currentLoad = this.loadBalancer.get(serverId) || 0;
    this.loadBalancer.set(serverId, currentLoad + 1);
  }

  /**
   * Decrement server load
   */
  private decrementServerLoad(serverId: string): void {
    const currentLoad = this.loadBalancer.get(serverId) || 0;
    this.loadBalancer.set(serverId, Math.max(0, currentLoad - 1));
  }

  /**
   * Detect tool conflicts between servers
   */
  private detectToolConflicts(): void {
    const toolServerMap = new Map<string, string[]>();

    // Build map of tools to servers
    for (const [serverId, adapter] of this.adapters) {
      const tools = adapter.getTools();
      for (const tool of tools) {
        if (!toolServerMap.has(tool.name)) {
          toolServerMap.set(tool.name, []);
        }
        toolServerMap.get(tool.name)!.push(serverId);
      }
    }

    // Identify conflicts (tools provided by multiple servers)
    for (const [toolName, serverIds] of toolServerMap) {
      if (serverIds.length > 1) {
        this.toolConflicts.set(toolName, serverIds);
        console.log(`Tool conflict detected: ${toolName} is provided by servers: ${serverIds.join(', ')}`);
      }
    }
  }

  /**
   * Set up default routing rules
   */
  private setupDefaultRoutingRules(): void {
    this.toolRoutingRules = [
      {
        id: 'code-analysis-context7',
        name: 'Route code analysis to Context7',
        description: 'Route code analysis tools to Context7 server',
        conditions: {
          toolNames: ['context7_search_code', 'context7_analyze_code', 'context7_find_similar'],
        },
        action: {
          strategy: 'priority',
          preferredServers: ['context7']
        },
        priority: 10,
        enabled: true
      },
      {
        id: 'documentation-context7',
        name: 'Route documentation to Context7',
        description: 'Route documentation tools to Context7 server',
        conditions: {
          keywords: ['documentation', 'docs', 'api reference', 'guide']
        },
        action: {
          strategy: 'priority',
          preferredServers: ['context7']
        },
        priority: 8,
        enabled: true
      },
      {
        id: 'load-balance-general',
        name: 'Load balance general tools',
        description: 'Load balance tools that are available on multiple servers',
        conditions: {},
        action: {
          strategy: 'load-balance'
        },
        priority: 1,
        enabled: true
      }
    ];

    console.log(`Set up ${this.toolRoutingRules.length} default routing rules`);
  }

  /**
   * Set up discovery event listeners
   */
  private setupDiscoveryListeners(discovery: MCPServerDiscovery): void {
    discovery.on('server-discovered', async (serverConfig: any) => {
      console.log(`New server discovered: ${serverConfig.name}`);
      if (serverConfig.enabled) {
        await this.createAdapterFromConfig(serverConfig);
        this.collectTools();
        this.detectToolConflicts();
      }
    });

    discovery.on('server-removed', (serverConfig: any) => {
      console.log(`Server removed: ${serverConfig.name}`);
      const adapter = this.adapters.get(serverConfig.id);
      if (adapter) {
        adapter.shutdown();
        this.adapters.delete(serverConfig.id);
        this.serverPriorities.delete(serverConfig.id);
        this.loadBalancer.delete(serverConfig.id);
        this.collectTools();
        this.detectToolConflicts();
      }
    });

    discovery.on('server-updated', async (serverConfig: any) => {
      console.log(`Server updated: ${serverConfig.name}`);
      const adapter = this.adapters.get(serverConfig.id);
      if (adapter) {
        await adapter.updateConfig(serverConfig);
        this.serverPriorities.set(serverConfig.id, serverConfig.priority || 1);
      }
    });
  }

  /**
   * Get tool conflicts
   */
  public getToolConflicts(): Map<string, string[]> {
    return new Map(this.toolConflicts);
  }

  /**
   * Get routing rules
   */
  public getRoutingRules(): ToolRoutingRule[] {
    return [...this.toolRoutingRules];
  }

  /**
   * Add routing rule
   */
  public addRoutingRule(rule: ToolRoutingRule): void {
    this.toolRoutingRules.push(rule);
    this.toolRoutingRules.sort((a, b) => b.priority - a.priority);
  }

  /**
   * Remove routing rule
   */
  public removeRoutingRule(ruleId: string): void {
    this.toolRoutingRules = this.toolRoutingRules.filter(rule => rule.id !== ruleId);
  }

  /**
   * Get server load information
   */
  public getServerLoads(): Map<string, number> {
    return new Map(this.loadBalancer);
  }
}
