/**
 * Tool Monitor
 * 
 * This module provides a monitor for tracking tool usage.
 * This is a placeholder implementation until the full MCP implementation is complete.
 */

import { ToolUsageEvent } from './mcp-tool-registry';

/**
 * Interface for tool usage statistics
 */
interface ToolUsageStats {
  totalCalls: number;
  successfulCalls: number;
  failedCalls: number;
  averageDuration: number;
  lastUsed: number | null;
}

/**
 * Tool Monitor class
 */
export class ToolMonitor {
  private static instance: ToolMonitor;
  private events: ToolUsageEvent[] = [];
  private stats: Map<string, ToolUsageStats> = new Map();
  private listeners: (() => void)[] = [];
  
  /**
   * Get the singleton instance of the Tool Monitor
   */
  public static getInstance(): ToolMonitor {
    if (!ToolMonitor.instance) {
      ToolMonitor.instance = new ToolMonitor();
    }
    return ToolMonitor.instance;
  }
  
  /**
   * Log a tool usage event
   */
  public logToolUsage(event: ToolUsageEvent): void {
    // Add timestamp if not provided
    if (!event.timestamp) {
      event.timestamp = Date.now();
    }
    
    // Add to events
    this.events.push(event);
    
    // Update stats
    this.updateStats(event);
    
    // Notify listeners
    this.notifyListeners();
  }
  
  /**
   * Update tool usage statistics
   */
  private updateStats(event: ToolUsageEvent): void {
    // Create a key for the stats map
    const key = `${event.agentId}:${event.toolName}`;
    
    // Get existing stats or create new ones
    const stats = this.stats.get(key) || {
      totalCalls: 0,
      successfulCalls: 0,
      failedCalls: 0,
      averageDuration: 0,
      lastUsed: null
    };
    
    // Update stats
    stats.totalCalls++;
    if (event.success) {
      stats.successfulCalls++;
    } else {
      stats.failedCalls++;
    }
    
    // Update average duration
    stats.averageDuration = (stats.averageDuration * (stats.totalCalls - 1) + event.duration) / stats.totalCalls;
    
    // Update last used
    stats.lastUsed = event.timestamp;
    
    // Save updated stats
    this.stats.set(key, stats);
  }
  
  /**
   * Get all tool usage events
   */
  public getEvents(): ToolUsageEvent[] {
    return [...this.events];
  }
  
  /**
   * Get all tool usage statistics
   */
  public getStats(): Map<string, ToolUsageStats> {
    return new Map(this.stats);
  }
  
  /**
   * Clear all events and stats
   */
  public clear(): void {
    this.events = [];
    this.stats.clear();
    this.notifyListeners();
  }
  
  /**
   * Add a listener for tool usage events
   */
  public addListener(listener: () => void): void {
    this.listeners.push(listener);
  }
  
  /**
   * Remove a listener for tool usage events
   */
  public removeListener(listener: () => void): void {
    const index = this.listeners.indexOf(listener);
    if (index !== -1) {
      this.listeners.splice(index, 1);
    }
  }
  
  /**
   * Notify all listeners
   */
  private notifyListeners(): void {
    for (const listener of this.listeners) {
      listener();
    }
  }
}
